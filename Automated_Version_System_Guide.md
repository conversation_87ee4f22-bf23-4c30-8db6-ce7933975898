# Claim<PERSON>utter Automated Version System Guide

## Overview

The ClaimCutter extension uses an automated version management system that increments version numbers across multiple files and displays the current version in the UI for easy verification during development.

## Version Format

**Format**: `2.1.XX` where XX increments for each change
- **Major.Minor.Patch** semantic versioning
- **Current Range**: 2.1.00 → 2.1.XX
- **Increment**: +1 for each build/change

## Automated Version Management

### 1. Version Bump Command
```bash
npm run version:bump
```

**What it does**:
- Increments version number by 1 (e.g., 2.1.04 → 2.1.05)
- Updates version in multiple files automatically
- Shows which files were updated
- Provides next steps for building

### 2. Files Updated Automatically

#### Primary Version File:
- `src/utils/version.ts` - Main version export

#### Inline Version Updates:
- `src/background.ts` - Background script version logging
- `src/scripts/apply-cuts.ts` - Content script version logging

### 3. Build Process
```bash
npm run build
```

**Complete Workflow**:
```bash
# 1. Bump version
npm run version:bump

# 2. Build extension
npm run build

# 3. Reload extension in Chrome
# 4. Test new version
```

## Version Display System

### 1. UI Version Display
- **Location**: Extension popup (top-right corner)
- **Format**: "v2.1.05"
- **Purpose**: Verify extension updates are loaded correctly

### 2. Console Version Logging
```javascript
// Background script
console.log('🚀 ClaimCutter Background Script v2.1.05 - Enhanced Versioning');

// Content scripts
console.log('🚀 ClaimCutter Apply Cuts v2.1.05 - Enhanced Versioning');
```

### 3. Version Verification
- Check popup UI for version number
- Check browser console for version logs
- Ensure version matches across all components

## Implementation Details

### 1. Version Bump Script
**Location**: `scripts/bump-version.js`

**Functionality**:
- Reads current version from `src/utils/version.ts`
- Increments patch number by 1
- Updates version in all target files
- Uses regex patterns to find and replace version strings

### 2. Version File Structure
```typescript
// src/utils/version.ts
export const VERSION = '2.1.05';
```

### 3. Inline Version Patterns
```typescript
// Pattern in background.ts and apply-cuts.ts
console.log('🚀 ClaimCutter [Script Name] v2.1.05 - Enhanced Versioning');
```

## Usage Instructions for Developers

### For New Features/Fixes:
1. **Make code changes**
2. **Bump version**: `npm run version:bump`
3. **Build extension**: `npm run build`
4. **Reload in Chrome**: Extensions → Developer mode → Reload
5. **Verify version**: Check popup UI shows new version
6. **Test functionality**

### For Testing Iterations:
```bash
# Quick iteration cycle
npm run version:bump && npm run build
```

### For Version Verification:
1. **Open extension popup** - Check version in top-right
2. **Open browser console** - Look for version logs
3. **Check multiple tabs** - Ensure consistent version across contexts

## Version History Tracking

### Recent Versions:
- **v2.1.05**: Character-by-character input implementation
- **v2.1.04**: Aggressive end time setting approach
- **v2.1.03**: Workflow restart logic for subsequent cuts
- **v2.1.02**: Enhanced timing and error handling
- **v2.1.01**: Basic cut automation implementation

### Version Increment Triggers:
- ✅ **Code changes** (any functional modification)
- ✅ **Bug fixes** (any attempt to resolve issues)
- ✅ **Logic improvements** (workflow enhancements)
- ✅ **Testing iterations** (during development cycles)
- ❌ **Documentation only** (no functional changes)

## Troubleshooting

### Version Not Updating in UI:
1. **Check build output** - Ensure build completed successfully
2. **Reload extension** - Use Chrome Extensions page reload button
3. **Hard refresh** - Close and reopen extension popup
4. **Check console** - Look for version logs in background script

### Version Mismatch Between Components:
1. **Run version bump again** - Ensures all files updated
2. **Check file contents** - Manually verify version strings
3. **Rebuild extension** - `npm run build`
4. **Clear browser cache** - Hard refresh all tabs

### Build Errors After Version Bump:
1. **Check syntax** - Ensure version bump didn't break code
2. **Verify file paths** - Ensure all target files exist
3. **Check TypeScript** - Run `npm run build` for detailed errors

## Advanced Usage

### Manual Version Setting:
```bash
# Edit src/utils/version.ts manually
export const VERSION = '2.1.XX';

# Then run build
npm run build
```

### Version Verification Script:
```bash
# Check current version across all files
grep -r "v2\.1\." src/ --include="*.ts"
```

### Custom Version Patterns:
The bump script looks for these patterns:
```typescript
// Pattern 1: Version export
export const VERSION = '2.1.XX';

// Pattern 2: Console log with version
console.log('🚀 ClaimCutter [Name] v2.1.XX - Enhanced Versioning');
```

## Integration with Development Workflow

### 1. Feature Development:
```bash
# Start development
git checkout -b feature-name

# Make changes, test, iterate
npm run version:bump && npm run build

# Commit with version
git add .
git commit -m "feat: description (v2.1.XX)"
```

### 2. Bug Fixing:
```bash
# Each fix attempt
npm run version:bump && npm run build

# Test fix
# If not working, repeat cycle
```

### 3. Release Preparation:
```bash
# Final version bump
npm run version:bump

# Build production version
npm run build

# Test thoroughly
# Commit and tag release
```

## Benefits of Automated Versioning

1. **Easy Verification**: Always know which version is loaded
2. **Development Tracking**: Each change gets a unique version
3. **Debugging Aid**: Version logs help identify which code is running
4. **Consistency**: All components show same version number
5. **Automation**: No manual version management needed

## File Locations

- **Version Bump Script**: `scripts/bump-version.js`
- **Main Version File**: `src/utils/version.ts`
- **Background Script**: `src/background.ts` (line ~1)
- **Apply Cuts Script**: `src/scripts/apply-cuts.ts` (line ~1)
- **Popup UI**: `src/ui/popup.ts` (version display logic)

The automated version system ensures consistent version tracking across all extension components and provides easy verification during development and testing.
