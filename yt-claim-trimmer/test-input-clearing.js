// Test script for YouTube Studio timestamp input clearing
// Run this in the browser devtools console on the YouTube Studio editor page

console.log('🧪 Testing YouTube Studio input clearing methods...');

// Helper function to add delays
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Test function to try different input clearing approaches
async function testInputClearing() {
  console.log('Looking for timestamp inputs...');
  
  // Find all timestamp inputs
  const inputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  console.log(`Found ${inputs.length} timestamp inputs`);
  
  if (inputs.length === 0) {
    console.error('❌ No timestamp inputs found. Make sure you are on the YouTube Studio editor page.');
    return;
  }
  
  // Test with the first visible input
  const testInput = Array.from(inputs).find(input => input.offsetParent !== null);
  if (!testInput) {
    console.error('❌ No visible timestamp inputs found.');
    return;
  }
  
  console.log('📍 Testing with input:', {
    value: testInput.value,
    placeholder: testInput.placeholder,
    ariaLabel: testInput.getAttribute('aria-label')
  });
  
  // Store original value
  const originalValue = testInput.value;
  console.log(`Original value: "${originalValue}"`);
  
  // Test Method 1: Basic select and replace
  console.log('\n🔬 Method 1: Basic select and replace');
  try {
    testInput.focus();
    await delay(200);
    testInput.select();
    await delay(200);
    testInput.value = '00:01:30';
    testInput.dispatchEvent(new Event('input', { bubbles: true }));
    testInput.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(500);
    console.log(`✅ Method 1 result: "${testInput.value}"`);
  } catch (error) {
    console.error('❌ Method 1 failed:', error);
  }
  
  // Reset
  testInput.value = originalValue;
  await delay(500);
  
  // Test Method 2: Aggressive clearing with backspace simulation
  console.log('\n🔬 Method 2: Aggressive clearing with backspace');
  try {
    testInput.focus();
    await delay(200);
    
    // Select all
    testInput.select();
    await delay(200);
    
    // Simulate backspace
    testInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Backspace', bubbles: true }));
    testInput.dispatchEvent(new KeyboardEvent('keyup', { key: 'Backspace', bubbles: true }));
    await delay(200);
    
    // Clear to empty
    testInput.value = '';
    testInput.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(200);
    
    // Set new value
    testInput.value = '00:02:15';
    testInput.dispatchEvent(new Event('input', { bubbles: true }));
    testInput.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(500);
    console.log(`✅ Method 2 result: "${testInput.value}"`);
  } catch (error) {
    console.error('❌ Method 2 failed:', error);
  }
  
  // Reset
  testInput.value = originalValue;
  await delay(500);
  
  // Test Method 3: Your discovery - type digit first, then replace
  console.log('\n🔬 Method 3: Type digit first, then replace (your discovery)');
  try {
    testInput.focus();
    await delay(200);
    
    // Type a single digit first
    testInput.value = '0';
    testInput.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(200);
    
    // Now select and replace
    testInput.select();
    await delay(200);
    testInput.value = '00:03:45';
    testInput.dispatchEvent(new Event('input', { bubbles: true }));
    testInput.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(500);
    console.log(`✅ Method 3 result: "${testInput.value}"`);
  } catch (error) {
    console.error('❌ Method 3 failed:', error);
  }
  
  // Reset
  testInput.value = originalValue;
  await delay(500);
  
  // Test Method 4: Character-by-character deletion
  console.log('\n🔬 Method 4: Character-by-character deletion');
  try {
    testInput.focus();
    await delay(200);
    
    // Select all and delete character by character
    testInput.select();
    await delay(200);
    
    // Simulate multiple backspaces
    for (let i = 0; i < 10; i++) {
      testInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Backspace', bubbles: true }));
      await delay(50);
    }
    
    // Ensure it's empty
    testInput.value = '';
    testInput.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(200);
    
    // Type new value character by character
    const newValue = '00:04:20';
    for (let char of newValue) {
      testInput.value += char;
      testInput.dispatchEvent(new Event('input', { bubbles: true }));
      await delay(100);
    }
    
    testInput.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(500);
    console.log(`✅ Method 4 result: "${testInput.value}"`);
  } catch (error) {
    console.error('❌ Method 4 failed:', error);
  }
  
  // Reset to original
  testInput.value = originalValue;
  testInput.dispatchEvent(new Event('input', { bubbles: true }));
  
  console.log('\n🎯 Test completed! Check which method worked best.');
  console.log('💡 The method that successfully changed the input value should be used in the extension.');
}

// Test function for timeline input specifically
async function testTimelineInput() {
  console.log('\n🎯 Testing timeline input specifically...');
  
  const timelineInput = document.querySelector('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  if (!timelineInput) {
    console.error('❌ Timeline input not found');
    return;
  }
  
  console.log('📍 Found timeline input:', {
    value: timelineInput.value,
    placeholder: timelineInput.placeholder
  });
  
  const originalValue = timelineInput.value;
  
  // Test the digit-first method on timeline
  console.log('🔬 Testing digit-first method on timeline input...');
  try {
    timelineInput.focus();
    await delay(200);
    
    // Type digit first
    timelineInput.value = '0';
    timelineInput.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(200);
    
    // Replace with target time
    timelineInput.select();
    timelineInput.value = '00:01:00';
    timelineInput.dispatchEvent(new Event('input', { bubbles: true }));
    timelineInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    // Simulate Enter key
    timelineInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
    
    await delay(1000);
    console.log(`✅ Timeline test result: "${timelineInput.value}"`);
    console.log('🎬 Check if the timeline actually moved to 1:00');
    
  } catch (error) {
    console.error('❌ Timeline test failed:', error);
  }
  
  // Reset
  setTimeout(() => {
    timelineInput.value = originalValue;
    timelineInput.dispatchEvent(new Event('input', { bubbles: true }));
  }, 2000);
}

// Run the tests
console.log('🚀 Starting input clearing tests...');
console.log('📋 This will test different methods to clear and set YouTube Studio timestamp inputs');
console.log('⏱️  Each test will take a few seconds...');

testInputClearing().then(() => {
  console.log('\n🎯 Basic tests completed. Running timeline-specific test...');
  return testTimelineInput();
}).then(() => {
  console.log('\n✅ All tests completed!');
  console.log('📊 Review the results above to see which method worked best.');
}).catch(error => {
  console.error('❌ Test failed:', error);
});
