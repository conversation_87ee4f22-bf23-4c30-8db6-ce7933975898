<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ClaimCutter</title>
  <style>
    body {
      width: 360px;
      min-height: 240px;
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #ffffff;
      color: #333333;
      overflow-x: hidden;
    }

    .container {
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e5e7eb;
    }

    .logo {
      font-size: 20px;
      font-weight: bold;
      color: #ff0000;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .logo-icon {
      font-size: 24px;
    }

    .subtitle {
      font-size: 13px;
      color: #6b7280;
      line-height: 1.4;
    }

    .page-status {
      margin-bottom: 20px;
      padding: 16px 12px;
      border-radius: 8px;
      font-size: 13px;
      text-align: center;
      line-height: 1.4;
    }

    .page-status.valid {
      background: #ecfdf5;
      color: #065f46;
      border: 1px solid #a7f3d0;
    }

    .page-status.invalid {
      background: #fef2f2;
      color: #991b1b;
      border: 1px solid #fecaca;
    }

    .page-status.checking {
      background: #fffbeb;
      color: #92400e;
      border: 1px solid #fed7aa;
    }

    .controls {
      margin-bottom: 20px;
    }

    .button {
      width: 100%;
      padding: 12px 16px;
      margin: 8px 0;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      position: relative;
    }

    .button-primary {
      background: #dc2626;
      color: white;
    }

    .button-primary:hover:not(:disabled) {
      background: #b91c1c;
    }

    .button-secondary {
      background: #f3f4f6;
      color: #374151;
      border: 1px solid #d1d5db;
    }

    .button-secondary:hover:not(:disabled) {
      background: #e5e7eb;
    }

    .button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .button-icon {
      font-size: 16px;
    }

    .progress-section {
      margin: 16px 0;
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background: #e5e7eb;
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 8px;
    }

    .progress-fill {
      height: 100%;
      background: #3b82f6;
      border-radius: 3px;
      transition: width 0.3s ease;
      width: 0%;
    }

    .progress-text {
      font-size: 12px;
      color: #6b7280;
      text-align: center;
    }

    .status {
      margin: 12px 0;
      padding: 12px;
      border-radius: 8px;
      font-size: 13px;
      line-height: 1.4;
    }

    .status-info {
      background: #eff6ff;
      color: #1e40af;
      border: 1px solid #bfdbfe;
    }

    .status-error {
      background: #fef2f2;
      color: #991b1b;
      border: 1px solid #fecaca;
    }

    .status-success {
      background: #ecfdf5;
      color: #065f46;
      border: 1px solid #a7f3d0;
    }

    .status-warning {
      background: #fffbeb;
      color: #92400e;
      border: 1px solid #fed7aa;
    }

    .error-actions {
      margin-top: 12px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .error-action {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      background: #f3f4f6;
      color: #374151;
      border: 1px solid #d1d5db;
    }

    .error-action.primary {
      background: #3b82f6;
      color: white;
      border-color: #3b82f6;
    }

    .error-action:hover {
      opacity: 0.8;
    }

    .stats {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 12px;
      margin: 12px 0;
      font-size: 12px;
    }

    .stats-row {
      display: flex;
      justify-content: space-between;
      margin: 4px 0;
    }

    .stats-label {
      color: #6b7280;
    }

    .stats-value {
      font-weight: 500;
      color: #374151;
    }

    .footer {
      margin-top: 20px;
      padding-top: 16px;
      border-top: 1px solid #e5e7eb;
      text-align: center;
      font-size: 11px;
      color: #9ca3af;
    }

    .footer a {
      color: #dc2626;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }

    .hidden {
      display: none !important;
    }

    .spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #e5e7eb;
      border-radius: 50%;
      border-top-color: #3b82f6;
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .fade-in {
      animation: fadeIn 0.3s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Turbo Mode Indicator */
    .turbo-indicator {
      background: linear-gradient(135deg, #fbbf24, #f59e0b);
      border: 1px solid #d97706;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
      text-align: center;
      color: #92400e;
      font-size: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .turbo-icon {
      font-size: 16px;
      margin-right: 6px;
    }

    .turbo-text {
      font-weight: 600;
      display: block;
      margin-bottom: 2px;
    }

    .turbo-description {
      font-size: 11px;
      opacity: 0.8;
    }

    /* Collection Results Styles */
    .collection-results {
      max-width: 100%;
    }

    .collection-results h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #065f46;
    }

    .collection-summary {
      background: #f8f9fa;
      border: 1px solid #e8eaed;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 15px;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      border-bottom: 1px solid #e8eaed;
    }

    .summary-item:last-child {
      border-bottom: none;
    }

    .summary-label {
      font-weight: 500;
      color: #5f6368;
      font-size: 13px;
    }

    .summary-value {
      font-weight: 600;
      color: #1a73e8;
      font-size: 14px;
    }

    .merged-note .summary-label {
      color: #ea8600;
    }

    .merged-note .summary-value {
      color: #ea8600;
    }

    .timestamp-list {
      margin: 12px 0;
      max-height: 200px;
      overflow-y: auto;
    }

    .timestamp-item {
      padding: 8px 12px;
      margin: 6px 0;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      font-size: 12px;
      position: relative;
    }

    .timestamp-item.merged {
      background: #fef3c7;
      border-color: #f59e0b;
    }

    .timestamp-number {
      font-weight: 600;
      color: #374151;
      margin-right: 8px;
    }

    .timestamp-range {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 500;
      color: #1f2937;
    }

    .timestamp-duration {
      color: #6b7280;
      font-size: 11px;
      margin-left: 8px;
    }

    .merge-badge {
      background: #f59e0b;
      color: white;
      font-size: 9px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 3px;
      margin-left: 8px;
      text-transform: uppercase;
    }

    .merge-info {
      margin-top: 6px;
      padding: 4px 8px;
      background: #fffbeb;
      border: 1px solid #fed7aa;
      border-radius: 4px;
      font-size: 10px;
      color: #92400e;
      line-height: 1.3;
    }

    .collection-actions {
      margin-top: 16px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .apply-cuts-button {
      flex: 1;
      padding: 10px 16px;
      background: #dc2626;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .apply-cuts-button:hover {
      background: #b91c1c;
    }

    .clear-results-button {
      padding: 10px 16px;
      background: #f3f4f6;
      color: #374151;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .clear-results-button:hover {
      background: #e5e7eb;
    }

    .collection-errors {
      margin-top: 12px;
      padding: 8px 12px;
      background: #fef2f2;
      border: 1px solid #fecaca;
      border-radius: 6px;
    }

    .collection-errors h4 {
      margin: 0 0 6px 0;
      font-size: 12px;
      color: #991b1b;
    }

    .error-item {
      font-size: 11px;
      color: #dc2626;
      margin: 2px 0;
    }

    /* NEW: Batch Mode Styles */
    .batch-discovery-results {
      max-width: 100%;
    }

    .batch-discovery-results h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #1e40af;
    }

    .discovery-summary {
      background: #eff6ff;
      border: 1px solid #bfdbfe;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 15px;
    }

    .video-list {
      margin: 12px 0;
      max-height: 200px;
      overflow-y: auto;
    }

    .batch-video-item {
      padding: 8px 12px;
      margin: 4px 0;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .video-number {
      font-weight: 600;
      color: #374151;
      min-width: 20px;
    }

    .video-title {
      flex: 1;
      color: #1f2937;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .video-status {
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 3px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .video-status.pending {
      background: #fef3c7;
      color: #92400e;
    }

    .batch-actions {
      margin-top: 16px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .start-batch-button {
      flex: 1;
      padding: 10px 16px;
      background: #1e40af;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .start-batch-button:hover {
      background: #1d4ed8;
    }

    .clear-discovery-button {
      padding: 10px 16px;
      background: #f3f4f6;
      color: #374151;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .clear-discovery-button:hover {
      background: #e5e7eb;
    }

    /* Batch Results Styles */
    .batch-results {
      max-width: 100%;
    }

    .batch-results h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #065f46;
    }

    .batch-summary {
      background: #ecfdf5;
      border: 1px solid #a7f3d0;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 15px;
    }

    .video-results {
      margin: 12px 0;
      max-height: 200px;
      overflow-y: auto;
    }

    .batch-video-result {
      padding: 8px 12px;
      margin: 4px 0;
      border-radius: 6px;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .batch-video-result.completed {
      background: #ecfdf5;
      border: 1px solid #a7f3d0;
    }

    .batch-video-result.failed {
      background: #fef2f2;
      border: 1px solid #fecaca;
    }

    .batch-video-result.skipped {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
    }

    .video-status-icon {
      font-size: 14px;
      min-width: 20px;
    }

    .video-claims {
      font-size: 10px;
      color: #6b7280;
      margin-left: auto;
    }

    .video-errors {
      font-size: 10px;
      color: #dc2626;
      margin-top: 4px;
      padding: 2px 6px;
      background: #fef2f2;
      border-radius: 3px;
    }

    .batch-final-actions {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }

    /* NEW: Batch control buttons */
    .batch-controls {
      display: flex;
      gap: 8px;
      margin: 12px 0;
      justify-content: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .batch-control-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 80px;
    }

    .batch-control-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .batch-control-btn:active {
      transform: translateY(0);
    }

    .pause-btn {
      background: #ffc107;
      color: #212529;
    }

    .pause-btn:hover {
      background: #ffb300;
    }

    .continue-btn {
      background: #28a745;
      color: white;
    }

    .continue-btn:hover {
      background: #218838;
    }

    .stop-btn {
      background: #dc3545;
      color: white;
    }

    .stop-btn:hover {
      background: #c82333;
    }

    .reset-batch-button {
      background: #6c757d;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .reset-batch-button:hover {
      background: #5a6268;
      transform: translateY(-1px);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">
        <span class="logo-icon">✂️</span>
        ClaimCutter
      </div>
      <div class="subtitle">YouTube Copyright Claim Trimmer</div>
    </div>

    <!-- Page Status Indicator -->
    <div id="page-status" class="page-status checking">
      <!-- Content will be dynamically populated -->
    </div>

    <!-- Turbo Mode Indicator -->
    <div id="turbo-indicator" class="turbo-indicator hidden">
      <span class="turbo-icon">🚀</span>
      <span class="turbo-text">Turbo Mode Active</span>
      <span class="turbo-description">Auto-navigation and cut application enabled</span>
    </div>

    <!-- Main Controls -->
    <div id="main-controls" class="controls">
      <button id="start-btn" class="button button-primary">
        <span class="button-icon">▶️</span>
        <span id="start-btn-text">Start Trimming</span>
      </button>

      <button id="dry-run-btn" class="button button-secondary">
        <span class="button-icon">🔍</span>
        Dry Run (Preview Only)
      </button>
    </div>

    <!-- Progress Section -->
    <div id="progress-section" class="progress-section hidden">
      <div class="progress-bar">
        <div id="progress-fill" class="progress-fill"></div>
      </div>
      <div id="progress-text" class="progress-text">Initializing...</div>
    </div>

    <!-- Status Messages -->
    <div id="status" class="status hidden"></div>

    <!-- Statistics -->
    <div id="stats" class="stats hidden">
      <div class="stats-row">
        <span class="stats-label">Claims Found:</span>
        <span id="stats-claims" class="stats-value">-</span>
      </div>
      <div class="stats-row">
        <span class="stats-label">Cuts Applied:</span>
        <span id="stats-cuts" class="stats-value">-</span>
      </div>
      <div class="stats-row">
        <span class="stats-label">Time Saved:</span>
        <span id="stats-time" class="stats-value">-</span>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <div style="margin-bottom: 8px;">
        <span id="version-display" style="font-weight: 600; color: #dc2626;">Loading...</span>
      </div>
      <a href="#" id="help-link">Help & Instructions</a> |
      <a href="#" id="github-link">GitHub</a> |
      <a href="#" id="settings-link">Settings</a> •
      <a href="#" id="test-debugger-link" style="color: #3b82f6;">Test Debugger</a>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
