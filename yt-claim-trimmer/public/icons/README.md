# Extension Icons

This directory should contain the following icon files:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Icon Requirements

- **Format**: PNG with transparency
- **Design**: Simple, recognizable symbol related to video editing/trimming
- **Colors**: Red (#FF0000) primary color to match YouTube branding
- **Style**: Clean, modern, readable at small sizes

## Temporary Solution

For development, you can create simple colored squares or use online icon generators:

1. Visit https://favicon.io/favicon-generator/
2. Create icons with "CC" text (ClaimCutter)
3. Download and rename to the required sizes
4. Place in this directory

## Production Icons

For production release, consider hiring a designer or using professional icon tools to create polished icons that represent the extension's purpose clearly.
