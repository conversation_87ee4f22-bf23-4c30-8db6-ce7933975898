import { defineConfig } from 'vite';
import { resolve } from 'path';
import { viteStaticCopy } from 'vite-plugin-static-copy';

export default defineConfig({
  build: {
    target: 'chrome115',
    outDir: 'dist',
    rollupOptions: {
      input: {
        background: resolve(__dirname, 'src/background.ts'),
        popup: resolve(__dirname, 'src/ui/popup.ts'),
        'collect-timestamps': resolve(__dirname, 'src/scripts/collect-timestamps.ts'),
        'apply-cuts': resolve(__dirname, 'src/scripts/apply-cuts.ts'),
        // NEW: Batch mode scripts
        'channel-content-detector': resolve(__dirname, 'src/scripts/channel-content-detector.ts'),
        'batch-processor': resolve(__dirname, 'src/scripts/batch-processor.ts'),
        'channel-content-detector-content': resolve(__dirname, 'src/scripts/channel-content-detector-content.ts'),
        'batch-processor-content': resolve(__dirname, 'src/scripts/batch-processor-content.ts'),
      },
      output: {
        entryFileNames: '[name].js',
        chunkFileNames: '[name].js',
        assetFileNames: '[name].[ext]',
        format: 'es', // Use ES format but ensure no imports
      },
      external: [], // Don't externalize any modules
    },
    minify: false, // Keep readable for debugging
    sourcemap: true,
  },
  plugins: [
    viteStaticCopy({
      targets: [
        {
          src: 'src/manifest.json',
          dest: '.',
        },
        {
          src: 'src/icons/**/*',
          dest: 'icons',
        },
        {
          src: 'public/**/*',
          dest: '.',
        },
      ],
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
  },
});
