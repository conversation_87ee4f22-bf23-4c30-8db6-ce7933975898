<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClaimCutter Extension Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: #f0f0f0;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-section {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
        }
        .mock-youtube-studio {
            background: #fff;
            border: 2px solid #ff0000;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .mock-copyright-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .mock-copyright-table th,
        .mock-copyright-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .mock-copyright-table th {
            background: #f5f5f5;
        }
        .mock-claim-row {
            background: #fff;
        }
        .mock-claim-row:hover {
            background: #f9f9f9;
        }
        .mock-details-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 ClaimCutter Extension Test Page</h1>
        <p>Use this page to test the ClaimCutter Chrome extension functionality.</p>
    </div>

    <div class="test-section">
        <h3>📋 Pre-Test Checklist</h3>
        <div id="checklist">
            <div class="status info">
                <strong>Step 1:</strong> Build the extension with <code>pnpm run build</code>
            </div>
            <div class="status info">
                <strong>Step 2:</strong> Load extension in Chrome from <code>chrome://extensions/</code>
            </div>
            <div class="status info">
                <strong>Step 3:</strong> Enable "Developer mode" and click "Load unpacked"
            </div>
            <div class="status info">
                <strong>Step 4:</strong> Select the <code>yt-claim-trimmer/dist</code> folder
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>🔧 Extension Detection Test</h3>
        <button class="test-button" onclick="testExtensionPresence()">Test Extension Presence</button>
        <div id="extension-status" class="status info">
            Click the button above to check if the extension is loaded.
        </div>
    </div>

    <div class="test-section">
        <h3>🎯 Popup Interface Test</h3>
        <p>Click the ClaimCutter extension icon in your Chrome toolbar to test the popup interface.</p>
        <div class="status info">
            <strong>Expected:</strong> Popup should open showing page validation status and disabled buttons (since this isn't YouTube Studio).
        </div>
    </div>

    <div class="test-section">
        <h3>📺 YouTube Studio Simulation</h3>
        <p>This section simulates YouTube Studio elements for testing selector functionality:</p>
        
        <div class="mock-youtube-studio">
            <h4>Mock YouTube Studio Copyright Page</h4>
            
            <!-- Mock copyright claims table -->
            <table class="mock-copyright-table" role="table">
                <thead>
                    <tr role="row">
                        <th>Claim Type</th>
                        <th>Content</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="mock-claim-row" role="row">
                        <td>Audio</td>
                        <td>Content found in 1:23 - 2:45</td>
                        <td>
                            <button class="mock-details-button" aria-label="See details">See details</button>
                        </td>
                    </tr>
                    <tr class="mock-claim-row" role="row">
                        <td>Audio</td>
                        <td>Content found in 3:15 - 4:30</td>
                        <td>
                            <button class="mock-details-button" aria-label="See details">See details</button>
                        </td>
                    </tr>
                    <tr class="mock-claim-row" role="row">
                        <td>Visual</td>
                        <td>Content found in 5:00 - 6:15</td>
                        <td>
                            <button class="mock-details-button" aria-label="See details">See details</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <button class="test-button" onclick="testSelectorMatching()">Test Selector Matching</button>
        <div id="selector-status" class="status info">
            Click to test if extension selectors can find mock elements.
        </div>
    </div>

    <div class="test-section">
        <h3>🐛 Console Debugging</h3>
        <p>Open Chrome DevTools (F12) and check the Console tab for:</p>
        <div class="code">
            [ClaimCutter] Extension loaded<br>
            [ClaimCutter] Popup script loaded<br>
            [ClaimCutter] Background service worker loaded
        </div>
        <button class="test-button" onclick="testConsoleLogging()">Generate Test Logs</button>
    </div>

    <div class="test-section">
        <h3>📝 Test Results</h3>
        <div id="test-results">
            <div class="status info">
                Test results will appear here as you run the tests above.
            </div>
        </div>
    </div>

    <script>
        function testExtensionPresence() {
            const statusDiv = document.getElementById('extension-status');
            
            // Check if Chrome extension APIs are available
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    // Try to get extension info
                    const extensionId = chrome.runtime.id;
                    if (extensionId) {
                        statusDiv.className = 'status success';
                        statusDiv.innerHTML = `
                            <strong>✅ Extension Detected!</strong><br>
                            Extension ID: ${extensionId}<br>
                            Chrome APIs are available.
                        `;
                    } else {
                        statusDiv.className = 'status error';
                        statusDiv.innerHTML = '<strong>❌ Extension not detected.</strong> Make sure it\'s loaded in chrome://extensions/';
                    }
                } catch (error) {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                }
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '<strong>❌ Chrome extension APIs not available.</strong> This page must be loaded as a Chrome extension.';
            }
        }

        function testSelectorMatching() {
            const statusDiv = document.getElementById('selector-status');
            let results = [];

            // Test table selector
            const table = document.querySelector('[role="table"]');
            results.push(`Table selector: ${table ? '✅ Found' : '❌ Not found'}`);

            // Test row selectors
            const rows = document.querySelectorAll('[role="row"]:not([role="columnheader"])');
            results.push(`Row selectors: ${rows.length > 0 ? `✅ Found ${rows.length} rows` : '❌ No rows found'}`);

            // Test button selectors
            const buttons = document.querySelectorAll('[aria-label*="See details"]');
            results.push(`Button selectors: ${buttons.length > 0 ? `✅ Found ${buttons.length} buttons` : '❌ No buttons found'}`);

            // Test timestamp parsing
            const timestampTexts = Array.from(document.querySelectorAll('.mock-claim-row td:nth-child(2)'))
                .map(td => td.textContent);
            const timestampPattern = /(\d{1,2}:\d{2}(?::\d{2})?)\s*[–—-]\s*(\d{1,2}:\d{2}(?::\d{2})?)/;
            const parsedTimestamps = timestampTexts.filter(text => timestampPattern.test(text));
            results.push(`Timestamp parsing: ${parsedTimestamps.length > 0 ? `✅ Parsed ${parsedTimestamps.length} timestamps` : '❌ No timestamps parsed'}`);

            statusDiv.className = 'status success';
            statusDiv.innerHTML = `
                <strong>Selector Test Results:</strong><br>
                ${results.join('<br>')}
            `;
        }

        function testConsoleLogging() {
            console.log('[ClaimCutter Test] Extension test page loaded');
            console.log('[ClaimCutter Test] Testing console logging functionality');
            console.warn('[ClaimCutter Test] This is a test warning');
            console.error('[ClaimCutter Test] This is a test error (not a real error)');
            
            const statusDiv = document.getElementById('test-results');
            statusDiv.innerHTML = `
                <div class="status success">
                    <strong>✅ Test logs generated!</strong><br>
                    Check the Console tab in DevTools to see the test messages.
                </div>
            `;
        }

        // Auto-run extension presence test on page load
        window.addEventListener('load', () => {
            setTimeout(testExtensionPresence, 1000);
        });
    </script>
</body>
</html>
