// Debug script to test video duration detection
// Run this in browser console on YouTube Studio copyright page

console.log('=== VIDEO DURATION DETECTION TEST ===');

function parseTimestamp(timeStr) {
  if (!timeStr) return null;
  
  const parts = timeStr.split(':').map(Number);

  if (parts.length === 2) {
    // MM:SS format
    const [minutes, seconds] = parts;
    return minutes * 60 + seconds;
  } else if (parts.length === 3) {
    // HH:MM:SS format
    const [hours, minutes, seconds] = parts;
    return hours * 3600 + minutes * 60 + seconds;
  }

  return null;
}

function formatTimestamp(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours === 0) {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}

function getVideoIdFromUrl() {
  const url = window.location.href;
  const match = url.match(/\/video\/([a-zA-Z0-9_-]+)/);
  return match ? match[1] : null;
}

function testVideoDurationDetection() {
  console.log('🔍 Testing video duration detection...');
  console.log('📍 Current URL:', window.location.href);
  console.log('🎬 Video ID:', getVideoIdFromUrl());
  
  // Try multiple selectors where YouTube Studio shows video duration
  const durationSelectors = [
    // Video details page
    '.video-duration',
    '[data-testid="video-duration"]',
    // Timeline in editor
    'input.style-scope.ytcp-media-timestamp-input[role="timer"]',
    // Video metadata areas
    '.ytcp-video-metadata-duration',
    '.duration-text',
    // Look for any element containing duration-like text
    '*[title*="duration" i]',
    '*[aria-label*="duration" i]'
  ];

  console.log('\n🔍 Testing specific selectors:');
  for (const selector of durationSelectors) {
    const elements = document.querySelectorAll(selector);
    console.log(`\n📍 Selector: ${selector}`);
    console.log(`   Found ${elements.length} elements`);
    
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      const text = element.textContent?.trim() || element.getAttribute('value') || '';
      const duration = parseTimestamp(text);
      
      console.log(`   Element ${i+1}:`, {
        text: text,
        duration: duration,
        formatted: duration ? formatTimestamp(duration) : null,
        visible: element.offsetParent !== null
      });
      
      if (duration && duration > 0) {
        console.log(`✅ FOUND VIDEO DURATION: ${text} (${duration}s) from selector: ${selector}`);
        return duration;
      }
    }
  }

  // Fallback: Look for any time-like patterns in the page
  console.log('\n🔍 Fallback: Scanning page for time patterns...');
  const pageText = document.body.textContent || '';
  const timePatterns = pageText.match(/\b\d{1,2}:\d{2}(?::\d{2})?\b/g);
  
  if (timePatterns) {
    console.log(`Found ${timePatterns.length} time patterns:`, timePatterns.slice(0, 10));
    
    // Look for the longest time pattern as it's likely the video duration
    let maxDuration = 0;
    let durationText = '';
    
    timePatterns.forEach(pattern => {
      const duration = parseTimestamp(pattern);
      if (duration && duration > maxDuration) {
        maxDuration = duration;
        durationText = pattern;
      }
    });
    
    if (maxDuration > 0) {
      console.log(`✅ ESTIMATED VIDEO DURATION: ${durationText} (${maxDuration}s)`);
      return maxDuration;
    }
  }

  console.warn('❌ Could not detect video duration');
  return null;
}

// Run the test
const detectedDuration = testVideoDurationDetection();

if (detectedDuration) {
  console.log(`\n🎯 RESULT: Video duration is ${formatTimestamp(detectedDuration)} (${detectedDuration} seconds)`);
  
  // Test validation logic
  console.log('\n🧪 Testing validation logic:');
  const maxReasonableClaim = Math.min(detectedDuration * 0.5, 30 * 60);
  console.log(`📏 Max reasonable claim duration: ${formatTimestamp(maxReasonableClaim)} (${Math.floor(maxReasonableClaim/60)}m)`);
  
  // Test some example timestamps
  const testTimestamps = [
    { start: '31:06', end: '2:32:36' },  // Your problematic case
    { start: '2:31:06', end: '2:32:36' } // Reasonable case
  ];
  
  testTimestamps.forEach((test, i) => {
    const start = parseTimestamp(test.start);
    const end = parseTimestamp(test.end);
    const duration = end - start;
    const durationMinutes = Math.floor(duration / 60);

    console.log(`\nTest ${i+1}: ${test.start} - ${test.end}`);
    console.log(`  Duration: ${formatTimestamp(duration)} (${durationMinutes}m)`);

    // New warning-based validation (only reject if exceeds video duration)
    let status = '✅ ACCEPTED';
    let warnings = [];

    if (end > detectedDuration) {
      status = '❌ REJECTED';
      warnings.push('Exceeds video duration');
    } else {
      // Check for format parsing error patterns
      if (detectedDuration >= 3600) { // Video is 1+ hours
        const startHours = Math.floor(start / 3600);
        const endHours = Math.floor(end / 3600);

        if (startHours === 0 && endHours >= 2 && duration > 60 * 60) {
          warnings.push('🚨 CAUTION: Possible format parsing error (MM:SS start, H:MM:SS end)');
        }
      }

      // Duration-based warnings
      if (duration >= 30 * 60) {
        warnings.push('🚨 CAUTION: Very long claim (30+ minutes)');
      } else if (duration >= 10 * 60) {
        warnings.push('ℹ️ INFO: Long claim (10-30 minutes)');
      }
    }

    console.log(`  Status: ${status}`);
    if (warnings.length > 0) {
      warnings.forEach(warning => console.log(`  ${warning}`));
    }
  });
} else {
  console.log('\n❌ Could not detect video duration - using fallback validation');
}

console.log('\n=== TEST COMPLETE ===');
