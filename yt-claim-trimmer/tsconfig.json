{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["chrome", "jest", "node"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "tests/**/*.ts"], "exclude": ["node_modules", "dist"]}