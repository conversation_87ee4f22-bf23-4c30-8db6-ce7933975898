// Debug script to test "Trim & cut" button detection
// Copy and paste this into the browser console on YouTube Studio editor page

console.log('🔍 Debugging "Trim & cut" button detection...');

// Method 1: Current approach from apply-cuts.ts
console.log('\n=== Method 1: Current Selector ===');
const trimAndCutButtons = document.querySelectorAll('a.style-scope.ytve-entrypoint-options-panel');
console.log(`Found ${trimAndCutButtons.length} buttons with current selector`);

let trimAndCutButton = null;
for (const button of trimAndCutButtons) {
  console.log('Button found:', {
    textContent: button.textContent?.trim(),
    className: button.className,
    visible: button.offsetParent !== null,
    href: button.href,
    id: button.id
  });

  if (button.textContent?.trim() === 'Trim & cut') {
    trimAndCutButton = button;
    console.log('✅ Found "Trim & cut" button with current method!');
    break;
  }
}

if (!trimAndCutButton) {
  console.log('❌ "Trim & cut" button not found with current method');
}

// Method 1.5: Try variations of the current selector
console.log('\n=== Method 1.5: Selector Variations ===');
const selectorVariations = [
  'a.style-scope.ytve-entrypoint-options-panel',
  'a[class*="ytve-entrypoint-options-panel"]',
  'a[class*="entrypoint-options"]',
  '.ytve-entrypoint-options-panel',
  '[class*="ytve-entrypoint-options-panel"]'
];

selectorVariations.forEach((selector, i) => {
  try {
    const elements = document.querySelectorAll(selector);
    console.log(`Variation ${i+1} (${selector}): Found ${elements.length} elements`);

    Array.from(elements).forEach((el, j) => {
      if (el.textContent && el.textContent.includes('Trim')) {
        console.log(`  Element ${j+1}:`, {
          textContent: el.textContent.trim(),
          tagName: el.tagName,
          className: el.className,
          visible: el.offsetParent !== null
        });
      }
    });
  } catch (error) {
    console.log(`Variation ${i+1} failed:`, error.message);
  }
});

// Method 2: Broader search for any element containing "Trim & cut"
console.log('\n=== Method 2: Broader Search ===');
const allElements = document.querySelectorAll('*');
const trimElements = Array.from(allElements).filter(el => 
  el.textContent && el.textContent.includes('Trim & cut')
);

console.log(`Found ${trimElements.length} elements containing "Trim & cut"`);
trimElements.forEach((el, i) => {
  console.log(`Element ${i+1}:`, {
    tagName: el.tagName,
    className: el.className,
    textContent: el.textContent?.trim(),
    visible: el.offsetParent !== null,
    clickable: el.tagName === 'A' || el.tagName === 'BUTTON' || el.getAttribute('role') === 'button'
  });
});

// Method 3: Look for anchor tags specifically
console.log('\n=== Method 3: All Anchor Tags ===');
const allAnchors = document.querySelectorAll('a');
const trimAnchors = Array.from(allAnchors).filter(a => 
  a.textContent && a.textContent.includes('Trim')
);

console.log(`Found ${trimAnchors.length} anchor tags containing "Trim"`);
trimAnchors.forEach((a, i) => {
  console.log(`Anchor ${i+1}:`, {
    textContent: a.textContent?.trim(),
    className: a.className,
    href: a.href,
    visible: a.offsetParent !== null
  });
});

// Method 4: Test clicking if found
if (trimAndCutButton) {
  console.log('\n=== Testing Click ===');
  console.log('Would click this button:', trimAndCutButton);
  console.log('Button details:', {
    textContent: trimAndCutButton.textContent?.trim(),
    className: trimAndCutButton.className,
    visible: trimAndCutButton.offsetParent !== null,
    boundingRect: trimAndCutButton.getBoundingClientRect()
  });
  
  // Uncomment the next line to actually test clicking:
  // trimAndCutButton.click();
  console.log('💡 To test clicking, uncomment the line above and run again');
} else {
  console.log('\n❌ Cannot test clicking - button not found');
}

console.log('\n🔍 Debug complete! Check the results above.');
