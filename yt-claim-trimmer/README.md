# ClaimCutter - YouTube Copyright Claim Batch Trimmer

A Chrome Extension that automates the process of trimming copyright-claimed segments from YouTube videos in batch, reducing manual editing time from 15-20 minutes to under 2 minutes.

## 🚀 Features

- **Automated Timestamp Collection**: Scrapes all copyright claim timestamps from YouTube Studio
- **Intelligent Merging**: Automatically merges overlapping timestamp ranges for optimal coverage
- **Batch Cut Application**: Applies all cuts in a single Editor session
- **Turbo Mode**: Fully automated workflow from copyright page to final save
- **User-Adjustable Settings**: Customizable merge buffer (5-30 seconds) and automation preferences
- **Persistent State**: Extension maintains data when popup is reopened
- **User Safety**: Manual Save confirmation to maintain user control (optional auto-save in turbo mode)
- **Dry Run Mode**: Preview cuts before applying them
- **Error Recovery**: Graceful handling of login timeouts and DOM changes
- **Real-time Progress**: Detailed progress tracking and status messages

## 📋 Requirements

- Google Chrome 115+ (Manifest V3 support)
- YouTube Studio access with copyright claims to process
- Developer mode enabled in Chrome extensions

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm

### Installation

```bash
# Clone the repository
git clone https://github.com/your-username/yt-claim-trimmer.git
cd yt-claim-trimmer

# Install dependencies
pnpm install

# Build the extension
pnpm run build
```

### Development Workflow

```bash
# Development build with watch mode
pnpm run dev

# Lint and format code
pnpm run lint
pnpm run format

# Run tests
pnpm run test
pnpm run test:e2e

# Build for production
pnpm run build

# Package for distribution
pnpm run package
```

## 📦 Installation (End Users)

1. Download the latest release ZIP from [GitHub Releases](https://github.com/your-username/yt-claim-trimmer/releases)
2. Extract the ZIP file to a local folder
3. Open Chrome and navigate to `chrome://extensions`
4. Enable "Developer mode" (toggle in top right)
5. Click "Load unpacked" and select the extracted folder
6. Pin the ClaimCutter icon to your toolbar

## 🎯 Usage

### Standard Mode
1. **Navigate to YouTube Studio**: Go to a video with copyright claims
2. **Open Copyright Tab**: Click on the "Copyright" tab in the left sidebar
3. **Start ClaimCutter**: Click the ClaimCutter extension icon in your toolbar
4. **Choose Mode**:
   - **Start Batch Trimming**: Process and apply cuts
   - **Dry Run**: Preview cuts without applying them
5. **Review Results**: Extension shows collected timestamps with merge information
6. **Apply Cuts**: Click "Apply Cuts in Editor" to navigate and apply cuts
7. **Manual Save**: Click the highlighted "Save" button to finalize

### Turbo Mode (Fully Automated)
1. **Enable Turbo Mode**: Click Settings and toggle "Turbo Mode" on
2. **Optional Auto-Save**: Enable "Auto-Save" for complete automation
3. **Start Turbo Trimming**: Click the "Start Turbo Trimming" button
4. **Hands-Off Operation**: Extension automatically:
   - Collects all copyright claim timestamps
   - Navigates to the editor page
   - Applies all cuts with intelligent merging
   - Clicks Save button (if auto-save enabled)
5. **Confirmation**: Manual confirmation dialog appears for final approval

## ⚠️ Important Notes

- **Manual Save Required**: The extension highlights the Save button but requires you to click it manually for safety
- **Single Video Processing**: Currently processes one video at a time
- **YouTube Studio Only**: Only works within YouTube Studio, not the main YouTube site
- **Terms of Service**: Use responsibly and in accordance with YouTube's Terms of Service

## 🏗️ Development Status

### EPIC 1: Project Setup ✅ COMPLETE
- [x] TypeScript + Vite + pnpm setup
- [x] ESLint/Prettier configuration
- [x] Manifest V3 with permissions
- [x] Basic extension structure

### EPIC 2: Timestamp Collector ✅ COMPLETE
- [x] YouTube Studio selector mapping
- [x] Timestamp collection implementation
- [x] Range merging utilities with intelligent overlap detection
- [x] Modal-based timestamp extraction
- [x] Persistent state management

### EPIC 3: Cut Applier ✅ COMPLETE
- [x] Cut application logic with timeline positioning
- [x] Character-by-character input typing
- [x] Dynamic Cut button detection
- [x] Enhanced error handling and debugging

### EPIC 4: Popup & Background ✅ COMPLETE
- [x] Enhanced popup UI with real-time progress
- [x] Background orchestration with service worker
- [x] Comprehensive error handling and recovery
- [x] Settings system with persistent storage

### EPIC 5: Advanced Features ✅ COMPLETE
- [x] Turbo Mode with full automation
- [x] User-adjustable merge buffer settings
- [x] Auto-save functionality
- [x] Professional UI with status indicators
- [x] Comprehensive debugging and error handling

### Current Status: 🎉 PRODUCTION READY
- **All core features implemented and tested**
- **Turbo mode working end-to-end**
- **Professional UI with comprehensive feedback**
- **Ready for Chrome Web Store submission**

## 🧪 Testing

The extension includes comprehensive testing:

- **Unit Tests**: Jest with Chrome API mocking
- **Integration Tests**: Playwright with real Chrome extension
- **Selector Tests**: Automated YouTube Studio DOM validation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚖️ Disclaimer

This extension is for educational and productivity purposes. Users are responsible for ensuring their use complies with YouTube's Terms of Service. The extension operates within the user's browser session and does not collect or transmit any data externally.

## 🐛 Known Issues

- YouTube Studio DOM changes may require selector updates
- Session timeouts during long operations need manual re-authentication
- Non-English Studio interfaces may affect text-based selectors

## 📞 Support

- [GitHub Issues](https://github.com/your-username/yt-claim-trimmer/issues)
- [Documentation](https://github.com/your-username/yt-claim-trimmer/wiki)
- [Discussions](https://github.com/your-username/yt-claim-trimmer/discussions)
