<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClaimCutter Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon-canvas {
            border: 1px solid #ddd;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 ClaimCutter Icon Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Generate Icons" below to create all required icon sizes</li>
                <li>Right-click each icon and "Save image as..." to download</li>
                <li>Save them in the <code>yt-claim-trimmer/src/icons/</code> folder with the exact names shown</li>
                <li>Rebuild the extension with <code>pnpm run build</code></li>
                <li>Reload the extension in Chrome</li>
            </ol>
        </div>

        <button onclick="generateAllIcons()" class="download-btn" style="font-size: 16px; padding: 12px 24px;">
            🎨 Generate All Icons
        </button>

        <div id="icons-container"></div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const center = size / 2;
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background circle
            ctx.fillStyle = '#FF0000';
            ctx.beginPath();
            ctx.arc(center, center, size * 0.45, 0, 2 * Math.PI);
            ctx.fill();
            
            // Inner white circle
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(center, center, size * 0.35, 0, 2 * Math.PI);
            ctx.fill();
            
            // Scissors - simplified for small sizes
            const scissorSize = size * 0.3;
            const scissorOffset = size * 0.1;
            
            // Left blade
            ctx.fillStyle = '#FF0000';
            ctx.fillRect(center - scissorSize, center - scissorOffset, scissorSize * 0.4, scissorSize * 0.8);
            
            // Right blade  
            ctx.fillRect(center + scissorSize * 0.6, center - scissorOffset, scissorSize * 0.4, scissorSize * 0.8);
            
            // Center pivot
            ctx.fillStyle = '#333333';
            ctx.beginPath();
            ctx.arc(center, center, size * 0.05, 0, 2 * Math.PI);
            ctx.fill();
            
            // Cut line
            ctx.strokeStyle = '#FF0000';
            ctx.lineWidth = Math.max(1, size * 0.02);
            ctx.setLineDash([size * 0.03, size * 0.02]);
            ctx.beginPath();
            ctx.moveTo(center - scissorSize * 0.8, center + scissorSize * 0.6);
            ctx.lineTo(center + scissorSize * 0.8, center + scissorSize * 0.6);
            ctx.stroke();
            
            // YouTube play triangle hint (for larger sizes)
            if (size >= 32) {
                ctx.fillStyle = '#FF0000';
                ctx.globalAlpha = 0.7;
                ctx.beginPath();
                const triangleSize = size * 0.08;
                ctx.moveTo(center - triangleSize, center + scissorSize * 0.3);
                ctx.lineTo(center - triangleSize, center + scissorSize * 0.5);
                ctx.lineTo(center + triangleSize * 0.5, center + scissorSize * 0.4);
                ctx.closePath();
                ctx.fill();
                ctx.globalAlpha = 1;
            }
        }

        function createIconCanvas(size, filename) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            canvas.className = 'icon-canvas';
            
            drawIcon(canvas, size);
            
            const container = document.createElement('div');
            container.className = 'icon-preview';
            
            const label = document.createElement('div');
            label.textContent = `${size}x${size}px`;
            label.style.fontWeight = 'bold';
            
            const filename_label = document.createElement('div');
            filename_label.textContent = filename;
            filename_label.style.fontSize = '12px';
            filename_label.style.color = '#666';
            
            const downloadBtn = document.createElement('button');
            downloadBtn.textContent = 'Download';
            downloadBtn.className = 'download-btn';
            downloadBtn.onclick = () => downloadCanvas(canvas, filename);
            
            container.appendChild(label);
            container.appendChild(canvas);
            container.appendChild(filename_label);
            container.appendChild(downloadBtn);
            
            return container;
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function generateAllIcons() {
            const container = document.getElementById('icons-container');
            container.innerHTML = '<h3>Generated Icons (Right-click to save):</h3>';
            
            const sizes = [
                { size: 16, filename: 'icon16.png' },
                { size: 32, filename: 'icon32.png' },
                { size: 48, filename: 'icon48.png' },
                { size: 128, filename: 'icon128.png' }
            ];
            
            sizes.forEach(({ size, filename }) => {
                const iconElement = createIconCanvas(size, filename);
                container.appendChild(iconElement);
            });
            
            // Add instructions
            const instructions = document.createElement('div');
            instructions.innerHTML = `
                <div style="margin-top: 20px; padding: 15px; background: #d4edda; border-radius: 4px; border: 1px solid #c3e6cb;">
                    <h4 style="margin-top: 0; color: #155724;">📁 Save Location:</h4>
                    <p style="margin-bottom: 0; color: #155724;">
                        Save all icons to: <code>yt-claim-trimmer/src/icons/</code><br>
                        Make sure the filenames match exactly: icon16.png, icon32.png, icon48.png, icon128.png
                    </p>
                </div>
            `;
            container.appendChild(instructions);
        }
    </script>
</body>
</html>
