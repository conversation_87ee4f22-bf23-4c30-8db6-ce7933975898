# ClaimCutter Versioning System

## Overview

ClaimCutter uses a centralized versioning system that automatically increments with each change and displays the version number in both console logs and the UI.

## Version Format

- **Format**: `MAJOR.MINOR.PATCH`
- **Example**: `2.1.02`
- **Current**: v2.1.02

## Version Management

### Centralized Version File
- **Location**: `src/utils/version.ts`
- **Purpose**: Single source of truth for version number
- **Exports**: 
  - `CLAIMCUTTER_VERSION`: Raw version string
  - `getVersionString(component)`: Formatted console log string
  - `getUIVersion()`: UI display format

### Automatic Version Bumping

#### Quick Version Bump + Build
```bash
npm run version:build
```
This command:
1. Increments the patch version (e.g., 2.1.01 → 2.1.02)
2. Builds the extension with the new version
3. Ready for testing

#### Manual Version Bump Only
```bash
npm run version:bump
```

#### Manual Build Only
```bash
npm run build
```

### Version Display Locations

#### 1. Console Logs
Every major component logs its version on startup:
```
🚀 ClaimCutter Background Script v2.1.02 - Enhanced Versioning
🚀 ClaimCutter Popup v2.1.02 - Enhanced Versioning
🚀 ClaimCutter Apply-Cuts Script File v2.1.02 - Enhanced Versioning
🚀 ClaimCutter Apply-Cuts Function v2.1.02 - Enhanced Versioning
🚀 ClaimCutter Apply-Cuts Main Execution v2.1.02 - Enhanced Versioning
```

#### 2. Extension Popup UI
- **Location**: Footer of the popup
- **Format**: `v2.1.02`
- **Style**: Red text, bold weight
- **Updates**: Automatically on extension reload

## Development Workflow

### Making Changes
1. **Make your code changes**
2. **Bump version and build**:
   ```bash
   npm run version:build
   ```
3. **Reload extension** in Chrome Extensions page
4. **Verify version** in console logs and popup UI
5. **Test your changes**

### Version Verification
- **Console**: Look for version logs starting with 🚀
- **UI**: Check footer of extension popup
- **Both should show the same version number**

## Troubleshooting

### Version Not Updating
1. **Check if build completed successfully**
2. **Reload extension** in Chrome Extensions page
3. **Hard refresh** the YouTube Studio page
4. **Check console logs** for version numbers

### Mixed Versions
If you see different version numbers in logs:
1. **Rebuild the extension**: `npm run build`
2. **Reload extension completely**
3. **Restart Chrome** if issues persist

### Script Caching Issues
If old code is still running:
1. **Disable and re-enable** the extension
2. **Clear browser cache**
3. **Restart Chrome**

## Version History

- **v2.1.02**: Enhanced versioning system with auto-increment
- **v2.1.01**: Initial versioning implementation
- **v2.1.00**: Latest timing fixes for race conditions

## Future Enhancements

- Automatic version display in extension icon badge
- Version-based feature flags
- Automatic changelog generation
- Semantic versioning with major/minor increments
