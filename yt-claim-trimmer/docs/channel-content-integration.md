# Channel Content Page Integration Design

## Overview

This document outlines the design for integrating ClaimCutter with the YouTube Studio Channel Content page, which provides an alternative entry point for batch processing copyright claims across multiple videos.

## Current vs New Flow

### Current Flow (Individual Video)
```
Individual Video Copyright Page → Collect Timestamps → Navigate to Editor → Apply Cuts
```

### New Flow (Channel Content Page)
```
Channel Content Page → Select Videos → Batch Collect Timestamps → Process Each Video → Apply Cuts
```

## Page Analysis

### URL Pattern
```
https://studio.youtube.com/channel/{CHANNEL_ID}/videos/upload?filter=%5B%5D&sort=%7B%22columnType%22%3A%22date%22%2C%22sortOrder%22%3A%22DESCENDING%22%7D
```

### Key Elements
- **Video Rows**: Table rows containing video information
- **Copyright Indicators**: Text like "Copyright", "See details", "Take action"
- **See Details Buttons**: Buttons that open copyright claim modals
- **Video IDs**: Embedded in href attributes or data attributes
- **Video Titles**: Displayed in title elements

## Technical Implementation Strategy

### 1. Page Detection
```javascript
function isChannelContentPage() {
  const url = window.location.href;
  return url.includes('/channel/') && 
         url.includes('/videos') && 
         document.querySelectorAll('tr, [role="row"]').length > 0;
}
```

### 2. Video Discovery
```javascript
function findCopyrightVideos() {
  const videoRows = document.querySelectorAll('tr, [role="row"]');
  const copyrightVideos = [];
  
  videoRows.forEach(row => {
    const rowText = row.textContent || '';
    const hasCopyright = rowText.includes('Copyright') || 
                        rowText.includes('See details');
    
    if (hasCopyright) {
      const video = extractVideoInfo(row);
      if (video.seeDetailsButton && video.videoId) {
        copyrightVideos.push(video);
      }
    }
  });
  
  return copyrightVideos;
}
```

### 3. Video Information Extraction
```javascript
function extractVideoInfo(row) {
  // Extract title
  const titleElement = row.querySelector('[data-testid="video-title"], .video-title, h3, h4');
  const title = titleElement?.textContent?.trim();
  
  // Extract video ID from href attributes
  let videoId = null;
  const videoLinks = row.querySelectorAll('a[href*="/video/"]');
  videoLinks.forEach(link => {
    const href = link.getAttribute('href');
    const match = href?.match(/\/video\/([a-zA-Z0-9_-]{11})/);
    if (match) videoId = match[1];
  });
  
  // Find "See details" button
  const seeDetailsButton = Array.from(row.querySelectorAll('button'))
    .find(btn => btn.textContent?.includes('See details'));
  
  return {
    title,
    videoId,
    seeDetailsButton,
    editorUrl: videoId ? `https://studio.youtube.com/video/${videoId}/editor` : null
  };
}
```

### 4. Timestamp Extraction (Reuse Existing Logic)
```javascript
async function extractTimestampsFromVideo(video) {
  // Click "See details" button
  video.seeDetailsButton.click();
  
  // Wait for modal
  await delay(1500);
  
  // Find modal and extract timestamps using existing logic
  const modal = document.querySelector('[role="dialog"]');
  const timestamps = extractTimestampsFromModal(modal);
  
  // Close modal
  closeModal(modal);
  
  return timestamps;
}
```

## Integration Approaches

### Approach 1: Extend Current Architecture
- Add channel content page detection to existing page compatibility check
- Extend popup UI to show multiple videos with copyright issues
- Add batch processing mode that iterates through videos
- Reuse existing timestamp extraction and cut application logic

### Approach 2: New Batch Processing Mode
- Create dedicated "Batch Mode" in extension popup
- Show list of videos with copyright issues
- Allow user to select which videos to process
- Process videos sequentially with progress tracking

### Approach 3: Hybrid Approach (Recommended)
- Detect page type and show appropriate UI
- For channel content page: show batch processing options
- For individual video page: show current single-video workflow
- Unified backend logic for timestamp extraction and cut application

## UI/UX Design

### Popup UI for Channel Content Page
```
┌─────────────────────────────────┐
│ ClaimCutter - Batch Mode        │
├─────────────────────────────────┤
│ Found 3 videos with copyright:  │
│                                 │
│ ☑ Video Title 1 (5 claims)      │
│ ☑ Video Title 2 (2 claims)      │
│ ☐ Video Title 3 (1 claim)       │
│                                 │
│ [Settings] [Start Batch Process]│
└─────────────────────────────────┘
```

### Progress Tracking
```
┌─────────────────────────────────┐
│ Processing Video 2 of 3...      │
├─────────────────────────────────┤
│ ✅ Video 1: 5 cuts applied      │
│ 🔄 Video 2: Extracting claims   │
│ ⏳ Video 3: Pending             │
│                                 │
│ [Cancel] [Skip Current]         │
└─────────────────────────────────┘
```

## Implementation Plan

### Phase 1: Detection and Analysis
1. Add channel content page detection
2. Implement video discovery logic
3. Test timestamp extraction from modals
4. Validate video ID extraction and editor URL construction

### Phase 2: UI Integration
1. Extend popup UI for batch mode
2. Add video selection interface
3. Implement progress tracking
4. Add batch processing controls

### Phase 3: Backend Integration
1. Extend background script for batch operations
2. Modify session storage structure for multiple videos
3. Implement sequential processing logic
4. Add error handling for batch operations

### Phase 4: Testing and Polish
1. Test with various channel content page layouts
2. Validate batch processing performance
3. Add comprehensive error recovery
4. Update documentation and user guides

## Technical Considerations

### Challenges
1. **Modal Timing**: Need to wait for modals to load between video processing
2. **Video ID Extraction**: Multiple methods needed for different page layouts
3. **Error Handling**: One failed video shouldn't stop entire batch
4. **Performance**: Processing many videos sequentially may be slow
5. **UI State**: Managing state across multiple video processing sessions

### Solutions
1. **Robust Delays**: Use progressive delays and element detection
2. **Fallback Selectors**: Multiple strategies for video ID extraction
3. **Graceful Degradation**: Continue processing even if some videos fail
4. **Progress Feedback**: Clear progress indicators and ability to cancel
5. **Session Management**: Enhanced session storage for batch operations

## Code Structure

### New Files Needed
- `src/scripts/channel-content-detector.ts` - Page detection and video discovery
- `src/scripts/batch-processor.ts` - Batch processing orchestration
- `src/ui/batch-popup.ts` - Batch mode popup UI
- `src/types/batch-types.ts` - TypeScript types for batch operations

### Modified Files
- `src/background.ts` - Add batch operation message handling
- `src/ui/popup.ts` - Add batch mode detection and UI switching
- `src/scripts/collect-timestamps.ts` - Extend for batch operations
- `manifest.json` - Ensure permissions cover channel content pages

## Testing Strategy

### Manual Testing
1. Test on various channel content page layouts
2. Verify video discovery accuracy
3. Test modal interaction timing
4. Validate batch processing flow

### Automated Testing
1. Unit tests for video discovery logic
2. Integration tests for batch processing
3. Mock modal interactions for timestamp extraction
4. Performance tests for large video lists

## Success Metrics

### Functionality
- ✅ Accurately detect videos with copyright issues
- ✅ Successfully extract video IDs and construct editor URLs
- ✅ Extract timestamps from "See details" modals
- ✅ Process multiple videos in sequence
- ✅ Maintain existing single-video functionality

### Performance
- ⏱️ Process 5 videos in under 2 minutes
- 🎯 95%+ accuracy in video discovery
- 🔄 Graceful handling of failed videos
- 💾 Efficient memory usage during batch operations

### User Experience
- 🎨 Intuitive batch mode interface
- 📊 Clear progress tracking
- ⚠️ Helpful error messages
- 🛑 Ability to cancel or skip videos

---

*This design provides a comprehensive approach to integrating the channel content page while maintaining the existing functionality and user experience of ClaimCutter.*
