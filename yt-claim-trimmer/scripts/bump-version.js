#!/usr/bin/env node

/**
 * Version Bump Script for ClaimCutter
 * Automatically increments the patch version number
 * Usage: node scripts/bump-version.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const VERSION_FILE = path.join(__dirname, '../src/utils/version.ts');
const APPLY_CUTS_FILE = path.join(__dirname, '../src/scripts/apply-cuts.ts');
const BACKGROUND_FILE = path.join(__dirname, '../src/background.ts');

function bumpVersion() {
  try {
    // Read the current version file
    const content = fs.readFileSync(VERSION_FILE, 'utf8');
    
    // Extract current version using regex
    const versionMatch = content.match(/export const CLAIMCUTTER_VERSION = '(\d+)\.(\d+)\.(\d+)';/);
    
    if (!versionMatch) {
      console.error('❌ Could not find version pattern in version.ts');
      process.exit(1);
    }
    
    const [, major, minor, patch] = versionMatch;
    const currentVersion = `${major}.${minor}.${patch}`;
    
    // Increment patch version
    const newPatch = String(parseInt(patch) + 1).padStart(2, '0');
    const newVersion = `${major}.${minor}.${newPatch}`;
    
    // Update the file content
    const newContent = content.replace(
      /export const CLAIMCUTTER_VERSION = '\d+\.\d+\.\d+';/,
      `export const CLAIMCUTTER_VERSION = '${newVersion}';`
    );
    
    // Write the updated content
    fs.writeFileSync(VERSION_FILE, newContent, 'utf8');

    // Also update the inline version in apply-cuts.ts
    try {
      const applyCutsContent = fs.readFileSync(APPLY_CUTS_FILE, 'utf8');
      const updatedApplyCutsContent = applyCutsContent.replace(
        /const CLAIMCUTTER_VERSION = '\d+\.\d+\.\d+';/,
        `const CLAIMCUTTER_VERSION = '${newVersion}';`
      );
      fs.writeFileSync(APPLY_CUTS_FILE, updatedApplyCutsContent, 'utf8');
      console.log(`📝 Updated inline version in: ${APPLY_CUTS_FILE}`);
    } catch (error) {
      console.warn(`⚠️ Could not update apply-cuts.ts: ${error.message}`);
    }

    // Also update the inline version in background.ts
    try {
      const backgroundContent = fs.readFileSync(BACKGROUND_FILE, 'utf8');
      const updatedBackgroundContent = backgroundContent.replace(
        /const CLAIMCUTTER_VERSION = '\d+\.\d+\.\d+';/,
        `const CLAIMCUTTER_VERSION = '${newVersion}';`
      );
      fs.writeFileSync(BACKGROUND_FILE, updatedBackgroundContent, 'utf8');
      console.log(`📝 Updated inline version in: ${BACKGROUND_FILE}`);
    } catch (error) {
      console.warn(`⚠️ Could not update background.ts: ${error.message}`);
    }

    console.log(`🚀 Version bumped: ${currentVersion} → ${newVersion}`);
    console.log(`📝 Updated: ${VERSION_FILE}`);
    console.log('');
    console.log('🔧 Next steps:');
    console.log('1. Run: npm run build');
    console.log('2. Reload extension in Chrome');
    console.log('3. Test the new version');
    
  } catch (error) {
    console.error('❌ Error bumping version:', error.message);
    process.exit(1);
  }
}

// Run the version bump
bumpVersion();
