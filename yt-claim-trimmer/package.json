{"name": "yt-claim-trimmer", "version": "1.0.0", "description": "ClaimCutter - YouTube Copyright Claim Batch Trimmer Chrome Extension", "type": "module", "scripts": {"dev": "vite build --watch --mode development", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .ts,.tsx --fix", "lint:check": "eslint src --ext .ts,.tsx", "format": "prettier --write \"src/**/*.{ts,tsx,html,css}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,html,css}\"", "test": "jest", "test:e2e": "playwright test", "clean": "rm -rf dist", "package": "npm run build && cd dist && zip -r ../yt-claim-trimmer-v1.0.0.zip .", "version:bump": "node scripts/bump-version.js", "version:build": "npm run version:bump && npm run build"}, "devDependencies": {"@types/chrome": "^0.0.268", "@types/jest": "^27.5.2", "@types/node": "^20.11.17", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@playwright/test": "^1.41.2", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-plugin-import": "^2.29.1", "jest": "^27.5.1", "jest-chrome": "^0.8.0", "jest-environment-jsdom": "^27.5.1", "prettier": "^3.2.5", "ts-jest": "^27.1.5", "typescript": "^5.3.3", "vite": "^5.1.3", "vite-plugin-static-copy": "^1.0.1"}, "dependencies": {}, "keywords": ["youtube", "copyright", "chrome-extension", "automation", "video-editing"], "author": "ClaimCutter Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/yt-claim-trimmer.git"}}