/**
 * In-page Modal Utilities
 * 
 * Creates confirmation modals using Shadow DOM to avoid conflicts
 * with YouTube Studio's styling and JavaScript.
 */

import { TimestampPair } from './timestamp-parser';
import { formatRangesForDisplay } from './range-merger';

export interface ModalOptions {
  title: string;
  message?: string;
  ranges: TimestampPair[];
  dryRun: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

/**
 * Create and show confirmation modal
 */
export function showConfirmationModal(options: ModalOptions): HTMLElement {
  const modal = createModal(options);
  document.body.appendChild(modal);
  
  // Focus the modal for accessibility
  const firstButton = modal.shadowRoot?.querySelector('button');
  if (firstButton) {
    firstButton.focus();
  }
  
  return modal;
}

/**
 * Create modal element with shadow DOM
 */
function createModal(options: ModalOptions): HTMLElement {
  const modalContainer = document.createElement('div');
  modalContainer.id = 'claimcutter-modal';
  
  // Create shadow DOM to isolate styles
  const shadow = modalContainer.attachShadow({ mode: 'closed' });
  
  // Create modal content
  const modalContent = createModalContent(options);
  shadow.appendChild(modalContent);
  
  return modalContainer;
}

/**
 * Create modal content with inline styles
 */
function createModalContent(options: ModalOptions): HTMLElement {
  const modal = document.createElement('div');
  modal.innerHTML = `
    <style>
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .modal-content {
        background: white;
        border-radius: 8px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        margin: 20px;
        animation: modalSlideIn 0.3s ease-out;
      }
      
      @keyframes modalSlideIn {
        from {
          opacity: 0;
          transform: translateY(-20px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }
      
      .modal-header {
        padding: 24px 24px 0 24px;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 20px;
      }
      
      .modal-title {
        font-size: 20px;
        font-weight: 600;
        color: #111827;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .modal-subtitle {
        font-size: 14px;
        color: #6b7280;
        margin: 0;
      }
      
      .modal-body {
        padding: 0 24px 20px 24px;
      }
      
      .warning-banner {
        background: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 20px;
        display: flex;
        align-items: start;
        gap: 8px;
      }
      
      .warning-icon {
        color: #f59e0b;
        font-size: 18px;
        margin-top: 1px;
      }
      
      .warning-text {
        font-size: 14px;
        color: #92400e;
        line-height: 1.4;
      }
      
      .cuts-list {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 20px;
      }
      
      .cuts-header {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .cuts-count {
        background: #3b82f6;
        color: white;
        font-size: 12px;
        font-weight: 500;
        padding: 2px 8px;
        border-radius: 12px;
      }
      
      .cut-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e5e7eb;
        font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        font-size: 14px;
      }
      
      .cut-item:last-child {
        border-bottom: none;
      }
      
      .cut-time {
        color: #374151;
        font-weight: 500;
      }
      
      .cut-duration {
        color: #6b7280;
        font-size: 12px;
      }
      
      .summary {
        background: #eff6ff;
        border: 1px solid #3b82f6;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 20px;
      }
      
      .summary-text {
        font-size: 14px;
        color: #1e40af;
        margin: 0;
      }
      
      .modal-footer {
        padding: 0 24px 24px 24px;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
      }
      
      .btn {
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        border: none;
        transition: all 0.2s ease;
        min-width: 80px;
      }
      
      .btn-cancel {
        background: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
      }
      
      .btn-cancel:hover {
        background: #e5e7eb;
      }
      
      .btn-confirm {
        background: #dc2626;
        color: white;
      }
      
      .btn-confirm:hover {
        background: #b91c1c;
      }
      
      .btn-dry-run {
        background: #3b82f6;
        color: white;
      }
      
      .btn-dry-run:hover {
        background: #2563eb;
      }
      
      .icon {
        display: inline-block;
        width: 20px;
        height: 20px;
      }
    </style>
    
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">
            <span class="icon">${options.dryRun ? '🔍' : '✂️'}</span>
            ${options.title}
          </h2>
          <p class="modal-subtitle">
            ${options.dryRun ? 'Preview mode - no changes will be made' : 'Review cuts before applying'}
          </p>
        </div>
        
        <div class="modal-body">
          ${!options.dryRun ? `
            <div class="warning-banner">
              <span class="warning-icon">⚠️</span>
              <div class="warning-text">
                <strong>Warning:</strong> These cuts will permanently modify your video. 
                Make sure you have a backup before proceeding.
              </div>
            </div>
          ` : ''}
          
          <div class="cuts-list">
            <div class="cuts-header">
              Cuts to Apply
              <span class="cuts-count">${options.ranges.length}</span>
            </div>
            ${createCutsList(options.ranges)}
          </div>
          
          <div class="summary">
            <p class="summary-text">
              <strong>Total:</strong> ${options.ranges.length} cuts removing 
              ${formatTotalDuration(options.ranges)} of content
            </p>
          </div>
          
          ${options.message ? `<p style="color: #6b7280; font-size: 14px; margin-bottom: 20px;">${options.message}</p>` : ''}
        </div>
        
        <div class="modal-footer">
          <button class="btn btn-cancel" id="cancel-btn">Cancel</button>
          <button class="btn ${options.dryRun ? 'btn-dry-run' : 'btn-confirm'}" id="confirm-btn">
            ${options.dryRun ? 'Close Preview' : 'Apply Cuts'}
          </button>
        </div>
      </div>
    </div>
  `;
  
  // Add event listeners
  const cancelBtn = modal.querySelector('#cancel-btn');
  const confirmBtn = modal.querySelector('#confirm-btn');
  
  if (cancelBtn) {
    cancelBtn.addEventListener('click', options.onCancel);
  }
  
  if (confirmBtn) {
    confirmBtn.addEventListener('click', options.onConfirm);
  }
  
  // Close on overlay click
  const overlay = modal.querySelector('.modal-overlay');
  if (overlay) {
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        options.onCancel();
      }
    });
  }
  
  // Close on Escape key
  document.addEventListener('keydown', function escapeHandler(e) {
    if (e.key === 'Escape') {
      document.removeEventListener('keydown', escapeHandler);
      options.onCancel();
    }
  });
  
  return modal;
}

/**
 * Create HTML for cuts list
 */
function createCutsList(ranges: TimestampPair[]): string {
  const formattedRanges = formatRangesForDisplay(ranges);
  
  return formattedRanges.map((rangeText, index) => {
    const range = ranges[index];
    const duration = range.end - range.start;
    
    return `
      <div class="cut-item">
        <span class="cut-time">${rangeText}</span>
        <span class="cut-duration">${formatDuration(duration)}</span>
      </div>
    `;
  }).join('');
}

/**
 * Format duration in seconds to human readable format
 */
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

/**
 * Calculate and format total duration of all cuts
 */
function formatTotalDuration(ranges: TimestampPair[]): string {
  const totalSeconds = ranges.reduce((sum, range) => sum + (range.end - range.start), 0);
  return formatDuration(totalSeconds);
}

/**
 * Remove modal from DOM
 */
export function removeModal(modal: HTMLElement): void {
  if (modal && modal.parentNode) {
    modal.parentNode.removeChild(modal);
  }
}
