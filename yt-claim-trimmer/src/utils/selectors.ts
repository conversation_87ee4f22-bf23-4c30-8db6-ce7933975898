/**
 * Centralized YouTube Studio Selector Mapping
 * 
 * This file contains all DOM selectors for YouTube Studio pages.
 * Uses ARIA-first approach with CSS fallbacks for internationalization support.
 * 
 * Based on research of YouTube Studio DOM structure as of 2025.
 */

export interface SelectorConfig {
  primary: string;
  fallbacks: string[];
  description: string;
}

/**
 * Copyright page selectors for claim detection and interaction
 */
export const COPYRIGHT_PAGE_SELECTORS = {
  // Main copyright claims table
  claimsTable: {
    primary: '[role="table"]',
    fallbacks: [
      '.ytcp-copyright-table',
      '[data-testid="copyright-table"]',
      'table'
    ],
    description: 'Main table containing copyright claims'
  },

  // Individual claim rows
  claimRows: {
    primary: '[role="row"]:not([role="columnheader"])',
    fallbacks: [
      '.ytcp-copyright-table-row',
      '[data-testid="copyright-row"]',
      'tr:not(:first-child)'
    ],
    description: 'Individual copyright claim rows'
  },

  // "See details" button for each claim
  seeDetailsButton: {
    primary: '[aria-label*="See details"], [aria-label*="View details"]',
    fallbacks: [
      'button[aria-label*="details"]',
      '.ytcp-copyright-details-button',
      'button:contains("See details")',
      'button:contains("View details")'
    ],
    description: 'Button to expand claim details'
  },

  // Timestamp text in expanded details
  timestampText: {
    primary: '[aria-label*="Content found"], [aria-label*="Claimed content"]',
    fallbacks: [
      '*:contains("Content found in")',
      '*:contains("Claimed content:")',
      '.ytcp-copyright-timestamp',
      '[data-testid="timestamp-info"]'
    ],
    description: 'Text containing timestamp information'
  },

  // Close details button
  closeDetailsButton: {
    primary: '[aria-label*="Close"], [aria-label*="Collapse"]',
    fallbacks: [
      'button[aria-label*="close"]',
      '.ytcp-copyright-close-button',
      'button:contains("Close")'
    ],
    description: 'Button to close expanded claim details'
  }
} as const;

/**
 * Editor page selectors for cut application
 */
export const EDITOR_PAGE_SELECTORS = {
  // Main timeline timecode input
  timelineTimecode: {
    primary: '[aria-label*="Timeline timecode"], [aria-label*="Current time"]',
    fallbacks: [
      'input[aria-label*="timecode"]',
      '.ytcp-editor-timecode-input',
      '[data-testid="timeline-timecode"]',
      'input[type="text"][placeholder*="00:00"]'
    ],
    description: 'Main timeline timecode input field'
  },

  // New cut button
  newCutButton: {
    primary: '[aria-label*="New cut"], [aria-label*="Add cut"]',
    fallbacks: [
      'button[aria-label*="cut"]',
      '.ytcp-editor-new-cut-button',
      '[data-testid="new-cut-button"]',
      'button:contains("New cut")'
    ],
    description: 'Button to create a new cut'
  },

  // Cut dialog elements
  cutDialog: {
    primary: '[role="dialog"], [aria-label*="Cut"], [aria-label*="Trim"]',
    fallbacks: [
      '.ytcp-editor-cut-dialog',
      '[data-testid="cut-dialog"]',
      '.dialog:contains("Cut")'
    ],
    description: 'Cut creation dialog'
  },

  // Start time input in cut dialog
  cutStartTime: {
    primary: '[aria-label*="Start time"], [aria-label*="From"]',
    fallbacks: [
      'input[aria-label*="start"]',
      '.ytcp-cut-start-input',
      '[data-testid="cut-start-time"]'
    ],
    description: 'Start time input in cut dialog'
  },

  // End time input in cut dialog
  cutEndTime: {
    primary: '[aria-label*="End time"], [aria-label*="To"]',
    fallbacks: [
      'input[aria-label*="end"]',
      '.ytcp-cut-end-input',
      '[data-testid="cut-end-time"]'
    ],
    description: 'End time input in cut dialog'
  },

  // Confirm cut button
  confirmCutButton: {
    primary: '[aria-label*="Confirm"], [aria-label*="Apply"], [aria-label*="Save cut"]',
    fallbacks: [
      'button[aria-label*="confirm"]',
      '.ytcp-cut-confirm-button',
      '[data-testid="confirm-cut"]',
      'button:contains("Confirm")',
      'button:contains("Apply")'
    ],
    description: 'Button to confirm and apply the cut'
  },

  // Save button (for manual user action)
  saveButton: {
    primary: '[aria-label*="Save"], [aria-label*="Save changes"]',
    fallbacks: [
      'button[aria-label*="save"]',
      '.ytcp-editor-save-button',
      '[data-testid="save-button"]',
      'button:contains("Save")'
    ],
    description: 'Main save button for video changes'
  }
} as const;

/**
 * General page selectors for navigation and state detection
 */
export const GENERAL_SELECTORS = {
  // Loading indicators
  loadingSpinner: {
    primary: '[role="progressbar"], [aria-label*="Loading"]',
    fallbacks: [
      '.ytcp-loading-spinner',
      '[data-testid="loading"]',
      '.spinner'
    ],
    description: 'Loading indicator elements'
  },

  // Error messages
  errorMessage: {
    primary: '[role="alert"], [aria-label*="Error"]',
    fallbacks: [
      '.ytcp-error-message',
      '[data-testid="error"]',
      '.error'
    ],
    description: 'Error message elements'
  },

  // Success messages
  successMessage: {
    primary: '[role="status"], [aria-label*="Success"]',
    fallbacks: [
      '.ytcp-success-message',
      '[data-testid="success"]',
      '.success'
    ],
    description: 'Success message elements'
  }
} as const;

/**
 * Utility function to find element using selector config
 */
export function findElement(
  selectorConfig: SelectorConfig, 
  context: Document | Element = document
): Element | null {
  // Try primary selector first
  let element = context.querySelector(selectorConfig.primary);
  if (element) {
    return element;
  }

  // Try fallback selectors
  for (const fallback of selectorConfig.fallbacks) {
    element = context.querySelector(fallback);
    if (element) {
      console.warn(`Primary selector failed, using fallback: ${fallback}`);
      return element;
    }
  }

  console.error(`No element found for selector config: ${selectorConfig.description}`);
  return null;
}

/**
 * Utility function to find all elements using selector config
 */
export function findElements(
  selectorConfig: SelectorConfig, 
  context: Document | Element = document
): Element[] {
  // Try primary selector first
  let elements = Array.from(context.querySelectorAll(selectorConfig.primary));
  if (elements.length > 0) {
    return elements;
  }

  // Try fallback selectors
  for (const fallback of selectorConfig.fallbacks) {
    elements = Array.from(context.querySelectorAll(fallback));
    if (elements.length > 0) {
      console.warn(`Primary selector failed, using fallback: ${fallback}`);
      return elements;
    }
  }

  console.error(`No elements found for selector config: ${selectorConfig.description}`);
  return [];
}

/**
 * Wait for element to appear with timeout
 */
export function waitForElement(
  selectorConfig: SelectorConfig,
  timeout: number = 10000,
  context: Document | Element = document
): Promise<Element> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const checkElement = () => {
      const element = findElement(selectorConfig, context);
      if (element) {
        resolve(element);
        return;
      }

      if (Date.now() - startTime > timeout) {
        reject(new Error(`Element not found within ${timeout}ms: ${selectorConfig.description}`));
        return;
      }

      setTimeout(checkElement, 100);
    };

    checkElement();
  });
}
