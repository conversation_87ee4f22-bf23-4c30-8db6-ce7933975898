/**
 * Settings management for ClaimCutter Chrome Extension
 * Handles user preferences with persistent storage
 */

export interface ClaimCutterSettings {
  mergeBuffer: number; // seconds (5-30)
  turboMode: boolean; // auto-navigate and apply cuts
  autoSave: boolean; // auto-save video (if enabled in turbo mode)
  defaultSettings: boolean; // whether these are saved as defaults
}

export const DEFAULT_SETTINGS: ClaimCutterSettings = {
  mergeBuffer: 15,
  turboMode: false,
  autoSave: false,
  defaultSettings: false,
};

/**
 * Load settings from chrome.storage.sync
 */
export async function loadSettings(): Promise<ClaimCutterSettings> {
  try {
    const result = await chrome.storage.sync.get('claimcutter_settings');
    const saved = result.claimcutter_settings;
    
    if (saved && typeof saved === 'object') {
      return {
        ...DEFAULT_SETTINGS,
        ...saved,
      };
    }
    
    return DEFAULT_SETTINGS;
  } catch (error) {
    console.error('Failed to load settings:', error);
    return DEFAULT_SETTINGS;
  }
}

/**
 * Save settings to chrome.storage.sync
 */
export async function saveSettings(settings: ClaimCutterSettings): Promise<void> {
  try {
    await chrome.storage.sync.set({
      claimcutter_settings: settings,
    });
    console.log('Settings saved:', settings);
  } catch (error) {
    console.error('Failed to save settings:', error);
    throw new Error('Failed to save settings');
  }
}

/**
 * Reset settings to defaults
 */
export async function resetSettings(): Promise<ClaimCutterSettings> {
  const defaults = { ...DEFAULT_SETTINGS };
  await saveSettings(defaults);
  return defaults;
}

/**
 * Validate settings values
 */
export function validateSettings(settings: Partial<ClaimCutterSettings>): ClaimCutterSettings {
  return {
    mergeBuffer: Math.max(5, Math.min(30, settings.mergeBuffer || DEFAULT_SETTINGS.mergeBuffer)),
    turboMode: Boolean(settings.turboMode),
    autoSave: Boolean(settings.autoSave),
    defaultSettings: Boolean(settings.defaultSettings),
  };
}

/**
 * Get merge buffer description for UI
 */
export function getMergeBufferDescription(seconds: number): string {
  if (seconds <= 5) return 'Minimal merging - only exact overlaps';
  if (seconds <= 10) return 'Conservative merging - close ranges only';
  if (seconds <= 15) return 'Balanced merging - recommended for most cases';
  if (seconds <= 20) return 'Aggressive merging - combines nearby claims';
  return 'Maximum merging - combines distant claims';
}

/**
 * Get turbo mode description for UI
 */
export function getTurboModeDescription(enabled: boolean): string {
  if (enabled) {
    return 'Automatically navigate to editor and apply cuts without confirmation';
  }
  return 'Manual confirmation required for each step';
}
