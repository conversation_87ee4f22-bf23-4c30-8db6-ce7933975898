/**
 * Centralized Error Handling Utilities
 * 
 * Provides consistent error handling, logging, and user-friendly error messages
 * across the entire Chrome extension.
 */

export interface ExtensionError {
  code: string;
  message: string;
  details?: string;
  timestamp: number;
  context?: string;
  recoverable: boolean;
  userMessage: string;
  actions?: ErrorAction[];
}

export interface ErrorAction {
  label: string;
  action: () => void;
  primary?: boolean;
}

/**
 * Error codes for different types of failures
 */
export const ERROR_CODES = {
  // Page/Navigation errors
  INVALID_PAGE: 'INVALID_PAGE',
  PAGE_LOAD_FAILED: 'PAGE_LOAD_FAILED',
  NAVIGATION_FAILED: 'NAVIGATION_FAILED',
  
  // Selector/DOM errors
  ELEMENT_NOT_FOUND: 'ELEMENT_NOT_FOUND',
  SELECTOR_FAILED: 'SELECTOR_FAILED',
  DOM_INTERACTION_FAILED: 'DOM_INTERACTION_FAILED',
  
  // Collection errors
  NO_CLAIMS_FOUND: 'NO_CLAIMS_FOUND',
  TIMESTAMP_PARSE_FAILED: 'TIMESTAMP_PARSE_FAILED',
  COLLECTION_TIMEOUT: 'COLLECTION_TIMEOUT',
  
  // Application errors
  CUT_APPLICATION_FAILED: 'CUT_APPLICATION_FAILED',
  EDITOR_NOT_READY: 'EDITOR_NOT_READY',
  SAVE_BUTTON_NOT_FOUND: 'SAVE_BUTTON_NOT_FOUND',
  
  // System errors
  STORAGE_ERROR: 'STORAGE_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  EXTENSION_ERROR: 'EXTENSION_ERROR',
  
  // User errors
  USER_CANCELLED: 'USER_CANCELLED',
  INVALID_INPUT: 'INVALID_INPUT',
  
  // Network/Auth errors
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  RATE_LIMITED: 'RATE_LIMITED',
} as const;

/**
 * Create a standardized extension error
 */
export function createError(
  code: keyof typeof ERROR_CODES,
  message: string,
  details?: string,
  context?: string
): ExtensionError {
  const errorInfo = getErrorInfo(code);
  
  return {
    code,
    message,
    details,
    timestamp: Date.now(),
    context,
    recoverable: errorInfo.recoverable,
    userMessage: errorInfo.userMessage,
    actions: errorInfo.actions,
  };
}

/**
 * Get error information including user-friendly messages and recovery actions
 */
function getErrorInfo(code: keyof typeof ERROR_CODES): {
  recoverable: boolean;
  userMessage: string;
  actions?: ErrorAction[];
} {
  switch (code) {
    case 'INVALID_PAGE':
      return {
        recoverable: true,
        userMessage: 'Please navigate to a YouTube Studio copyright page first.',
        actions: [
          {
            label: 'Open YouTube Studio',
            action: () => window.open('https://studio.youtube.com', '_blank'),
            primary: true,
          },
        ],
      };
      
    case 'NO_CLAIMS_FOUND':
      return {
        recoverable: true,
        userMessage: 'No copyright claims found on this video. Make sure you\'re on the Copyright tab.',
        actions: [
          {
            label: 'Refresh Page',
            action: () => window.location.reload(),
          },
        ],
      };
      
    case 'SESSION_EXPIRED':
      return {
        recoverable: true,
        userMessage: 'Your YouTube session has expired. Please log in again.',
        actions: [
          {
            label: 'Refresh & Login',
            action: () => window.location.reload(),
            primary: true,
          },
        ],
      };
      
    case 'ELEMENT_NOT_FOUND':
      return {
        recoverable: true,
        userMessage: 'YouTube Studio interface has changed. The extension may need an update.',
        actions: [
          {
            label: 'Report Issue',
            action: () => window.open('https://github.com/your-repo/issues', '_blank'),
          },
        ],
      };
      
    case 'CUT_APPLICATION_FAILED':
      return {
        recoverable: true,
        userMessage: 'Failed to apply cuts. You can try again or apply them manually.',
        actions: [
          {
            label: 'Try Again',
            action: () => window.location.reload(),
            primary: true,
          },
        ],
      };
      
    case 'USER_CANCELLED':
      return {
        recoverable: true,
        userMessage: 'Operation cancelled by user.',
      };
      
    case 'RATE_LIMITED':
      return {
        recoverable: true,
        userMessage: 'Please wait a moment before trying again. YouTube may be rate limiting requests.',
        actions: [
          {
            label: 'Wait & Retry',
            action: () => setTimeout(() => window.location.reload(), 5000),
            primary: true,
          },
        ],
      };
      
    default:
      return {
        recoverable: false,
        userMessage: 'An unexpected error occurred. Please try refreshing the page.',
        actions: [
          {
            label: 'Refresh Page',
            action: () => window.location.reload(),
            primary: true,
          },
        ],
      };
  }
}

/**
 * Log error with appropriate level and context
 */
export function logError(error: ExtensionError): void {
  const logData = {
    code: error.code,
    message: error.message,
    details: error.details,
    context: error.context,
    timestamp: new Date(error.timestamp).toISOString(),
  };
  
  if (error.recoverable) {
    console.warn('[ClaimCutter] Recoverable error:', logData);
  } else {
    console.error('[ClaimCutter] Critical error:', logData);
  }
}

/**
 * Send error to background script for centralized handling
 */
export function reportError(error: ExtensionError): void {
  logError(error);
  
  try {
    chrome.runtime.sendMessage({
      action: 'ERROR_REPORT',
      error: {
        code: error.code,
        message: error.message,
        details: error.details,
        context: error.context,
        timestamp: error.timestamp,
        recoverable: error.recoverable,
      },
    });
  } catch (e) {
    console.error('[ClaimCutter] Failed to report error to background:', e);
  }
}

/**
 * Handle errors from Chrome extension APIs
 */
export function handleChromeError(context: string): ExtensionError | null {
  if (chrome.runtime.lastError) {
    const error = createError(
      'EXTENSION_ERROR',
      chrome.runtime.lastError.message || 'Unknown Chrome extension error',
      undefined,
      context
    );
    reportError(error);
    return error;
  }
  return null;
}

/**
 * Wrap async functions with error handling
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context: string
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    try {
      const result = await fn(...args);
      handleChromeError(context);
      return result;
    } catch (error) {
      const extensionError = createError(
        'EXTENSION_ERROR',
        error instanceof Error ? error.message : String(error),
        error instanceof Error ? error.stack : undefined,
        context
      );
      reportError(extensionError);
      throw extensionError;
    }
  };
}

/**
 * Create user-friendly error message for display
 */
export function formatErrorForUser(error: ExtensionError): string {
  let message = error.userMessage;
  
  if (error.details && error.code !== 'USER_CANCELLED') {
    message += '\n\nTechnical details: ' + error.details;
  }
  
  return message;
}

/**
 * Check if error is recoverable and suggest actions
 */
export function getRecoveryActions(error: ExtensionError): ErrorAction[] {
  return error.actions || [];
}

/**
 * Retry wrapper with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000,
  context: string = 'Unknown operation'
): Promise<T> {
  let lastError: ExtensionError;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error 
        ? createError('EXTENSION_ERROR', error.message, error.stack, context)
        : error as ExtensionError;
      
      if (attempt === maxAttempts) {
        reportError(lastError);
        throw lastError;
      }
      
      // Exponential backoff delay
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.warn(`[ClaimCutter] Attempt ${attempt} failed, retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}
