/**
 * Range Merging Utilities
 * 
 * Handles merging of overlapping and adjacent timestamp ranges
 * to optimize cut application in YouTube Studio editor.
 */

import { TimestampPair } from './timestamp-parser';

export interface MergedRange extends TimestampPair {
  originalCount: number; // Number of original ranges that were merged
  sources: string[];     // Original raw text sources for debugging
}

/**
 * Merge overlapping and adjacent timestamp ranges
 * 
 * Algorithm:
 * 1. Sort ranges by start time
 * 2. Iterate through sorted ranges
 * 3. Merge if current range overlaps or is adjacent to previous
 * 4. Keep track of original sources for debugging
 */
export function mergeRanges(ranges: TimestampPair[]): MergedRange[] {
  if (ranges.length === 0) {
    return [];
  }
  
  // Sort ranges by start time
  const sortedRanges = [...ranges].sort((a, b) => a.start - b.start);
  
  const merged: MergedRange[] = [];
  let current: MergedRange = {
    start: sortedRanges[0].start,
    end: sortedRanges[0].end,
    originalCount: 1,
    sources: [sortedRanges[0].raw || `${sortedRanges[0].start}-${sortedRanges[0].end}`]
  };
  
  for (let i = 1; i < sortedRanges.length; i++) {
    const range = sortedRanges[i];
    
    // Check if current range overlaps or is adjacent to the previous one
    // Adjacent means there's no gap (or minimal gap < 5 seconds)
    if (range.start <= current.end + 5) {
      // Merge ranges
      current.end = Math.max(current.end, range.end);
      current.originalCount++;
      current.sources.push(range.raw || `${range.start}-${range.end}`);
      
      console.log(`Merged range: ${current.start}-${current.end} (from ${current.originalCount} sources)`);
    } else {
      // No overlap, push current and start new range
      merged.push(current);
      current = {
        start: range.start,
        end: range.end,
        originalCount: 1,
        sources: [range.raw || `${range.start}-${range.end}`]
      };
    }
  }
  
  // Don't forget the last range
  merged.push(current);
  
  console.log(`Merged ${ranges.length} ranges into ${merged.length} ranges`);
  return merged;
}

/**
 * Check if two ranges overlap
 */
export function rangesOverlap(range1: TimestampPair, range2: TimestampPair): boolean {
  return range1.start < range2.end && range2.start < range1.end;
}

/**
 * Check if two ranges are adjacent (within tolerance)
 */
export function rangesAdjacent(range1: TimestampPair, range2: TimestampPair, tolerance: number = 5): boolean {
  // Check if range1 ends near where range2 starts, or vice versa
  return Math.abs(range1.end - range2.start) <= tolerance || 
         Math.abs(range2.end - range1.start) <= tolerance;
}

/**
 * Calculate total duration of ranges
 */
export function calculateTotalDuration(ranges: TimestampPair[]): number {
  return ranges.reduce((total, range) => total + (range.end - range.start), 0);
}

/**
 * Find gaps between ranges (useful for identifying unclaimed content)
 */
export function findGaps(ranges: TimestampPair[], videoDuration?: number): TimestampPair[] {
  if (ranges.length === 0) {
    return videoDuration ? [{ start: 0, end: videoDuration }] : [];
  }
  
  // Sort ranges by start time
  const sortedRanges = [...ranges].sort((a, b) => a.start - b.start);
  const gaps: TimestampPair[] = [];
  
  // Gap before first range
  if (sortedRanges[0].start > 0) {
    gaps.push({ start: 0, end: sortedRanges[0].start });
  }
  
  // Gaps between ranges
  for (let i = 0; i < sortedRanges.length - 1; i++) {
    const currentEnd = sortedRanges[i].end;
    const nextStart = sortedRanges[i + 1].start;
    
    if (nextStart > currentEnd) {
      gaps.push({ start: currentEnd, end: nextStart });
    }
  }
  
  // Gap after last range
  if (videoDuration && sortedRanges[sortedRanges.length - 1].end < videoDuration) {
    gaps.push({ 
      start: sortedRanges[sortedRanges.length - 1].end, 
      end: videoDuration 
    });
  }
  
  return gaps;
}

/**
 * Optimize ranges for cutting by removing very short segments
 */
export function optimizeRanges(ranges: TimestampPair[], minDuration: number = 3): TimestampPair[] {
  return ranges.filter(range => {
    const duration = range.end - range.start;
    if (duration < minDuration) {
      console.warn(`Removing short range: ${range.start}-${range.end} (${duration}s < ${minDuration}s)`);
      return false;
    }
    return true;
  });
}

/**
 * Validate that ranges don't exceed video bounds
 */
export function validateRanges(ranges: TimestampPair[], videoDuration?: number): TimestampPair[] {
  return ranges.filter(range => {
    // Check basic validity
    if (range.start < 0 || range.end <= range.start) {
      console.warn(`Invalid range: ${range.start}-${range.end}`);
      return false;
    }
    
    // Check video duration bounds
    if (videoDuration && range.end > videoDuration) {
      console.warn(`Range exceeds video duration: ${range.start}-${range.end} > ${videoDuration}`);
      // Trim to video duration instead of removing
      range.end = videoDuration;
    }
    
    return true;
  });
}

/**
 * Convert ranges to human-readable format for display
 */
export function formatRangesForDisplay(ranges: TimestampPair[]): string[] {
  return ranges.map(range => {
    const startTime = formatSeconds(range.start);
    const endTime = formatSeconds(range.end);
    const duration = formatSeconds(range.end - range.start);
    
    return `${startTime} - ${endTime} (${duration})`;
  });
}

/**
 * Format seconds as MM:SS or HH:MM:SS
 */
function formatSeconds(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * Debug function to analyze range merging
 */
export function debugRangeMerging(ranges: TimestampPair[]): void {
  console.group('Range Merging Analysis');
  
  console.log('Original ranges:');
  ranges.forEach((range, index) => {
    console.log(`  ${index + 1}: ${formatSeconds(range.start)} - ${formatSeconds(range.end)} (${formatSeconds(range.end - range.start)})`);
  });
  
  const merged = mergeRanges(ranges);
  console.log('\nMerged ranges:');
  merged.forEach((range, index) => {
    console.log(`  ${index + 1}: ${formatSeconds(range.start)} - ${formatSeconds(range.end)} (${formatSeconds(range.end - range.start)}) [from ${range.originalCount} sources]`);
  });
  
  const originalDuration = calculateTotalDuration(ranges);
  const mergedDuration = calculateTotalDuration(merged);
  
  console.log(`\nSummary:`);
  console.log(`  Original: ${ranges.length} ranges, ${formatSeconds(originalDuration)} total`);
  console.log(`  Merged: ${merged.length} ranges, ${formatSeconds(mergedDuration)} total`);
  console.log(`  Reduction: ${ranges.length - merged.length} ranges eliminated`);
  
  console.groupEnd();
}
