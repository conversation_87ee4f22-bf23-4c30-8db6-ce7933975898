/**
 * Timestamp Parsing Utilities
 * 
 * Handles parsing of timestamp strings from YouTube Studio copyright claims
 * and conversion to seconds for processing.
 */

export interface TimestampPair {
  start: number; // seconds
  end: number;   // seconds
  raw?: string;  // original text for debugging
}

/**
 * Regular expressions for parsing different timestamp formats
 */
const TIMESTAMP_PATTERNS = {
  // "Content found in 1:23 – 2:45" or "Content found in 01:23 – 02:45"
  contentFound: /Content found in\s+(\d{1,2}:\d{2}(?::\d{2})?)\s*[–—-]\s*(\d{1,2}:\d{2}(?::\d{2})?)/i,
  
  // "Claimed content: 1:23 - 2:45" or similar variations
  claimedContent: /Claimed content:?\s*(\d{1,2}:\d{2}(?::\d{2})?)\s*[–—-]\s*(\d{1,2}:\d{2}(?::\d{2})?)/i,
  
  // "From 1:23 to 2:45" or "1:23 to 2:45"
  fromTo: /(?:From\s+)?(\d{1,2}:\d{2}(?::\d{2})?)\s+to\s+(\d{1,2}:\d{2}(?::\d{2})?)/i,
  
  // Generic timestamp range with various separators
  generic: /(\d{1,2}:\d{2}(?::\d{2})?)\s*[–—\-~]\s*(\d{1,2}:\d{2}(?::\d{2})?)/,
  
  // Parenthetical format "(1:23 - 2:45)"
  parenthetical: /\((\d{1,2}:\d{2}(?::\d{2})?)\s*[–—-]\s*(\d{1,2}:\d{2}(?::\d{2})?)\)/
};

/**
 * Convert timestamp string (MM:SS or HH:MM:SS) to seconds
 */
export function timestampToSeconds(timestamp: string): number {
  const parts = timestamp.split(':').map(part => parseInt(part, 10));
  
  if (parts.length === 2) {
    // MM:SS format
    const [minutes, seconds] = parts;
    return minutes * 60 + seconds;
  } else if (parts.length === 3) {
    // HH:MM:SS format
    const [hours, minutes, seconds] = parts;
    return hours * 3600 + minutes * 60 + seconds;
  }
  
  throw new Error(`Invalid timestamp format: ${timestamp}`);
}

/**
 * Convert seconds back to timestamp string (MM:SS or HH:MM:SS)
 */
export function secondsToTimestamp(seconds: number, includeHours: boolean = false): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  // Use MM:SS for times under 1 hour, HH:MM:SS for longer times (unless forced)
  if (includeHours || hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * Parse timestamp pair from text using multiple patterns
 */
export function parseTimestampPair(text: string): TimestampPair | null {
  // Clean up the text
  const cleanText = text.trim().replace(/\s+/g, ' ');
  
  // Try each pattern in order of specificity
  for (const [patternName, pattern] of Object.entries(TIMESTAMP_PATTERNS)) {
    const match = cleanText.match(pattern);
    if (match) {
      try {
        const startTime = timestampToSeconds(match[1]);
        const endTime = timestampToSeconds(match[2]);
        
        // Validate that end time is after start time
        if (endTime <= startTime) {
          console.warn(`Invalid timestamp range: ${match[1]} - ${match[2]} (end <= start)`);
          continue;
        }
        
        // Validate reasonable duration (not more than 24 hours)
        if (endTime - startTime > 24 * 3600) {
          console.warn(`Suspicious timestamp range: ${match[1]} - ${match[2]} (too long)`);
          continue;
        }
        
        console.log(`Parsed timestamp using ${patternName}: ${match[1]} - ${match[2]}`);
        
        return {
          start: startTime,
          end: endTime,
          raw: cleanText
        };
      } catch (error) {
        console.warn(`Failed to parse timestamps from match: ${match[1]} - ${match[2]}`, error);
        continue;
      }
    }
  }
  
  console.warn(`No timestamp pattern matched for text: "${cleanText}"`);
  return null;
}

/**
 * Parse multiple timestamp pairs from text
 */
export function parseMultipleTimestamps(text: string): TimestampPair[] {
  const pairs: TimestampPair[] = [];
  
  // Split text by common separators and try to parse each part
  const parts = text.split(/[,;]|\band\b|\bor\b/i);
  
  for (const part of parts) {
    const pair = parseTimestampPair(part);
    if (pair) {
      pairs.push(pair);
    }
  }
  
  return pairs;
}

/**
 * Validate timestamp pair
 */
export function validateTimestampPair(pair: TimestampPair): boolean {
  // Check basic validity
  if (pair.start < 0 || pair.end < 0) {
    return false;
  }
  
  if (pair.end <= pair.start) {
    return false;
  }
  
  // Check reasonable bounds (not more than 24 hours)
  if (pair.start > 24 * 3600 || pair.end > 24 * 3600) {
    return false;
  }
  
  // Check reasonable duration (not more than 12 hours for a single claim)
  if (pair.end - pair.start > 12 * 3600) {
    return false;
  }
  
  return true;
}

/**
 * Extract all possible timestamp information from DOM element
 */
export function extractTimestampsFromElement(element: Element): TimestampPair[] {
  const timestamps: TimestampPair[] = [];
  
  // Get all text content from the element and its children
  const textContent = element.textContent || '';
  
  // Try to parse as single timestamp pair first
  const singlePair = parseTimestampPair(textContent);
  if (singlePair && validateTimestampPair(singlePair)) {
    timestamps.push(singlePair);
    return timestamps;
  }
  
  // Try to parse multiple timestamp pairs
  const multiplePairs = parseMultipleTimestamps(textContent);
  for (const pair of multiplePairs) {
    if (validateTimestampPair(pair)) {
      timestamps.push(pair);
    }
  }
  
  return timestamps;
}

/**
 * Debug function to test timestamp parsing
 */
export function debugTimestampParsing(text: string): void {
  console.group(`Debug parsing: "${text}"`);
  
  for (const [patternName, pattern] of Object.entries(TIMESTAMP_PATTERNS)) {
    const match = text.match(pattern);
    if (match) {
      console.log(`✓ ${patternName}: ${match[1]} - ${match[2]}`);
      try {
        const start = timestampToSeconds(match[1]);
        const end = timestampToSeconds(match[2]);
        console.log(`  → ${start}s - ${end}s (duration: ${end - start}s)`);
      } catch (error) {
        console.log(`  → Parse error: ${error}`);
      }
    } else {
      console.log(`✗ ${patternName}: no match`);
    }
  }
  
  console.groupEnd();
}
