/**
 * ClaimCutter Version Management
 * Centralized version number for easy updates
 */

export const CLAIMCUTTER_VERSION = '2.1.03';

/**
 * Get formatted version string for console logs
 */
export function getVersionString(component: string): string {
  return `🚀 ClaimCutter ${component} v${CLAIMCUTTER_VERSION} - Enhanced Versioning`;
}

/**
 * Get version for UI display
 */
export function getUIVersion(): string {
  return `v${CLAIMCUTTER_VERSION}`;
}
