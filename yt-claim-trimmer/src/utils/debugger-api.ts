/**
 * Chrome Debugger API Utility for Trusted Event Dispatch
 * 
 * This module provides functions to dispatch trusted events using Chrome's Debugger API,
 * which can bypass anti-automation protections in web applications like YouTube Studio.
 * 
 * Key Features:
 * - Dispatch trusted keyboard events (Tab, Enter, etc.)
 * - Dispatch trusted mouse events (clicks, focus changes)
 * - Automatic debugger session management
 * - Error handling and cleanup
 */

export interface DebuggerSession {
  tabId: number;
  attached: boolean;
  version: string;
}

export interface TrustedKeyEventOptions {
  key: string;
  keyCode: number;
  code?: string;
  windowsVirtualKeyCode?: number;
  modifiers?: number;
}

export interface TrustedMouseEventOptions {
  x: number;
  y: number;
  button?: 'left' | 'right' | 'middle';
  clickCount?: number;
}

/**
 * Debugger API Manager Class
 */
export class DebuggerManager {
  private session: DebuggerSession | null = null;
  private readonly DEBUGGER_VERSION = '1.3';

  /**
   * Attach debugger to a tab
   */
  async attachToTab(tabId: number): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log(`🔧 Attaching debugger to tab ${tabId}...`);
      
      chrome.debugger.attach({ tabId }, this.DEBUGGER_VERSION, () => {
        if (chrome.runtime.lastError) {
          console.error('❌ Failed to attach debugger:', chrome.runtime.lastError.message);
          reject(new Error(`Failed to attach debugger: ${chrome.runtime.lastError.message}`));
          return;
        }

        this.session = {
          tabId,
          attached: true,
          version: this.DEBUGGER_VERSION
        };

        console.log('✅ Debugger attached successfully');
        resolve();
      });
    });
  }

  /**
   * Detach debugger from current tab
   */
  async detachFromTab(): Promise<void> {
    if (!this.session || !this.session.attached) {
      console.log('⚠️ No active debugger session to detach');
      return;
    }

    return new Promise((resolve) => {
      console.log(`🔧 Detaching debugger from tab ${this.session!.tabId}...`);
      
      chrome.debugger.detach({ tabId: this.session!.tabId }, () => {
        if (chrome.runtime.lastError) {
          console.warn('⚠️ Error detaching debugger:', chrome.runtime.lastError.message);
        } else {
          console.log('✅ Debugger detached successfully');
        }

        this.session = null;
        resolve();
      });
    });
  }

  /**
   * Dispatch trusted Tab key event
   */
  async dispatchTrustedTabKey(): Promise<void> {
    await this.dispatchTrustedKeyEvent({
      key: 'Tab',
      keyCode: 9,
      code: 'Tab',
      windowsVirtualKeyCode: 9
    });
  }

  /**
   * Dispatch trusted Enter key event
   */
  async dispatchTrustedEnterKey(): Promise<void> {
    await this.dispatchTrustedKeyEvent({
      key: 'Enter',
      keyCode: 13,
      code: 'Enter',
      windowsVirtualKeyCode: 13
    });
  }

  /**
   * Dispatch trusted keyboard event
   */
  async dispatchTrustedKeyEvent(options: TrustedKeyEventOptions): Promise<void> {
    if (!this.session || !this.session.attached) {
      throw new Error('Debugger not attached. Call attachToTab() first.');
    }

    console.log(`⌨️ Dispatching trusted ${options.key} key event...`);

    // Dispatch keyDown event
    await this.sendDebuggerCommand('Input.dispatchKeyEvent', {
      type: 'keyDown',
      key: options.key,
      keyCode: options.keyCode,
      code: options.code || options.key,
      windowsVirtualKeyCode: options.windowsVirtualKeyCode || options.keyCode,
      modifiers: options.modifiers || 0
    });

    // Small delay between keyDown and keyUp
    await this.delay(50);

    // Dispatch keyUp event
    await this.sendDebuggerCommand('Input.dispatchKeyEvent', {
      type: 'keyUp',
      key: options.key,
      keyCode: options.keyCode,
      code: options.code || options.key,
      windowsVirtualKeyCode: options.windowsVirtualKeyCode || options.keyCode,
      modifiers: options.modifiers || 0
    });

    console.log(`✅ Trusted ${options.key} key event dispatched`);
  }

  /**
   * Dispatch trusted mouse click event
   */
  async dispatchTrustedMouseClick(options: TrustedMouseEventOptions): Promise<void> {
    if (!this.session || !this.session.attached) {
      throw new Error('Debugger not attached. Call attachToTab() first.');
    }

    console.log(`🖱️ Dispatching trusted mouse click at (${options.x}, ${options.y})...`);

    // Dispatch mousePressed event
    await this.sendDebuggerCommand('Input.dispatchMouseEvent', {
      type: 'mousePressed',
      x: options.x,
      y: options.y,
      button: options.button || 'left',
      clickCount: options.clickCount || 1
    });

    // Small delay between press and release
    await this.delay(50);

    // Dispatch mouseReleased event
    await this.sendDebuggerCommand('Input.dispatchMouseEvent', {
      type: 'mouseReleased',
      x: options.x,
      y: options.y,
      button: options.button || 'left',
      clickCount: options.clickCount || 1
    });

    console.log(`✅ Trusted mouse click dispatched at (${options.x}, ${options.y})`);
  }

  /**
   * Send command to debugger
   */
  private async sendDebuggerCommand(method: string, params: any): Promise<any> {
    if (!this.session || !this.session.attached) {
      throw new Error('Debugger not attached');
    }

    return new Promise((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId: this.session!.tabId },
        method,
        params,
        (result) => {
          if (chrome.runtime.lastError) {
            console.error(`❌ Debugger command ${method} failed:`, chrome.runtime.lastError.message);
            reject(new Error(`Debugger command failed: ${chrome.runtime.lastError.message}`));
            return;
          }

          resolve(result);
        }
      );
    });
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Check if debugger is attached
   */
  isAttached(): boolean {
    return this.session !== null && this.session.attached;
  }

  /**
   * Get current session info
   */
  getSession(): DebuggerSession | null {
    return this.session;
  }
}

/**
 * Global debugger manager instance
 */
export const debuggerManager = new DebuggerManager();

/**
 * Convenience functions for common operations
 */

/**
 * Execute a function with debugger attached, automatically cleaning up
 */
export async function withDebugger<T>(
  tabId: number,
  operation: (manager: DebuggerManager) => Promise<T>
): Promise<T> {
  try {
    await debuggerManager.attachToTab(tabId);
    const result = await operation(debuggerManager);
    return result;
  } finally {
    await debuggerManager.detachFromTab();
  }
}

/**
 * Dispatch trusted Tab key to trigger timeline updates
 */
export async function dispatchTrustedTab(tabId: number): Promise<void> {
  await withDebugger(tabId, async (manager) => {
    await manager.dispatchTrustedTabKey();
  });
}

/**
 * Dispatch trusted Enter key
 */
export async function dispatchTrustedEnter(tabId: number): Promise<void> {
  await withDebugger(tabId, async (manager) => {
    await manager.dispatchTrustedEnterKey();
  });
}

/**
 * Dispatch trusted mouse click at element coordinates
 */
export async function dispatchTrustedClickOnElement(
  tabId: number,
  element: Element
): Promise<void> {
  const rect = element.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;

  await withDebugger(tabId, async (manager) => {
    await manager.dispatchTrustedMouseClick({ x, y });
  });
}
