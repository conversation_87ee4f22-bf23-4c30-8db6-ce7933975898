/**
 * Delay Utilities for Human-like Interactions
 * 
 * Provides various delay functions to mimic human behavior and avoid
 * detection by YouTube's bot protection systems.
 */

/**
 * Basic delay function
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Random delay between min and max milliseconds
 */
export function randomDelay(minMs: number = 300, maxMs: number = 700): Promise<void> {
  const delayMs = minMs + Math.random() * (maxMs - minMs);
  return delay(Math.floor(delayMs));
}

/**
 * Short random delay for quick actions (100-300ms)
 */
export function shortDelay(): Promise<void> {
  return randomDelay(100, 300);
}

/**
 * Medium random delay for normal actions (300-700ms)
 */
export function mediumDelay(): Promise<void> {
  return randomDelay(300, 700);
}

/**
 * Long random delay for complex actions (700-1200ms)
 */
export function longDelay(): Promise<void> {
  return randomDelay(700, 1200);
}

/**
 * Variable delay based on action type
 */
export function actionDelay(actionType: 'click' | 'type' | 'navigate' | 'wait'): Promise<void> {
  switch (actionType) {
    case 'click':
      return randomDelay(200, 500);
    case 'type':
      return randomDelay(100, 300);
    case 'navigate':
      return randomDelay(1000, 2000);
    case 'wait':
      return randomDelay(500, 1000);
    default:
      return mediumDelay();
  }
}

/**
 * Typing delay that varies per character (more human-like)
 */
export function typingDelay(text: string): Promise<void> {
  // Base delay + variable delay per character
  const baseDelay = 50;
  const variableDelay = text.length * (10 + Math.random() * 20);
  return delay(baseDelay + variableDelay);
}

/**
 * Progressive delay that increases with each iteration
 * Useful for retry scenarios or when waiting for elements
 */
export function progressiveDelay(attempt: number, baseMs: number = 500): Promise<void> {
  const delayMs = baseMs * Math.pow(1.5, attempt - 1);
  const maxDelay = 5000; // Cap at 5 seconds
  return delay(Math.min(delayMs, maxDelay));
}

/**
 * Jittered delay with exponential backoff
 * Good for handling rate limiting or temporary failures
 */
export function exponentialBackoff(
  attempt: number, 
  baseMs: number = 1000, 
  maxMs: number = 10000
): Promise<void> {
  const exponentialDelay = baseMs * Math.pow(2, attempt - 1);
  const jitter = Math.random() * 0.1 * exponentialDelay; // 10% jitter
  const totalDelay = Math.min(exponentialDelay + jitter, maxMs);
  return delay(totalDelay);
}

/**
 * Wait for a condition to be true with timeout and progressive delays
 */
export async function waitForCondition(
  condition: () => boolean | Promise<boolean>,
  timeoutMs: number = 10000,
  checkIntervalMs: number = 100
): Promise<boolean> {
  const startTime = Date.now();
  let attempt = 1;

  while (Date.now() - startTime < timeoutMs) {
    try {
      const result = await condition();
      if (result) {
        return true;
      }
    } catch (error) {
      console.warn(`Condition check failed on attempt ${attempt}:`, error);
    }

    // Use progressive delay for retries
    await progressiveDelay(attempt, checkIntervalMs);
    attempt++;
  }

  return false;
}

/**
 * Simulate human reading time based on text length
 */
export function readingDelay(text: string, wordsPerMinute: number = 200): Promise<void> {
  const words = text.split(/\s+/).length;
  const readingTimeMs = (words / wordsPerMinute) * 60 * 1000;
  
  // Add some randomness (±20%)
  const jitter = readingTimeMs * 0.2 * (Math.random() - 0.5);
  const totalDelay = Math.max(500, readingTimeMs + jitter); // Minimum 500ms
  
  return delay(totalDelay);
}

/**
 * Simulate human decision-making time
 */
export function decisionDelay(): Promise<void> {
  // Humans typically take 1-3 seconds to make simple decisions
  return randomDelay(1000, 3000);
}

/**
 * Batch delay for processing multiple items
 * Adds variation to avoid predictable patterns
 */
export function batchDelay(itemIndex: number, totalItems: number): Promise<void> {
  // Slightly longer delays for first and last items
  const isFirstOrLast = itemIndex === 0 || itemIndex === totalItems - 1;
  const baseDelay = isFirstOrLast ? 800 : 500;
  const variableDelay = Math.random() * 400;
  
  return delay(baseDelay + variableDelay);
}

/**
 * Debug function to log delay information
 */
export function logDelay(delayMs: number, context: string): void {
  console.log(`[Delay] ${context}: ${delayMs}ms`);
}

/**
 * Delay with logging for debugging
 */
export async function debugDelay(
  ms: number, 
  context: string = 'Unknown'
): Promise<void> {
  logDelay(ms, context);
  return delay(ms);
}
