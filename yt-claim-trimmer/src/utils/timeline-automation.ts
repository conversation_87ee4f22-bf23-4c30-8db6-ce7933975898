/**
 * Enhanced Timeline Automation with Debugger API Support
 * 
 * This module provides advanced timeline manipulation using Chrome's Debugger API
 * to dispatch trusted events that can bypass YouTube Studio's anti-automation protection.
 */

import { debuggerManager, dispatchTrustedTab, withDebugger } from './debugger-api';
import { delay } from './timing';

export interface TimelineUpdateResult {
  success: boolean;
  method: 'trusted_events' | 'fallback_manual' | 'failed';
  message: string;
  timelineUpdated: boolean;
}

export interface TimestampInputs {
  startInput: HTMLInputElement;
  endInput: HTMLInputElement;
  allInputs: HTMLInputElement[];
}

/**
 * Enhanced timeline automation class with debugger API support
 */
export class TimelineAutomation {
  private tabId: number;

  constructor(tabId: number) {
    this.tabId = tabId;
  }

  /**
   * Set timestamp values and trigger timeline update using trusted events
   */
  async setTimestampsWithTrustedEvents(
    startTime: string,
    endTime: string
  ): Promise<TimelineUpdateResult> {
    console.log('🚀 Attempting timeline update with trusted events...');
    console.log(`Setting times: ${startTime} → ${endTime}`);

    try {
      // Step 1: Find timestamp inputs
      const inputs = this.findTimestampInputs();
      if (!inputs) {
        return {
          success: false,
          method: 'failed',
          message: 'Could not find timestamp inputs',
          timelineUpdated: false
        };
      }

      // Step 2: Set values programmatically (this part works)
      console.log('📝 Setting timestamp values...');
      await this.setInputValue(inputs.startInput, startTime);
      await this.setInputValue(inputs.endInput, endTime);

      // Step 3: Use debugger API to dispatch trusted Tab event
      console.log('⌨️ Dispatching trusted Tab event to trigger timeline update...');
      
      // Focus the end input first (simulating manual workflow)
      inputs.endInput.focus();
      await delay(100);

      // Dispatch trusted Tab key event
      await dispatchTrustedTab(this.tabId);
      
      // Wait for timeline to potentially update
      await delay(500);

      // Step 4: Verify timeline update
      const timelineUpdated = await this.verifyTimelineUpdate();
      
      if (timelineUpdated) {
        console.log('✅ Timeline updated successfully with trusted events!');
        return {
          success: true,
          method: 'trusted_events',
          message: 'Timeline updated using trusted Tab event',
          timelineUpdated: true
        };
      } else {
        console.log('⚠️ Timeline did not update with trusted events, trying alternative...');
        return await this.fallbackToMouseClicks(inputs, startTime, endTime);
      }

    } catch (error) {
      console.error('❌ Trusted events failed:', error);
      return {
        success: false,
        method: 'failed',
        message: `Trusted events failed: ${error}`,
        timelineUpdated: false
      };
    }
  }

  /**
   * Fallback method: Use trusted mouse clicks to simulate focus changes
   */
  private async fallbackToMouseClicks(
    inputs: TimestampInputs,
    startTime: string,
    endTime: string
  ): Promise<TimelineUpdateResult> {
    console.log('🖱️ Trying fallback: trusted mouse clicks for focus changes...');

    try {
      await withDebugger(this.tabId, async (manager) => {
        // Click start input
        const startRect = inputs.startInput.getBoundingClientRect();
        await manager.dispatchTrustedMouseClick({
          x: startRect.left + startRect.width / 2,
          y: startRect.top + startRect.height / 2
        });

        await delay(200);

        // Click end input
        const endRect = inputs.endInput.getBoundingClientRect();
        await manager.dispatchTrustedMouseClick({
          x: endRect.left + endRect.width / 2,
          y: endRect.top + endRect.height / 2
        });

        await delay(200);

        // Try Tab key after clicks
        await manager.dispatchTrustedTabKey();
      });

      await delay(500);

      const timelineUpdated = await this.verifyTimelineUpdate();
      
      if (timelineUpdated) {
        console.log('✅ Timeline updated with trusted mouse clicks!');
        return {
          success: true,
          method: 'trusted_events',
          message: 'Timeline updated using trusted mouse clicks',
          timelineUpdated: true
        };
      } else {
        console.log('⚠️ Trusted mouse clicks also failed, falling back to manual...');
        return {
          success: false,
          method: 'fallback_manual',
          message: 'Trusted events failed, manual Tab press required',
          timelineUpdated: false
        };
      }

    } catch (error) {
      console.error('❌ Mouse click fallback failed:', error);
      return {
        success: false,
        method: 'failed',
        message: `Mouse click fallback failed: ${error}`,
        timelineUpdated: false
      };
    }
  }

  /**
   * Find timestamp inputs in the cut dialog
   */
  private findTimestampInputs(): TimestampInputs | null {
    const allInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
    const visibleInputs = Array.from(allInputs).filter(input => 
      (input as HTMLElement).offsetParent !== null
    ) as HTMLInputElement[];

    if (visibleInputs.length < 2) {
      console.error(`❌ Need at least 2 visible timestamp inputs, found ${visibleInputs.length}`);
      return null;
    }

    return {
      startInput: visibleInputs[0],
      endInput: visibleInputs[1],
      allInputs: visibleInputs
    };
  }

  /**
   * Set value in input field with proper event triggering
   */
  private async setInputValue(input: HTMLInputElement, value: string): Promise<void> {
    // Focus the input
    input.focus();
    await delay(50);

    // Clear existing content
    input.select();
    await delay(50);

    // Set new value
    input.value = value;
    input.dispatchEvent(new Event('input', { bubbles: true }));
    input.dispatchEvent(new Event('change', { bubbles: true }));
    
    await delay(100);
  }

  /**
   * Verify if timeline brackets have updated
   * This is a heuristic check - in a real implementation, you'd check for visual changes
   */
  private async verifyTimelineUpdate(): Promise<boolean> {
    // For now, we'll return false to test the fallback mechanisms
    // In a real implementation, this would check for timeline bracket position changes
    // or other visual indicators that the timeline has updated
    
    console.log('🔍 Checking if timeline brackets updated...');
    
    // TODO: Implement actual timeline verification logic
    // This could check for:
    // - Timeline bracket position changes
    // - CSS class changes on timeline elements
    // - Timeline scrubber position
    // - Any visual indicators of timeline state change
    
    // For testing purposes, we'll simulate verification
    await delay(200);
    
    // Return false initially to test fallback mechanisms
    // Change this to true when we have proper verification logic
    return false;
  }

  /**
   * Get current tab ID
   */
  getTabId(): number {
    return this.tabId;
  }
}

/**
 * Convenience function to create timeline automation instance
 */
export async function createTimelineAutomation(): Promise<TimelineAutomation> {
  // Get current tab ID
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
  if (!tab.id) {
    throw new Error('Could not get current tab ID');
  }
  
  return new TimelineAutomation(tab.id);
}

/**
 * Quick test function for trusted events
 */
export async function testTrustedEvents(): Promise<void> {
  console.log('🧪 Testing trusted events...');
  
  try {
    const automation = await createTimelineAutomation();
    const result = await automation.setTimestampsWithTrustedEvents('00:05:00', '00:08:00');
    
    console.log('Test result:', result);
    
    if (result.success) {
      console.log('🎉 Trusted events test PASSED!');
    } else {
      console.log('⚠️ Trusted events test failed, but this is expected during initial testing');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}
