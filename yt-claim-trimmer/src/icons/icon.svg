<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="#FF0000"/>
  <circle cx="64" cy="64" r="56" fill="#FFFFFF"/>
  
  <!-- Scissors icon -->
  <g transform="translate(32, 32)">
    <!-- Left scissor blade -->
    <ellipse cx="20" cy="20" rx="8" ry="12" fill="#FF0000" transform="rotate(-30 20 20)"/>
    <ellipse cx="20" cy="20" rx="4" ry="8" fill="#FFFFFF" transform="rotate(-30 20 20)"/>
    
    <!-- Right scissor blade -->
    <ellipse cx="44" cy="20" rx="8" ry="12" fill="#FF0000" transform="rotate(30 44 20)"/>
    <ellipse cx="44" cy="20" rx="4" ry="8" fill="#FFFFFF" transform="rotate(30 44 20)"/>
    
    <!-- Center pivot -->
    <circle cx="32" cy="32" r="4" fill="#333333"/>
    
    <!-- Cut lines -->
    <line x1="16" y1="48" x2="48" y2="48" stroke="#FF0000" stroke-width="3" stroke-dasharray="4,2"/>
    <line x1="20" y1="52" x2="44" y2="52" stroke="#FF0000" stroke-width="2" stroke-dasharray="3,2"/>
    
    <!-- YouTube play button hint -->
    <polygon points="24,40 24,56 36,48" fill="#FF0000" opacity="0.7"/>
  </g>
  
  <!-- Border -->
  <circle cx="64" cy="64" r="60" fill="none" stroke="#E0E0E0" stroke-width="2"/>
</svg>
