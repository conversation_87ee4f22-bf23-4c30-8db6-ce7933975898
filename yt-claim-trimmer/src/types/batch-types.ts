/**
 * Batch Mode TypeScript Interfaces for ClaimCutter Extension
 * 
 * Defines all types and interfaces needed for batch processing of copyright videos
 * from YouTube Studio channel content pages.
 */

import { TimestampPair } from '../utils/timestamp-parser';

/**
 * Individual video in a batch operation
 */
export interface BatchVideo {
  videoId: string;
  title: string;
  copyrightUrl: string;
  editorUrl: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  index: number; // Position in batch (1-based)
  timestamps?: TimestampPair[];
  errors?: string[];
  processedAt?: number; // Timestamp when processing completed
  claimsFound?: number; // Number of copyright claims detected
  cutsApplied?: number; // Number of cuts successfully applied
}

/**
 * Complete batch operation state
 */
export interface BatchOperation {
  id: string;
  videos: BatchVideo[];
  currentIndex: number;
  startedAt: number;
  completedAt?: number;
  settings: BatchSettings;
  totalClaimsFound: number;
  totalCutsApplied: number;
  successCount: number;
  failureCount: number;
}

/**
 * Batch processing settings (extends individual video settings)
 */
export interface BatchSettings {
  mergeBuffer: number; // 5-30 seconds
  turboMode: boolean; // Full automation including save
  autoSave: boolean; // Auto-click save button
  autoConfirm: boolean; // Auto-click confirmation dialog (default: true)
  autoCopyrightFilter: boolean; // Auto-activate copyright filter (default: true)
  maxRetries: number; // Retry failed videos
  delayBetweenVideos: number; // Delay between video processing (ms)
}

/**
 * Video discovery results from channel content page
 */
export interface BatchDiscoveryResult {
  videos: BatchVideo[];
  filterActive: boolean;
  filterMethod: 'URL' | 'Chip' | 'None' | 'redirect_pending';
  totalFound: number;
  discoveredAt: number;
  redirected?: boolean;
  message?: string;
  error?: string;
}

/**
 * Real-time progress tracking for batch operations
 */
export interface BatchProgress {
  operationId: string;
  currentVideo: number;
  totalVideos: number;
  currentVideoTitle: string;
  currentStatus: string;
  overallProgress: number; // 0-100
  estimatedTimeRemaining?: number; // seconds
}

/**
 * Result of processing a single video in batch mode
 */
export interface VideoProcessingResult {
  success: boolean;
  claimsFound: number;
  cutsApplied: number;
  errors: string[];
  processingTime?: number; // milliseconds
}

/**
 * Copyright filter validation result
 */
export interface FilterValidationResult {
  isActive: boolean;
  method: 'URL' | 'Chip' | 'None';
  videoCount: number;
}

/**
 * Default batch settings with full automation enabled
 */
export const DEFAULT_BATCH_SETTINGS: BatchSettings = {
  mergeBuffer: 15, // 15 seconds default
  turboMode: true, // Full automation enabled by default
  autoSave: true, // Auto-click save button
  autoConfirm: true, // Auto-click confirmation dialog (NEW - default ON)
  autoCopyrightFilter: true, // Auto-activate copyright filter (NEW - default ON)
  maxRetries: 2, // Retry failed videos twice
  delayBetweenVideos: 2000 // 2 second delay between videos
};

/**
 * Settings descriptions for UI tooltips
 */
export const SETTING_DESCRIPTIONS = {
  autoCopyrightFilter: "Automatically activates YouTube's copyright filter when you click 'Discover Videos'. Ensures only copyright-affected videos are processed.",
  turboMode: "Enables full automation including automatic saving and navigation between videos.",
  autoConfirm: "Automatically clicks confirmation dialogs after saving. Enables completely hands-free batch processing.",
  mergeBuffer: "Time buffer for merging overlapping timestamp ranges. Higher values ensure complete claim removal.",
  autoSave: "Automatically clicks the Save button after applying cuts to each video.",
  maxRetries: "Number of times to retry processing a video if it fails.",
  delayBetweenVideos: "Delay between processing videos to maintain human-like behavior."
};

/**
 * Page type detection for UI mode switching
 */
export type PageType = 'individual' | 'batch' | 'unsupported';

/**
 * Batch mode message types for background script communication
 */
export interface StartBatchOperationMessage {
  action: 'START_BATCH_OPERATION';
  videos: BatchVideo[];
  settings: BatchSettings;
}

export interface BatchProgressMessage {
  action: 'BATCH_PROGRESS_UPDATE';
  operationId: string;
  currentVideo: number;
  totalVideos: number;
  currentVideoStatus: string;
  progress: BatchProgress;
}

export interface DiscoverVideosMessage {
  action: 'DISCOVER_VIDEOS';
  settings: BatchSettings;
}

export interface BatchCompleteMessage {
  action: 'BATCH_COMPLETE';
  operationId: string;
  results: BatchOperation;
}

/**
 * Union type for all batch-related messages
 */
export type BatchMessage = 
  | StartBatchOperationMessage 
  | BatchProgressMessage 
  | DiscoverVideosMessage 
  | BatchCompleteMessage;
