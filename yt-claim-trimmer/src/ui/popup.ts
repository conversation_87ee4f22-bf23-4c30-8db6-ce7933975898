/**
 * Enhanced Popup UI Logic for ClaimCutter Chrome Extension
 * Handles user interactions, state management, and real-time progress updates
 */

import { SettingsModal } from './settings-modal.js';
import { ClaimCutterSettings, loadSettings } from '../utils/settings.js';
import { getVersionString, getUIVersion } from '../utils/version.js';
import {
  BatchVideo,
  BatchDiscoveryResult,
  BatchSettings,
  DEFAULT_BATCH_SETTINGS,
  PageType
} from '../types/batch-types.js';

// Inline error handling to avoid import issues
interface ExtensionError {
  code: string;
  message: string;
  details?: string;
  timestamp: number;
  context?: string;
  recoverable: boolean;
  userMessage: string;
}

function createError(
  code: string,
  message: string,
  details?: string,
  context?: string
): ExtensionError {
  return {
    code,
    message,
    details,
    timestamp: Date.now(),
    context,
    recoverable: code !== 'EXTENSION_ERROR',
    userMessage: message,
  };
}

function formatErrorForUser(error: ExtensionError): string {
  return error.userMessage + (error.details ? `\n\nTechnical details: ${error.details}` : '');
}

function getRecoveryActions(error: ExtensionError): any[] {
  // Simplified recovery actions
  if (error.code === 'INVALID_PAGE') {
    return [{
      label: 'Open YouTube Studio',
      action: () => window.open('https://studio.youtube.com', '_blank'),
      primary: true,
    }];
  }
  return [];
}

// DOM Elements
const pageStatusDiv = document.getElementById('page-status') as HTMLDivElement;
const turboIndicator = document.getElementById('turbo-indicator') as HTMLDivElement;
const startBtn = document.getElementById('start-btn') as HTMLButtonElement;
const startBtnText = document.getElementById('start-btn-text') as HTMLSpanElement;
const dryRunBtn = document.getElementById('dry-run-btn') as HTMLButtonElement;
const progressSection = document.getElementById('progress-section') as HTMLDivElement;
const progressFill = document.getElementById('progress-fill') as HTMLDivElement;
const progressText = document.getElementById('progress-text') as HTMLDivElement;
const statusDiv = document.getElementById('status') as HTMLDivElement;
const statsDiv = document.getElementById('stats') as HTMLDivElement;
const statsClaims = document.getElementById('stats-claims') as HTMLSpanElement;
const statsCuts = document.getElementById('stats-cuts') as HTMLSpanElement;
const statsTime = document.getElementById('stats-time') as HTMLSpanElement;
const helpLink = document.getElementById('help-link') as HTMLAnchorElement;
const githubLink = document.getElementById('github-link') as HTMLAnchorElement;
const settingsLink = document.getElementById('settings-link') as HTMLAnchorElement;
const testDebuggerLink = document.getElementById('test-debugger-link') as HTMLAnchorElement;
const versionDisplay = document.getElementById('version-display') as HTMLSpanElement;

// Enhanced State management with batch mode support
interface PopupState {
  isProcessing: boolean;
  currentPage: string;
  isValidPage: boolean;
  pageType: 'copyright' | 'editor' | 'channel' | 'other' | 'unknown';
  uiMode: 'individual' | 'batch'; // NEW: UI mode switching
  batchPaused: boolean; // NEW: Track if batch processing is paused
  progress: {
    phase: 'idle' | 'checking' | 'collecting' | 'applying' | 'complete' | 'error' | 'collection_complete' | 'discovering' | 'batch_processing';
    percentage: number;
    message: string;
  };
  stats: {
    claimsFound: number;
    cutsApplied: number;
    timeSaved: string;
  };
  collectionResults?: {
    count: number;
    rawCount?: number; // Add rawCount for persistent state
    totalClaims?: number; // Add totalClaims for persistent state
    timestamps: any[];
    errors: string[];
    completedAt: number;
  };
  // NEW: Batch mode state
  batchDiscovery?: BatchDiscoveryResult;
  batchSettings?: BatchSettings;
  lastError?: ExtensionError;
}

let state: PopupState = {
  isProcessing: false,
  currentPage: '',
  isValidPage: false,
  pageType: 'unknown',
  uiMode: 'individual', // Default to individual mode
  batchPaused: false, // Initialize batch paused state
  progress: {
    phase: 'idle',
    percentage: 0,
    message: '',
  },
  stats: {
    claimsFound: 0,
    cutsApplied: 0,
    timeSaved: '0s',
  },
  batchSettings: DEFAULT_BATCH_SETTINGS, // Initialize with default batch settings
};

// Settings and UI components
let currentSettings: ClaimCutterSettings;
let settingsModal: SettingsModal;

/**
 * Initialize popup when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', async () => {
  console.log(getVersionString('Popup'));
  console.log('ClaimCutter popup loaded');

  // Clear any existing content and set initial checking status
  console.log('🧹 Initial page status content:', pageStatusDiv.innerHTML);
  pageStatusDiv.innerHTML = ''; // Clear any residual content
  pageStatusDiv.className = 'page-status checking';
  console.log('🧹 After clearing:', pageStatusDiv.innerHTML);
  updatePageStatus('checking', '🔍', 'Checking page compatibility...');
  console.log('🧹 After initial update:', pageStatusDiv.innerHTML);

  // Load settings first and wait for completion
  await loadCurrentSettings();

  // Initialize settings modal
  settingsModal = new SettingsModal(onSettingsChanged);

  // Check current page
  await checkCurrentPage();

  // Set up event listeners
  setupEventListeners();

  // Set up message listener for real-time updates
  setupMessageListener();

  // Load any saved state
  await loadState();

  // Update UI based on current state
  updateUI();

  // Update version display
  updateVersionDisplay();
});

/**
 * Update version display in the UI
 */
function updateVersionDisplay(): void {
  const currentVersion = getUIVersion();
  if (versionDisplay) {
    versionDisplay.textContent = currentVersion;
    console.log(`🔢 Version display updated to: ${currentVersion}`);
  }
}

/**
 * Enhanced page checking with detailed validation
 */
async function checkCurrentPage(): Promise<void> {
  console.log('🔍 Starting page compatibility check...');

  try {
    // Don't show progress for page checking - we use page-status instead
    // updateProgress('checking', 0, 'Checking page compatibility...');

    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    console.log('📄 Current tab:', tab?.url);

    if (!tab?.url) {
      console.log('❌ No tab URL found');
      state.isValidPage = false;
      state.pageType = 'unknown';
      updatePageStatus('invalid', '❌', 'Cannot access current page');
      updateProgress('idle', 0, '');
      return;
    }

    state.currentPage = tab.url;

    // Enhanced page type detection with batch mode support
    const isYouTubeStudio = tab.url.includes('studio.youtube.com');
    const isCopyrightPage = tab.url.includes('/copyright');
    const isEditorPage = tab.url.includes('/editor');
    const isChannelContentPage = tab.url.includes('/channel/') && tab.url.includes('/videos');

    console.log('🔍 Page analysis:', { isYouTubeStudio, isCopyrightPage, isEditorPage, isChannelContentPage });

    if (!isYouTubeStudio) {
      console.log('❌ Not a YouTube Studio page');
      state.isValidPage = false;
      state.pageType = 'other';
      state.uiMode = 'individual';
      updatePageStatus('invalid', '❌', 'Please navigate to YouTube Studio first');
    } else if (isChannelContentPage) {
      console.log('✅ Channel content page detected - switching to batch mode');
      state.isValidPage = true;
      state.pageType = 'channel';
      state.uiMode = 'batch';
      updatePageStatus('valid', '🎬', 'Channel content page detected - ready for batch processing');
    } else if (isCopyrightPage) {
      console.log('✅ Copyright page detected');
      state.isValidPage = true;
      state.pageType = 'copyright';
      state.uiMode = 'individual';
      updatePageStatus('valid', '✅', 'Copyright page detected - ready for claim collection');
    } else if (isEditorPage) {
      console.log('✅ Editor page detected');
      state.isValidPage = true;
      state.pageType = 'editor';
      state.uiMode = 'individual';
      updatePageStatus('valid', '✅', 'Editor page detected - ready for cut application');
    } else {
      console.log('❌ Other YouTube Studio page');
      state.isValidPage = false;
      state.pageType = 'other';
      state.uiMode = 'individual';
      updatePageStatus('invalid', '❌', 'Navigate to Copyright, Editor, or Channel Content tab');
    }

    // Page status is handled by updatePageStatus calls above
    console.log('✅ Page compatibility check completed');

    // Skip background validation for now to avoid hanging
    // The local validation is sufficient for determining page type

  } catch (error) {
    console.error('❌ Failed to check current page:', error);
    state.isValidPage = false;
    state.pageType = 'unknown';
    state.lastError = createError(
      'PAGE_LOAD_FAILED',
      'Failed to check current page',
      error instanceof Error ? error.message : String(error)
    );
    updatePageStatus('invalid', '❌', 'Failed to check page compatibility');
  }
}

/**
 * Set up event listeners for UI interactions
 */
function setupEventListeners(): void {
  startBtn.addEventListener('click', () => handleStart(false));
  dryRunBtn.addEventListener('click', () => handleStart(true));

  helpLink.addEventListener('click', (e) => {
    e.preventDefault();
    chrome.tabs.create({
      url: 'https://github.com/your-repo/claimcutter#readme'
    });
  });

  githubLink.addEventListener('click', (e) => {
    e.preventDefault();
    chrome.tabs.create({
      url: 'https://github.com/your-repo/claimcutter'
    });
  });

  settingsLink.addEventListener('click', (e) => {
    e.preventDefault();
    settingsModal.show();
  });

  testDebuggerLink.addEventListener('click', (e) => {
    e.preventDefault();
    handleTestDebugger();
  });
}

/**
 * Set up message listener for real-time updates from background script
 */
function setupMessageListener(): void {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Popup received message:', message);

    // Only handle messages that are specifically meant for the popup
    // Don't interfere with content script <-> background script communication
    switch (message.action) {
      case 'PROGRESS_UPDATE':
        updateProgress(
          message.phase,
          message.percentage,
          message.message
        );
        if (message.stats) {
          updateStats(message.stats);
        }
        sendResponse({ received: true });
        return true;

      case 'COLLECTION_COMPLETE':
        handleCollectionComplete(message.data);
        sendResponse({ received: true });
        return true;

      case 'OPERATION_COMPLETE':
        console.log('🎉 Popup received OPERATION_COMPLETE:', message.data);
        handleOperationComplete(message.data);
        sendResponse({ received: true });
        return true;

      case 'OPERATION_ERROR':
        handleOperationError(message.error);
        sendResponse({ received: true });
        return true;

      case 'PAGE_VALIDATED':
        handlePageValidation(message.isValid, message.details);
        sendResponse({ received: true });
        return true;

      // NEW: Batch mode message handlers
      case 'BATCH_DISCOVERY_COMPLETE':
        handleBatchDiscoveryComplete(message.data);
        sendResponse({ received: true });
        return true;

      case 'BATCH_PROGRESS_UPDATE':
        handleBatchProgressUpdate(message);
        sendResponse({ received: true });
        return true;

      case 'BATCH_COMPLETE':
        handleBatchComplete(message.results);
        sendResponse({ received: true });
        return true;

      default:
        // Don't respond to messages not meant for popup
        // This allows content script <-> background script communication to work properly
        console.log('Popup ignoring message not meant for popup:', message);
        return false; // Don't keep message channel open
    }
  });
}

/**
 * Enhanced start button handling with batch mode support
 */
async function handleStart(dryRun: boolean): Promise<void> {
  if (state.isProcessing) {
    console.log('Operation already in progress');
    return;
  }

  if (!state.isValidPage) {
    showError(createError(
      'INVALID_PAGE',
      'Cannot start operation on invalid page',
      `Current page type: ${state.pageType}`
    ));
    return;
  }

  // Handle batch mode actions
  if (state.uiMode === 'batch') {
    const hasBatchDiscovery = state.batchDiscovery && state.batchDiscovery.videos.length > 0;

    if (hasBatchDiscovery) {
      // Start batch processing
      await handleStartBatch();
    } else {
      // Discover videos
      await handleDiscoverVideos();
    }
    return;
  }

  // Handle individual mode (existing logic)
  try {
    // Get current tab ID
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab || !tab.id) {
      throw new Error('No active tab found');
    }

    // Update state and UI
    state.isProcessing = true;
    state.progress.phase = 'collecting';
    updateProgress('collecting', 0, dryRun ? 'Starting dry run...' : 'Starting operation...');
    updateUI();

    // Send message to background script with tab ID and settings
    const response = await chrome.runtime.sendMessage({
      action: 'START_OPERATION',
      dryRun,
      pageType: state.pageType,
      url: state.currentPage,
      tabId: tab.id, // Include the tab ID!
      settings: currentSettings, // Include current settings
    });

    if (response && response.error) {
      // response.error is already a serialized error object from background script
      console.error('Background script returned error:', response.error);
      handleOperationError(response.error);
      return;
    }

  } catch (error) {
    console.error('Failed to start operation:', error);
    const extensionError = createError(
      'EXTENSION_ERROR',
      'Failed to start operation',
      error instanceof Error ? error.message : String(error)
    );
    handleOperationError(extensionError);
  }
}

/**
 * Update progress display
 */
function updateProgress(
  phase: PopupState['progress']['phase'],
  percentage: number,
  message: string
): void {
  state.progress = { phase, percentage, message };

  // Update progress bar
  progressFill.style.width = `${Math.max(0, Math.min(100, percentage))}%`;
  progressText.textContent = message;

  // Show/hide progress section
  if (phase === 'idle') {
    progressSection.classList.add('hidden');
  } else {
    progressSection.classList.remove('hidden');
  }

  // Add batch control buttons if batch processing is active
  if (phase === 'batch_processing') {
    showBatchControlButtons();
  } else {
    hideBatchControlButtons();
  }

  // Update status message
  if (phase === 'error') {
    showStatus('error', message);
  } else if (phase === 'complete') {
    showStatus('success', message);
  } else if (phase !== 'idle') {
    showStatus('info', message);
  }
}

/**
 * Update page status indicator
 */
function updatePageStatus(
  status: 'valid' | 'invalid' | 'checking',
  icon: string,
  message: string
): void {
  console.log(`🔄 Updating page status: ${status} - ${icon} ${message}`);
  pageStatusDiv.className = `page-status ${status}`;

  if (status === 'checking') {
    // For checking status, include spinner and single line
    pageStatusDiv.innerHTML = `<div style="display: flex; align-items: center; justify-content: center; gap: 8px;"><span class="spinner"></span>${icon} ${message}</div>`;
  } else if (status === 'valid' && message.includes('Copyright page detected')) {
    // Special formatting for copyright page detected message
    pageStatusDiv.innerHTML = `
      <div style="font-weight: 600; margin-bottom: 4px;">${icon} Copyright Page Detected</div>
      <div style="font-size: 12px; opacity: 0.9;">Ready for Claim Collection</div>
    `;
  } else {
    // For other valid/invalid status, show icon and message
    pageStatusDiv.innerHTML = `${icon} ${message}`;
  }

  console.log(`✅ Page status updated. New content: "${pageStatusDiv.innerHTML}"`);
}

/**
 * Update statistics display
 */
function updateStats(stats: Partial<PopupState['stats']>): void {
  Object.assign(state.stats, stats);

  statsClaims.textContent = state.stats.claimsFound.toString();
  statsCuts.textContent = state.stats.cutsApplied.toString();
  statsTime.textContent = state.stats.timeSaved;

  // Show stats if we have data
  if (state.stats.claimsFound > 0 || state.stats.cutsApplied > 0) {
    statsDiv.classList.remove('hidden');
  }
}

/**
 * Handle collection completion (NEW)
 */
function handleCollectionComplete(data: any): void {
  state.isProcessing = false;
  state.progress.phase = 'collection_complete';
  state.progress.percentage = 100;
  state.progress.message = data.count > 0 ?
    `Collection complete! Found ${data.count} timestamp(s)` :
    'Collection complete - no timestamps found';

  // Store collection results
  state.collectionResults = {
    count: data.count,
    rawCount: data.rawCount, // Store rawCount for persistent state
    totalClaims: data.totalClaims, // Store totalClaims for persistent state
    timestamps: data.timestamps,
    errors: data.errors,
    completedAt: Date.now()
  };

  if (data.stats) {
    updateStats(data.stats);
  }

  // Save state for persistence
  saveState();

  updateUI();
  showCollectionResults(data);
}

/**
 * Handle operation completion
 */
function handleOperationComplete(data: any): void {
  console.log('🎉 handleOperationComplete called with data:', data);
  state.isProcessing = false;
  updateProgress('complete', 100, 'Operation completed successfully!');

  if (data.stats) {
    updateStats(data.stats);
  }

  // Clear collection results after successful application
  state.collectionResults = undefined;
  saveState();

  updateUI();
  console.log('🎉 UI updated after operation completion');

  // Auto-hide progress after a delay
  setTimeout(() => {
    if (state.progress.phase === 'complete') {
      updateProgress('idle', 0, '');
    }
  }, 5000);
}

/**
 * Handle operation errors with recovery options
 */
function handleOperationError(error: ExtensionError): void {
  state.isProcessing = false;
  state.lastError = error;
  updateProgress('error', 0, 'Operation failed');
  showError(error);
  updateUI();
}

/**
 * Handle page validation results
 */
function handlePageValidation(isValid: boolean, details?: string): void {
  if (isValid) {
    updatePageStatus('valid', '✅', details || 'Page validated successfully');
  } else {
    updatePageStatus('invalid', '❌', details || 'Page validation failed');
    state.isValidPage = false;
  }
  updateUI();
}

/**
 * NEW: Handle batch video discovery completion
 */
function handleBatchDiscoveryComplete(data: BatchDiscoveryResult): void {
  state.batchDiscovery = data;
  state.isProcessing = false;
  state.progress.phase = 'idle';

  // Save batch discovery results for persistence
  saveState();

  console.log(`🎯 Batch discovery complete: ${data.videos.length} videos found`);

  // Handle redirect case - auto-retry discovery after filter activation
  if (data.redirected) {
    console.log('🔄 Page redirected to activate filter - retrying discovery in 3 seconds...');
    updateProgress('processing', 50, 'Filter activated - retrying discovery...');

    setTimeout(() => {
      console.log('🔄 Retrying discovery after filter activation...');
      handleDiscoverVideos();
    }, 3000); // Increased to 3 seconds to allow page content to fully load
    return;
  }

  if (data.videos.length === 0) {
    updateProgress('error', 0, 'No copyright videos found');
    showStatus('warning', 'No copyright videos found. Try activating the copyright filter manually.');
  } else {
    updateProgress('complete', 100, `Discovery complete! Found ${data.videos.length} videos`);
    showBatchDiscoveryResults(data);
  }

  updateUI();
}

/**
 * NEW: Handle batch processing progress updates
 */
function handleBatchProgressUpdate(message: any): void {
  console.log('📊 Handling batch progress update:', message);

  // Extract progress data from message
  let currentIndex, totalVideos, currentVideo, status;

  if (message.currentIndex !== undefined) {
    // Direct structure: { currentIndex, totalVideos, currentVideo, status }
    currentIndex = message.currentIndex;
    totalVideos = message.totalVideos;
    currentVideo = message.currentVideo;
    status = message.status;
  } else if (message.progress) {
    // Nested structure: { progress: { currentIndex, totalVideos, ... } }
    currentIndex = message.progress.currentIndex;
    totalVideos = message.progress.totalVideos;
    currentVideo = message.progress.currentVideo;
    status = message.progress.status;
  } else {
    console.error('Unknown batch progress message structure:', message);
    return;
  }

  // Calculate progress percentage
  const overallProgress = Math.round(((currentIndex + 1) / totalVideos) * 100);

  state.progress.phase = 'batch_processing';
  state.progress.percentage = overallProgress;
  state.progress.message = `Processing ${currentIndex + 1}/${totalVideos}: ${currentVideo?.title || currentVideo?.id || 'Unknown'}`;

  updateProgress('batch_processing', overallProgress, `Processing video ${currentIndex + 1}/${totalVideos}`);

  console.log(`📊 Batch progress: ${currentIndex + 1}/${totalVideos} (${overallProgress}%) - ${status}`);
}

/**
 * NEW: Handle batch processing completion
 */
function handleBatchComplete(results: any): void {
  state.isProcessing = false;
  state.progress.phase = 'complete';
  state.progress.percentage = 100;

  // Safely extract properties with defaults
  const successCount = results?.successCount || 0;
  const failureCount = results?.failureCount || 0;
  const totalClaimsFound = results?.totalClaimsFound || 0;
  const totalCutsApplied = results?.totalCutsApplied || 0;
  const totalVideos = successCount + failureCount;

  const statusMessage = results?.stopped ?
    `Batch stopped! ${successCount}/${totalVideos} videos processed` :
    `Batch complete! ${successCount}/${totalVideos} videos processed`;

  updateProgress('complete', 100, statusMessage);

  // Update stats
  updateStats({
    claimsFound: totalClaimsFound,
    cutsApplied: totalCutsApplied,
    timeSaved: results?.completedAt && results?.startedAt ?
      `${Math.round((results.completedAt - results.startedAt) / 1000)}s` :
      'N/A'
  });

  showBatchResults(results);
  updateUI();

  console.log(`🎉 Batch processing ${results?.stopped ? 'stopped' : 'complete'}: ${successCount}/${totalVideos} successful`);
}

/**
 * Show error with recovery actions
 */
function showError(error: ExtensionError): void {
  const errorMessage = formatErrorForUser(error);
  const actions = getRecoveryActions(error);

  statusDiv.className = 'status status-error';
  statusDiv.innerHTML = `
    <div>${errorMessage}</div>
    ${actions.length > 0 ? `
      <div class="error-actions">
        ${actions.map(action => `
          <button class="error-action ${action.primary ? 'primary' : ''}"
                  onclick="(${action.action.toString()})()">
            ${action.label}
          </button>
        `).join('')}
      </div>
    ` : ''}
  `;
  statusDiv.classList.remove('hidden');
}

/**
 * Show collection results with timestamps
 */
function showCollectionResults(data: any): void {
  const { count, timestamps, errors = [] } = data;

  if (count === 0) {
    showStatus('warning', 'No timestamps found in copyright claims');
    return;
  }

  // Create collection results display with merge information
  const timestampList = timestamps.map((ts: any, index: number) => {
    const startTime = formatTime(ts.start);
    const endTime = formatTime(ts.end);
    const duration = formatTime(ts.end - ts.start);

    // Check if this timestamp was merged from multiple sources
    const isMerged = ts.originalCount && ts.originalCount > 1;
    const mergeInfo = isMerged ?
      `<div class="merge-info">🔗 Merged from ${ts.originalCount} sources: ${ts.sources ? ts.sources.join(', ') : 'multiple timestamps'}</div>` :
      '';

    return `<div class="timestamp-item ${isMerged ? 'merged' : ''}">
      <span class="timestamp-number">${index + 1}.</span>
      <span class="timestamp-range">${startTime} - ${endTime}</span>
      <span class="timestamp-duration">(${duration})</span>
      ${isMerged ? '<span class="merge-badge">[MERGED]</span>' : ''}
      ${mergeInfo}
    </div>`;
  }).join('');

  const errorList = errors.length > 0 ? `
    <div class="collection-errors">
      <h4>⚠️ Warnings:</h4>
      ${errors.map((error: string) => `<div class="error-item">${error}</div>`).join('')}
    </div>
  ` : '';

  // Calculate collection summary
  const rawCount = data.rawCount || count; // Use rawCount from data if available
  const totalClaims = data.totalClaims || rawCount; // Use totalClaims if available, fallback to rawCount
  const mergedCount = timestamps.filter((ts: any) => ts.originalCount && ts.originalCount > 1).length;
  const claimsProcessed = totalClaims; // Use total claims processed

  statusDiv.className = 'status status-success';
  statusDiv.innerHTML = `
    <div class="collection-results">
      <h3>✅ Collection Complete!</h3>
      <div class="collection-summary">
        <div class="summary-item">
          <span class="summary-label">Claims Processed:</span>
          <span class="summary-value">${claimsProcessed}</span>
        </div>
        ${rawCount !== count ? `
        <div class="summary-item">
          <span class="summary-label">Raw Timestamps Found:</span>
          <span class="summary-value">${rawCount}</span>
        </div>
        ` : ''}
        <div class="summary-item">
          <span class="summary-label">Final Timestamps:</span>
          <span class="summary-value">${count}</span>
        </div>
        ${mergedCount > 0 ? `
        <div class="summary-item merged-note">
          <span class="summary-label">Merged Ranges:</span>
          <span class="summary-value">${mergedCount}</span>
        </div>
        ` : ''}
      </div>
      <div class="timestamp-list">${timestampList}</div>
      ${errorList}
      <div class="collection-actions">
        <button id="apply-cuts-btn" class="apply-cuts-button">
          🎬 Apply Cuts in Editor
        </button>
        <button id="clear-results-btn" class="clear-results-button">
          🗑️ Clear Results
        </button>
      </div>
    </div>
  `;
  statusDiv.classList.remove('hidden');

  // Add event listeners for new buttons
  const applyCutsBtn = document.getElementById('apply-cuts-btn');
  const clearResultsBtn = document.getElementById('clear-results-btn');

  if (applyCutsBtn) {
    applyCutsBtn.addEventListener('click', handleApplyCuts);
  }

  if (clearResultsBtn) {
    clearResultsBtn.addEventListener('click', handleClearResults);
  }
}

/**
 * Show status message to user
 */
function showStatus(type: 'info' | 'error' | 'success' | 'warning', message: string): void {
  statusDiv.className = `status status-${type}`;
  statusDiv.textContent = message;
  statusDiv.classList.remove('hidden');
  statusDiv.classList.add('fade-in');
}

/**
 * Handle apply cuts button click
 */
async function handleApplyCuts(): Promise<void> {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    const tabId = tabs[0]?.id;

    if (!tabId) {
      showStatus('error', 'No active tab found');
      return;
    }

    // Send apply cuts message to background
    await chrome.runtime.sendMessage({
      action: 'APPLY_CUTS',
      tabId: tabId
    });

    showStatus('info', 'Navigating to editor to apply cuts...');

  } catch (error) {
    console.error('Failed to apply cuts:', error);
    showStatus('error', 'Failed to apply cuts');
  }
}

/**
 * Handle clear results button click
 */
async function handleClearResults(): Promise<void> {
  state.collectionResults = undefined;
  state.batchDiscovery = undefined; // NEW: Clear batch discovery results
  state.progress.phase = 'idle';
  state.progress.message = '';
  state.progress.percentage = 0;
  state.isProcessing = false; // Reset processing state

  // Clear storage including batch state
  await chrome.storage.session.remove([
    'claimcutter_collection_complete',
    'claimcutter_collection_results',
    'claimcutter_timestamps',
    'claimcutter_batch_discovery', // NEW: Clear batch discovery storage
    'claimcutter_batch_discovery_timestamp',
    'claimcutter_batch_settings'
  ]);

  // Clear any ongoing batch operations
  const allStorage = await chrome.storage.session.get();
  const batchOperationKeys = Object.keys(allStorage).filter(key => key.startsWith('batchOperation_'));
  if (batchOperationKeys.length > 0) {
    await chrome.storage.session.remove(batchOperationKeys);
    console.log('🧹 Cleared batch operations:', batchOperationKeys);
  }

  saveState();
  updateUI();
  statusDiv.classList.add('hidden');
}

/**
 * NEW: Show batch control buttons during batch processing
 */
function showBatchControlButtons(): void {
  // Remove any existing batch control buttons
  hideBatchControlButtons();

  // Create batch control container
  const batchControlsDiv = document.createElement('div');
  batchControlsDiv.id = 'batch-controls';
  batchControlsDiv.className = 'batch-controls';

  // Determine button states based on current state
  const isPaused = state.batchPaused;
  const isProcessing = state.isProcessing && !isPaused;

  if (isProcessing) {
    // Show Pause and Stop buttons when processing
    batchControlsDiv.innerHTML = `
      <button id="pause-batch-btn" class="batch-control-btn pause-btn">
        ⏸️ Pause
      </button>
      <button id="stop-batch-btn" class="batch-control-btn stop-btn">
        ⏹️ Stop
      </button>
    `;
  } else if (isPaused) {
    // Show Continue and Stop buttons when paused
    batchControlsDiv.innerHTML = `
      <button id="continue-batch-btn" class="batch-control-btn continue-btn">
        ▶️ Continue
      </button>
      <button id="stop-batch-btn" class="batch-control-btn stop-btn">
        ⏹️ Stop
      </button>
    `;
  }

  // Insert after progress section
  progressSection.parentNode?.insertBefore(batchControlsDiv, progressSection.nextSibling);

  // Add event listeners
  const pauseBtn = document.getElementById('pause-batch-btn');
  const continueBtn = document.getElementById('continue-batch-btn');
  const stopBtn = document.getElementById('stop-batch-btn');

  if (pauseBtn) {
    pauseBtn.addEventListener('click', handlePauseBatch);
  }
  if (continueBtn) {
    continueBtn.addEventListener('click', handleContinueBatch);
  }
  if (stopBtn) {
    stopBtn.addEventListener('click', handleStopBatch);
  }
}

/**
 * NEW: Hide batch control buttons
 */
function hideBatchControlButtons(): void {
  const existingControls = document.getElementById('batch-controls');
  if (existingControls) {
    existingControls.remove();
  }
}

/**
 * NEW: Handle pause batch button click
 */
async function handlePauseBatch(): Promise<void> {
  try {
    console.log('🔄 Pausing batch processing...');

    state.batchPaused = true;
    state.isProcessing = false;

    // Send pause message to background script
    await chrome.runtime.sendMessage({
      action: 'PAUSE_BATCH_OPERATION'
    });

    updateProgress('batch_processing', state.progress.percentage, 'Batch processing paused');
    saveState();
    updateUI();

  } catch (error) {
    console.error('Failed to pause batch:', error);
    showStatus('error', 'Failed to pause batch processing');
  }
}

/**
 * NEW: Handle continue batch button click
 */
async function handleContinueBatch(): Promise<void> {
  try {
    console.log('▶️ Continuing batch processing...');

    state.batchPaused = false;
    state.isProcessing = true;

    // Send continue message to background script
    await chrome.runtime.sendMessage({
      action: 'CONTINUE_BATCH_OPERATION'
    });

    updateProgress('batch_processing', state.progress.percentage, 'Batch processing resumed');
    saveState();
    updateUI();

  } catch (error) {
    console.error('Failed to continue batch:', error);
    showStatus('error', 'Failed to continue batch processing');
  }
}

/**
 * NEW: Handle stop batch button click
 */
async function handleStopBatch(): Promise<void> {
  try {
    console.log('⏹️ Stopping batch processing...');

    state.batchPaused = false;
    state.isProcessing = false;

    // Send stop message to background script
    await chrome.runtime.sendMessage({
      action: 'STOP_BATCH_OPERATION'
    });

    updateProgress('complete', 100, 'Batch processing stopped');
    saveState();
    updateUI();

  } catch (error) {
    console.error('Failed to stop batch:', error);
    showStatus('error', 'Failed to stop batch processing');
  }
}

/**
 * NEW: Handle reset batch button click
 * Clears all batch state but preserves settings
 */
async function handleResetBatch(): Promise<void> {
  try {
    console.log('🔄 Resetting batch state...');

    // Reset batch-related state but preserve settings
    state.batchDiscovery = undefined;
    state.batchPaused = false;
    state.isProcessing = false;
    state.progress.phase = 'idle';
    state.progress.message = '';
    state.progress.percentage = 0;

    // Clear batch storage but preserve settings
    await chrome.storage.session.remove([
      'claimcutter_batch_discovery',
      'claimcutter_batch_discovery_timestamp'
      // Note: We keep claimcutter_batch_settings to preserve user settings
    ]);

    // Clear any ongoing batch operations
    const allStorage = await chrome.storage.session.get();
    const batchOperationKeys = Object.keys(allStorage).filter(key => key.startsWith('batchOperation_'));
    if (batchOperationKeys.length > 0) {
      await chrome.runtime.sendMessage({
        action: 'CLEAR_BATCH_OPERATIONS',
        operationKeys: batchOperationKeys
      });
      await chrome.storage.session.remove(batchOperationKeys);
      console.log('🧹 Cleared batch operations:', batchOperationKeys);
    }

    saveState();
    updateUI();
    statusDiv.classList.add('hidden');

    showStatus('success', 'Batch state reset successfully');

  } catch (error) {
    console.error('Failed to reset batch:', error);
    showStatus('error', 'Failed to reset batch state');
  }
}

/**
 * NEW: Show batch discovery results
 */
function showBatchDiscoveryResults(data: BatchDiscoveryResult): void {
  const { videos, filterActive, filterMethod, totalFound } = data;

  const videoList = videos.map((video, index) => `
    <div class="batch-video-item">
      <span class="video-number">${index + 1}.</span>
      <span class="video-title">${video.title}</span>
      <span class="video-status pending">Pending</span>
    </div>
  `).join('');

  statusDiv.className = 'status status-success';
  statusDiv.innerHTML = `
    <div class="batch-discovery-results">
      <h3>🎯 Video Discovery Complete!</h3>
      <div class="discovery-summary">
        <div class="summary-item">
          <span class="summary-label">Videos Found:</span>
          <span class="summary-value">${totalFound}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Copyright Filter:</span>
          <span class="summary-value">${filterActive ? `✅ Active (${filterMethod})` : '❌ Inactive'}</span>
        </div>
      </div>
      <div class="video-list">${videoList}</div>
      <div class="batch-actions">
        <button id="start-batch-btn" class="start-batch-button">
          🚀 Start Batch Processing
        </button>
        <button id="clear-discovery-btn" class="clear-discovery-button">
          🗑️ Clear Discovery
        </button>
      </div>
    </div>
  `;
  statusDiv.classList.remove('hidden');

  // Add event listeners for batch action buttons
  const startBatchBtn = document.getElementById('start-batch-btn');
  const clearDiscoveryBtn = document.getElementById('clear-discovery-btn');

  if (startBatchBtn) {
    startBatchBtn.addEventListener('click', handleStartBatch);
  }

  if (clearDiscoveryBtn) {
    clearDiscoveryBtn.addEventListener('click', handleClearResults);
  }
}

/**
 * NEW: Show batch processing results
 */
function showBatchResults(results: any): void {
  const { videos, successCount, failureCount, totalClaimsFound, totalCutsApplied } = results;
  const totalVideos = successCount + failureCount;

  const videoList = videos.map((video: any) => {
    const statusIcon = video.status === 'completed' ? '✅' :
                      video.status === 'failed' ? '❌' :
                      video.status === 'skipped' ? '⏭️' : '⏳';

    const claimsInfo = video.claimsFound ? `${video.claimsFound} claims → ${video.cutsApplied} cuts` : 'No claims';
    const errorInfo = video.errors && video.errors.length > 0 ?
      `<div class="video-errors">${video.errors.join(', ')}</div>` : '';

    return `
      <div class="batch-video-result ${video.status}">
        <span class="video-status-icon">${statusIcon}</span>
        <span class="video-title">${video.title}</span>
        <span class="video-claims">${claimsInfo}</span>
        ${errorInfo}
      </div>
    `;
  }).join('');

  statusDiv.className = 'status status-success';
  statusDiv.innerHTML = `
    <div class="batch-results">
      <h3>🎉 Batch Processing Complete!</h3>
      <div class="batch-summary">
        <div class="summary-item">
          <span class="summary-label">Videos Processed:</span>
          <span class="summary-value">${successCount}/${totalVideos}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Total Claims:</span>
          <span class="summary-value">${totalClaimsFound}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Total Cuts:</span>
          <span class="summary-value">${totalCutsApplied}</span>
        </div>
      </div>
      <div class="video-results">${videoList}</div>
      <div class="batch-final-actions">
        <button id="reset-batch-btn" class="reset-batch-button">
          🔄 Reset Batch
        </button>
        <button id="clear-batch-results-btn" class="clear-results-button">
          🗑️ Clear Results
        </button>
      </div>
    </div>
  `;
  statusDiv.classList.remove('hidden');

  // Add event listeners for action buttons
  const resetBtn = document.getElementById('reset-batch-btn');
  const clearBtn = document.getElementById('clear-batch-results-btn');

  if (resetBtn) {
    resetBtn.addEventListener('click', handleResetBatch);
  }
  if (clearBtn) {
    clearBtn.addEventListener('click', handleClearResults);
  }
}

/**
 * Format time in seconds to MM:SS or HH:MM:SS
 */
function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  // Use MM:SS for times under 1 hour, HH:MM:SS for longer times
  if (hours === 0) {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * NEW: Handle discover videos button click (batch mode)
 */
async function handleDiscoverVideos(): Promise<void> {
  if (state.isProcessing) {
    console.log('Discovery already in progress');
    return;
  }

  if (state.pageType !== 'channel') {
    showError(createError(
      'INVALID_PAGE',
      'Video discovery only works on channel content pages',
      `Current page type: ${state.pageType}`
    ));
    return;
  }

  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab || !tab.id) {
      throw new Error('No active tab found');
    }

    state.isProcessing = true;
    state.progress.phase = 'discovering';
    updateProgress('discovering', 0, 'Discovering copyright videos...');
    updateUI();

    // Send discovery message to background script
    const response = await chrome.runtime.sendMessage({
      action: 'DISCOVER_VIDEOS',
      tabId: tab.id,
      settings: state.batchSettings
    });

    if (response && response.error) {
      console.error('Video discovery failed:', response.error);
      handleOperationError(response.error);
      return;
    }

  } catch (error) {
    console.error('Failed to start video discovery:', error);
    const extensionError = createError(
      'DISCOVERY_ERROR',
      'Failed to start video discovery',
      error instanceof Error ? error.message : String(error)
    );
    handleOperationError(extensionError);
  }
}

/**
 * NEW: Handle start batch processing button click
 */
async function handleStartBatch(): Promise<void> {
  if (!state.batchDiscovery || state.batchDiscovery.videos.length === 0) {
    showError(createError(
      'NO_VIDEOS',
      'No videos discovered for batch processing',
      'Please run video discovery first'
    ));
    return;
  }

  if (state.isProcessing) {
    console.log('Batch processing already in progress');
    return;
  }

  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab || !tab.id) {
      throw new Error('No active tab found');
    }

    state.isProcessing = true;
    state.progress.phase = 'batch_processing';
    updateProgress('batch_processing', 0, 'Starting batch processing...');

    // Save state to persist batch processing status
    saveState();
    updateUI();

    // Send batch processing message to background script
    const response = await chrome.runtime.sendMessage({
      action: 'START_BATCH_OPERATION',
      videos: state.batchDiscovery.videos,
      settings: state.batchSettings,
      tabId: tab.id
    });

    if (response && response.error) {
      console.error('Batch processing failed:', response.error);
      handleOperationError(response.error);
      return;
    }

  } catch (error) {
    console.error('Failed to start batch processing:', error);
    const extensionError = createError(
      'BATCH_ERROR',
      'Failed to start batch processing',
      error instanceof Error ? error.message : String(error)
    );
    handleOperationError(extensionError);
  }
}

/**
 * Update UI based on current state
 */
function updateUI(): void {
  // Update button states based on collection results
  const hasCollectionResults = state.collectionResults && state.collectionResults.count > 0;
  const buttonsDisabled = state.isProcessing || !state.isValidPage;

  startBtn.disabled = buttonsDisabled;
  dryRunBtn.disabled = buttonsDisabled;

  // Update button text based on state and UI mode
  if (state.isProcessing) {
    if (state.uiMode === 'batch') {
      startBtn.innerHTML = '<span class="spinner"></span> Processing Batch...';
      dryRunBtn.innerHTML = '<span class="spinner"></span> Discovering...';
    } else {
      startBtn.innerHTML = '<span class="spinner"></span> Processing...';
      dryRunBtn.innerHTML = '<span class="spinner"></span> Running...';
    }
  } else if (state.uiMode === 'batch') {
    // Batch mode button text
    const hasBatchDiscovery = state.batchDiscovery && state.batchDiscovery.videos.length > 0;
    if (hasBatchDiscovery) {
      startBtn.innerHTML = `<span class="button-icon">🚀</span> Start Batch (${state.batchDiscovery.videos.length} videos)`;
      dryRunBtn.innerHTML = '<span class="button-icon">🔄</span> Discover Again';
    } else {
      startBtn.innerHTML = '<span class="button-icon">🎯</span> Discover Videos';
      dryRunBtn.innerHTML = '<span class="button-icon">🔍</span> Preview Discovery';
    }
  } else if (hasCollectionResults) {
    // Individual mode with collection results
    startBtn.innerHTML = '<span class="button-icon">🔄</span> Collect Again';
    dryRunBtn.innerHTML = '<span class="button-icon">🔍</span> Dry Run Again';
  } else {
    // Individual mode default
    const buttonText = currentSettings?.turboMode ? 'Start Turbo Trimming' : 'Start Trimming';
    startBtn.innerHTML = `<span class="button-icon">▶️</span> <span id="start-btn-text">${buttonText}</span>`;
    dryRunBtn.innerHTML = '<span class="button-icon">🔍</span> Dry Run (Preview Only)';
  }

  // Update turbo mode indicator
  updateTurboModeUI();

  // Show collection results if available
  if (hasCollectionResults && state.progress.phase === 'collection_complete') {
    showCollectionResults({
      count: state.collectionResults.count,
      rawCount: state.collectionResults.rawCount, // Pass rawCount for persistent state
      totalClaims: state.collectionResults.totalClaims, // Pass totalClaims for persistent state
      timestamps: state.collectionResults.timestamps,
      errors: state.collectionResults.errors
    });
  }
}

/**
 * Load saved state from storage
 */
async function loadState(): Promise<void> {
  try {
    // Get popup state from background
    const response = await chrome.runtime.sendMessage({
      action: 'GET_POPUP_STATE'
    });

    if (response.popupState) {
      // Restore non-processing state only
      const savedState = response.popupState;
      if (!savedState.isProcessing) {
        Object.assign(state, savedState);
      }
    }

    // Restore collection results if available
    if (response.collectionResults && response.collectionComplete) {
      state.collectionResults = response.collectionResults;
      state.progress.phase = 'collection_complete';
      state.progress.percentage = 100;
      state.progress.message = response.collectionResults.count > 0 ?
        `Collection complete! Found ${response.collectionResults.count} timestamp(s)` :
        'Collection complete - no timestamps found';
    }

    // Restore batch discovery results from background
    if (response.batchDiscovery) {
      state.batchDiscovery = response.batchDiscovery;
      console.log('🔄 Restored batch discovery from background:', state.batchDiscovery.videos.length, 'videos');
    }

    // Restore batch settings from background
    if (response.batchSettings) {
      state.batchSettings = response.batchSettings;
      console.log('🔄 Restored batch settings from background:', state.batchSettings);
    }

    // Restore batch processing state if active
    if (response.batchProcessingState && response.batchProcessingState.isProcessing) {
      state.isProcessing = true;
      state.progress.phase = 'batch_processing';
      state.progress.percentage = Math.round((response.batchProcessingState.currentIndex / response.batchProcessingState.totalVideos) * 100);
      state.progress.message = `Batch processing in progress... (${response.batchProcessingState.currentIndex}/${response.batchProcessingState.totalVideos})`;
      console.log('🔄 Restored active batch processing state');

      // Use updateProgress to properly show progress section and batch control buttons
      updateProgress('batch_processing', state.progress.percentage, state.progress.message);
    }

    // Also check legacy storage format and batch state
    const legacyResult = await chrome.storage.session.get([
      'claimcutter_operation_state',
      'claimcutter_stats',
      'claimcutter_collection_results',
      'claimcutter_collection_complete',
      'claimcutter_batch_discovery',
      'claimcutter_batch_discovery_timestamp',
      'claimcutter_batch_settings'
    ]);

    if (legacyResult.claimcutter_stats) {
      updateStats(legacyResult.claimcutter_stats);
    }

    // Restore collection results from legacy storage if not already restored
    if (!state.collectionResults && legacyResult.claimcutter_collection_results && legacyResult.claimcutter_collection_complete) {
      state.collectionResults = legacyResult.claimcutter_collection_results;
      state.progress.phase = 'collection_complete';
      state.progress.percentage = 100;
      state.progress.message = legacyResult.claimcutter_collection_results.count > 0 ?
        `Collection complete! Found ${legacyResult.claimcutter_collection_results.count} timestamp(s)` :
        'Collection complete - no timestamps found';
    }

    // Restore batch discovery results
    if (legacyResult.claimcutter_batch_discovery) {
      state.batchDiscovery = legacyResult.claimcutter_batch_discovery;
      console.log('🔄 Restored batch discovery:', state.batchDiscovery.videos.length, 'videos');
    }

    // Restore batch settings
    if (legacyResult.claimcutter_batch_settings) {
      state.batchSettings = legacyResult.claimcutter_batch_settings;
      console.log('🔄 Restored batch settings:', state.batchSettings);
    }

    // Check for ongoing batch operations
    const batchOperations = await chrome.storage.session.get();
    const activeBatchOp = Object.keys(batchOperations).find(key => key.startsWith('batchOperation_'));

    if (activeBatchOp) {
      const batchOp = batchOperations[activeBatchOp];
      if (batchOp && !batchOp.completedAt) {
        console.log('🔄 Found ongoing batch operation:', batchOp);
        state.isProcessing = true;
        state.progress.phase = 'batch_processing';
        state.progress.percentage = Math.round(((batchOp.currentIndex || 0) / (batchOp.videos?.length || 1)) * 100);
        state.progress.message = `Batch processing in progress... (${batchOp.currentIndex || 0}/${batchOp.videos?.length || 0})`;

        // Check if batch is paused
        state.batchPaused = batchOp.isPaused || false;

        // Use updateProgress to properly show progress section and batch control buttons
        updateProgress('batch_processing', state.progress.percentage, state.progress.message);
      }
    }

  } catch (error) {
    console.error('Failed to load state:', error);
  }
}

/**
 * Save current state to storage
 */
async function saveState(): Promise<void> {
  try {
    // Save main popup state
    await chrome.storage.session.set({
      claimcutter_operation_state: {
        ...state,
        isProcessing: false, // Don't persist processing state for individual mode
      },
      claimcutter_stats: state.stats,
    });

    // Save batch discovery results separately for persistence
    if (state.batchDiscovery) {
      await chrome.storage.session.set({
        claimcutter_batch_discovery: state.batchDiscovery,
        claimcutter_batch_discovery_timestamp: Date.now()
      });
    }

    // Save batch settings
    if (state.batchSettings) {
      await chrome.storage.session.set({
        claimcutter_batch_settings: state.batchSettings
      });
    }

    // Update popup state in background script for comprehensive persistence
    await chrome.runtime.sendMessage({
      action: 'UPDATE_POPUP_STATE',
      state: {
        ...state,
        isProcessing: false, // Don't persist processing state for individual mode
      }
    });

  } catch (error) {
    console.error('Failed to save state:', error);
  }
}

// Auto-save state periodically
setInterval(saveState, 5000);

/**
 * Load current settings
 */
async function loadCurrentSettings(): Promise<void> {
  try {
    currentSettings = await loadSettings();
    console.log('Loaded settings:', currentSettings);
  } catch (error) {
    console.error('Failed to load settings:', error);
    currentSettings = {
      mergeBuffer: 15,
      turboMode: false,
      autoSave: false,
      defaultSettings: false,
    };
  }
}

/**
 * Handle settings changes
 */
function onSettingsChanged(newSettings: ClaimCutterSettings): void {
  currentSettings = newSettings;
  updateTurboModeUI();
  console.log('Settings updated:', newSettings);
}

/**
 * Update turbo mode UI indicator
 */
function updateTurboModeUI(): void {
  // Safety check - ensure currentSettings is loaded
  if (!currentSettings) {
    console.log('Settings not loaded yet, skipping turbo mode UI update');
    return;
  }

  if (currentSettings.turboMode) {
    turboIndicator.classList.remove('hidden');
    if (startBtnText) startBtnText.textContent = 'Start Turbo Trimming';
  } else {
    turboIndicator.classList.add('hidden');
    if (startBtnText) startBtnText.textContent = 'Start Trimming';
  }
}

/**
 * Test debugger API functionality
 */
async function handleTestDebugger(): Promise<void> {
  console.log('🧪 Testing debugger API...');

  try {
    // Get current tab
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab?.id) {
      showStatus('error', 'No active tab found');
      return;
    }

    // Check if we're on YouTube Studio
    if (!tab.url?.includes('studio.youtube.com')) {
      showStatus('warning', 'Please navigate to YouTube Studio first');
      return;
    }

    showStatus('info', 'Testing debugger API - check console for details...');

    // Test debugger attachment
    console.log('📋 Attaching debugger to tab:', tab.id);

    await new Promise<void>((resolve, reject) => {
      chrome.debugger.attach({ tabId: tab.id! }, '1.3', () => {
        if (chrome.runtime.lastError) {
          reject(new Error(`Failed to attach debugger: ${chrome.runtime.lastError.message}`));
          return;
        }
        console.log('✅ Debugger attached successfully');
        resolve();
      });
    });

    // Test trusted Tab key event
    console.log('⌨️ Dispatching trusted Tab key event...');

    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId: tab.id! },
        'Input.dispatchKeyEvent',
        {
          type: 'keyDown',
          key: 'Tab',
          keyCode: 9,
          code: 'Tab',
          windowsVirtualKeyCode: 9
        },
        () => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Tab keyDown failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          console.log('✅ Trusted Tab keyDown event dispatched');
          resolve();
        }
      );
    });

    // Small delay
    await new Promise(resolve => setTimeout(resolve, 50));

    // Test keyUp event
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId: tab.id! },
        'Input.dispatchKeyEvent',
        {
          type: 'keyUp',
          key: 'Tab',
          keyCode: 9,
          code: 'Tab',
          windowsVirtualKeyCode: 9
        },
        () => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Tab keyUp failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          console.log('✅ Trusted Tab keyUp event dispatched');
          resolve();
        }
      );
    });

    // Detach debugger
    chrome.debugger.detach({ tabId: tab.id! }, () => {
      if (chrome.runtime.lastError) {
        console.warn('⚠️ Error detaching debugger:', chrome.runtime.lastError.message);
      } else {
        console.log('🔧 Debugger detached');
      }
    });

    showStatus('success', '🎉 Debugger API test completed successfully! Check console for details.');
    console.log('🎯 Debugger API test completed successfully');

  } catch (error) {
    console.error('❌ Debugger API test failed:', error);
    showStatus('error', `Debugger test failed: ${error}`);

    // Try to detach debugger in case of error
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab?.id) {
        chrome.debugger.detach({ tabId: tab.id }, () => {
          console.log('🔧 Debugger detached after error');
        });
      }
    } catch (detachError) {
      console.warn('⚠️ Failed to detach debugger after error:', detachError);
    }
  }
}

console.log('ClaimCutter enhanced popup script loaded');
