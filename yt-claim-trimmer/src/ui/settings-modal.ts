/**
 * Settings Modal for ClaimCutter Chrome Extension
 * Provides user interface for adjusting extension settings
 */

import { ClaimCutterSettings, loadSettings, saveSettings, resetSettings, validateSettings, getMergeBufferDescription, getTurboModeDescription } from '../utils/settings.js';

export class SettingsModal {
  private modal: HTMLElement | null = null;
  private settings: ClaimCutterSettings;
  private onSettingsChanged?: (settings: ClaimCutterSettings) => void;

  constructor(onSettingsChanged?: (settings: ClaimCutterSettings) => void) {
    this.onSettingsChanged = onSettingsChanged;
    this.settings = {
      mergeBuffer: 15,
      turboMode: false,
      autoSave: false,
      defaultSettings: false,
    };
  }

  /**
   * Show the settings modal
   */
  async show(): Promise<void> {
    // Load current settings
    this.settings = await loadSettings();
    
    // Create modal if it doesn't exist
    if (!this.modal) {
      this.createModal();
    }
    
    // Update form with current settings
    this.updateForm();
    
    // Show modal
    if (this.modal) {
      this.modal.style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }
  }

  /**
   * Hide the settings modal
   */
  hide(): void {
    if (this.modal) {
      this.modal.style.display = 'none';
      document.body.style.overflow = '';
    }
  }

  /**
   * Create the modal DOM structure
   */
  private createModal(): void {
    this.modal = document.createElement('div');
    this.modal.className = 'settings-modal';
    this.modal.innerHTML = `
      <div class="settings-modal-overlay">
        <div class="settings-modal-content">
          <div class="settings-header">
            <h2>ClaimCutter Settings</h2>
            <button class="settings-close" aria-label="Close settings">×</button>
          </div>
          
          <div class="settings-body">
            <div class="setting-group">
              <label class="setting-label">
                <span class="setting-title">Merge Buffer</span>
                <span class="setting-description" id="merge-description">Balanced merging - recommended for most cases</span>
              </label>
              <div class="setting-control">
                <input type="range" id="merge-buffer" min="5" max="30" step="1" value="15" class="setting-slider">
                <span class="setting-value" id="merge-value">15s</span>
              </div>
            </div>

            <div class="setting-group">
              <label class="setting-label">
                <span class="setting-title">Turbo Mode</span>
                <span class="setting-description" id="turbo-description">Manual confirmation required for each step</span>
              </label>
              <div class="setting-control">
                <label class="setting-toggle">
                  <input type="checkbox" id="turbo-mode">
                  <span class="setting-toggle-slider"></span>
                </label>
              </div>
            </div>

            <div class="setting-group" id="auto-save-group" style="display: none;">
              <label class="setting-label">
                <span class="setting-title">Auto-Save Video</span>
                <span class="setting-description">Automatically save video after applying cuts (Turbo mode only)</span>
              </label>
              <div class="setting-control">
                <label class="setting-toggle">
                  <input type="checkbox" id="auto-save">
                  <span class="setting-toggle-slider"></span>
                </label>
              </div>
            </div>

            <div class="setting-group">
              <label class="setting-label">
                <span class="setting-title">Save as Default</span>
                <span class="setting-description">Remember these settings for future use</span>
              </label>
              <div class="setting-control">
                <label class="setting-toggle">
                  <input type="checkbox" id="default-settings" checked>
                  <span class="setting-toggle-slider"></span>
                </label>
              </div>
            </div>
          </div>
          
          <div class="settings-footer">
            <button class="settings-button settings-button-secondary" id="reset-settings">Reset to Defaults</button>
            <div class="settings-actions">
              <button class="settings-button settings-button-secondary" id="cancel-settings">Cancel</button>
              <button class="settings-button settings-button-primary" id="save-settings">Save Settings</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add styles
    this.addStyles();
    
    // Add event listeners
    this.setupEventListeners();
    
    // Append to body
    document.body.appendChild(this.modal);
  }

  /**
   * Add CSS styles for the modal
   */
  private addStyles(): void {
    if (document.getElementById('settings-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'settings-modal-styles';
    style.textContent = `
      .settings-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        display: none;
      }

      .settings-modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .settings-modal-content {
        background: white;
        border-radius: 12px;
        width: 100%;
        max-width: 480px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }

      .settings-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 24px 16px;
        border-bottom: 1px solid #e5e7eb;
      }

      .settings-header h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #111827;
      }

      .settings-close {
        background: none;
        border: none;
        font-size: 24px;
        color: #6b7280;
        cursor: pointer;
        padding: 4px;
        line-height: 1;
      }

      .settings-close:hover {
        color: #374151;
      }

      .settings-body {
        padding: 20px 24px;
      }

      .setting-group {
        margin-bottom: 24px;
      }

      .setting-group:last-child {
        margin-bottom: 0;
      }

      .setting-label {
        display: block;
        margin-bottom: 8px;
      }

      .setting-title {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #111827;
        margin-bottom: 4px;
      }

      .setting-description {
        display: block;
        font-size: 12px;
        color: #6b7280;
        line-height: 1.4;
      }

      .setting-control {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .setting-slider {
        flex: 1;
        height: 6px;
        border-radius: 3px;
        background: #e5e7eb;
        outline: none;
        -webkit-appearance: none;
      }

      .setting-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #dc2626;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .setting-slider::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #dc2626;
        cursor: pointer;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .setting-value {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        min-width: 32px;
        text-align: center;
      }

      .setting-toggle {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;
      }

      .setting-toggle input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .setting-toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #e5e7eb;
        transition: 0.2s;
        border-radius: 24px;
      }

      .setting-toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: 0.2s;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .setting-toggle input:checked + .setting-toggle-slider {
        background-color: #dc2626;
      }

      .setting-toggle input:checked + .setting-toggle-slider:before {
        transform: translateX(20px);
      }

      .settings-footer {
        padding: 16px 24px 20px;
        border-top: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .settings-actions {
        display: flex;
        gap: 8px;
      }

      .settings-button {
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid transparent;
      }

      .settings-button-primary {
        background: #dc2626;
        color: white;
      }

      .settings-button-primary:hover {
        background: #b91c1c;
      }

      .settings-button-secondary {
        background: #f3f4f6;
        color: #374151;
        border-color: #d1d5db;
      }

      .settings-button-secondary:hover {
        background: #e5e7eb;
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * Setup event listeners for the modal
   */
  private setupEventListeners(): void {
    if (!this.modal) return;

    // Close button
    const closeBtn = this.modal.querySelector('.settings-close') as HTMLButtonElement;
    closeBtn?.addEventListener('click', () => this.hide());

    // Overlay click to close
    const overlay = this.modal.querySelector('.settings-modal-overlay') as HTMLElement;
    overlay?.addEventListener('click', (e) => {
      if (e.target === overlay) this.hide();
    });

    // Merge buffer slider
    const mergeSlider = this.modal.querySelector('#merge-buffer') as HTMLInputElement;
    const mergeValue = this.modal.querySelector('#merge-value') as HTMLElement;
    const mergeDescription = this.modal.querySelector('#merge-description') as HTMLElement;
    
    mergeSlider?.addEventListener('input', () => {
      const value = parseInt(mergeSlider.value);
      mergeValue.textContent = `${value}s`;
      mergeDescription.textContent = getMergeBufferDescription(value);
    });

    // Turbo mode toggle
    const turboToggle = this.modal.querySelector('#turbo-mode') as HTMLInputElement;
    const turboDescription = this.modal.querySelector('#turbo-description') as HTMLElement;
    const autoSaveGroup = this.modal.querySelector('#auto-save-group') as HTMLElement;
    
    turboToggle?.addEventListener('change', () => {
      turboDescription.textContent = getTurboModeDescription(turboToggle.checked);
      autoSaveGroup.style.display = turboToggle.checked ? 'block' : 'none';
    });

    // Action buttons
    const cancelBtn = this.modal.querySelector('#cancel-settings') as HTMLButtonElement;
    const saveBtn = this.modal.querySelector('#save-settings') as HTMLButtonElement;
    const resetBtn = this.modal.querySelector('#reset-settings') as HTMLButtonElement;

    cancelBtn?.addEventListener('click', () => this.hide());
    saveBtn?.addEventListener('click', () => this.saveSettings());
    resetBtn?.addEventListener('click', () => this.resetToDefaults());

    // Escape key to close
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.modal?.style.display === 'flex') {
        this.hide();
      }
    });
  }

  /**
   * Update form with current settings
   */
  private updateForm(): void {
    if (!this.modal) return;

    const mergeSlider = this.modal.querySelector('#merge-buffer') as HTMLInputElement;
    const mergeValue = this.modal.querySelector('#merge-value') as HTMLElement;
    const mergeDescription = this.modal.querySelector('#merge-description') as HTMLElement;
    const turboToggle = this.modal.querySelector('#turbo-mode') as HTMLInputElement;
    const turboDescription = this.modal.querySelector('#turbo-description') as HTMLElement;
    const autoSaveToggle = this.modal.querySelector('#auto-save') as HTMLInputElement;
    const autoSaveGroup = this.modal.querySelector('#auto-save-group') as HTMLElement;
    const defaultToggle = this.modal.querySelector('#default-settings') as HTMLInputElement;

    if (mergeSlider) {
      mergeSlider.value = this.settings.mergeBuffer.toString();
      mergeValue.textContent = `${this.settings.mergeBuffer}s`;
      mergeDescription.textContent = getMergeBufferDescription(this.settings.mergeBuffer);
    }

    if (turboToggle) {
      turboToggle.checked = this.settings.turboMode;
      turboDescription.textContent = getTurboModeDescription(this.settings.turboMode);
      autoSaveGroup.style.display = this.settings.turboMode ? 'block' : 'none';
    }

    if (autoSaveToggle) {
      autoSaveToggle.checked = this.settings.autoSave;
    }

    if (defaultToggle) {
      defaultToggle.checked = this.settings.defaultSettings;
    }
  }

  /**
   * Save current settings
   */
  private async saveSettings(): Promise<void> {
    if (!this.modal) return;

    try {
      const mergeSlider = this.modal.querySelector('#merge-buffer') as HTMLInputElement;
      const turboToggle = this.modal.querySelector('#turbo-mode') as HTMLInputElement;
      const autoSaveToggle = this.modal.querySelector('#auto-save') as HTMLInputElement;
      const defaultToggle = this.modal.querySelector('#default-settings') as HTMLInputElement;

      const newSettings: ClaimCutterSettings = validateSettings({
        mergeBuffer: parseInt(mergeSlider.value),
        turboMode: turboToggle.checked,
        autoSave: autoSaveToggle.checked,
        defaultSettings: defaultToggle.checked,
      });

      await saveSettings(newSettings);
      this.settings = newSettings;

      // Notify parent component
      if (this.onSettingsChanged) {
        this.onSettingsChanged(newSettings);
      }

      this.hide();
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('Failed to save settings. Please try again.');
    }
  }

  /**
   * Reset to default settings
   */
  private async resetToDefaults(): Promise<void> {
    try {
      this.settings = await resetSettings();
      this.updateForm();
    } catch (error) {
      console.error('Failed to reset settings:', error);
      alert('Failed to reset settings. Please try again.');
    }
  }
}
