/**
 * Content Script A: Timestamp Collection
 * Scrapes copyright claim timestamps from YouTube Studio copyright page
 */

// Inline all utilities to avoid import issues

// Enhanced selectors for YouTube Studio copyright page (based on successful browser testing)
const COPYRIGHT_PAGE_SELECTORS = {
  // Exact selector for claim containers - tested and working
  claimRows: 'ytcr-video-content-list-row.style-scope.ytcr-video-content-list',

  // Exact selector for "See details" button - tested and working
  seeDetailsButton: 'button.ytcp-button-shape-impl[aria-label="See details"]',
};

// Timestamp pair interface
interface TimestampPair {
  start: number;
  end: number;
  raw?: string; // Original text for debugging
}

// Enhanced interface for merged ranges
interface MergedRange extends TimestampPair {
  originalCount: number; // Number of original ranges that were merged
  sources: string[];     // Original raw text sources for debugging
}

// Utility functions
function findElement(selector: string, parent: Element | Document = document): Element | null {
  try {
    return parent.querySelector(selector);
  } catch (error) {
    console.warn(`Invalid selector: ${selector}`, error);
    return null;
  }
}

function findElements(selector: string, parent: Element | Document = document): Element[] {
  try {
    return Array.from(parent.querySelectorAll(selector));
  } catch (error) {
    console.warn(`Invalid selector: ${selector}`, error);
    return [];
  }
}

function waitForElement(selector: string, timeout: number = 10000): Promise<Element> {
  return new Promise((resolve, reject) => {
    const element = findElement(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = findElement(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element not found: ${selector}`));
    }, timeout);
  });
}

/**
 * Extract timestamps from visible text content (not CSS/JS)
 * Based on successful browser testing
 */
function extractTimestampsFromPage(): TimestampPair[] {
  const timestamps: TimestampPair[] = [];

  // Get visible text content only (avoid CSS and JavaScript)
  function getVisibleText(element: Element): boolean {
    const style = window.getComputedStyle(element);
    return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
  }

  // Get all visible text elements
  const allElements = document.querySelectorAll('*');
  let visibleText = '';

  Array.from(allElements).forEach(el => {
    if (el.children.length === 0 && getVisibleText(el)) {
      const text = el.textContent?.trim();
      if (text && text.length > 0 && !text.match(/^[\s\n\r]*$/)) {
        visibleText += text + ' ';
      }
    }
  });

  // Search for timestamp patterns in visible text
  const timestampPatterns = [
    /(\d{1,2}:\d{2}:\d{2})\s*[-–—]\s*(\d{1,2}:\d{2}:\d{2})/g,  // HH:MM:SS - HH:MM:SS
    /(\d{1,2}:\d{2})\s*[-–—]\s*(\d{1,2}:\d{2}:\d{2})/g,        // MM:SS - HH:MM:SS
    /(\d{1,2}:\d{2}:\d{2})\s*[-–—]\s*(\d{1,2}:\d{2})/g,        // HH:MM:SS - MM:SS
    /(\d{1,2}:\d{2})\s*[-–—]\s*(\d{1,2}:\d{2})/g               // MM:SS - MM:SS
  ];

  timestampPatterns.forEach(pattern => {
    const matches = [...visibleText.matchAll(pattern)];
    matches.forEach(match => {
      const start = parseTimestamp(match[1]);
      const end = parseTimestamp(match[2]);

      if (start !== null && end !== null && start < end) {
        timestamps.push({ start, end });
        console.log(`Found timestamp: ${match[1]} - ${match[2]} (${start}s - ${end}s)`);
      }
    });
  });

  return timestamps;
}

/**
 * Legacy function for backward compatibility
 */
function extractTimestampsFromElement(element: Element): TimestampPair[] {
  const text = element.textContent || '';
  const timestamps: TimestampPair[] = [];

  // Look for timestamp patterns like "1:23 - 2:45" or "1:23-2:45"
  const timestampRegex = /(\d{1,2}:\d{2}(?::\d{2})?)\s*[-–—]\s*(\d{1,2}:\d{2}(?::\d{2})?)/g;
  let match;

  while ((match = timestampRegex.exec(text)) !== null) {
    const start = parseTimestamp(match[1]);
    const end = parseTimestamp(match[2]);

    if (start !== null && end !== null && start < end) {
      timestamps.push({ start, end });
    }
  }

  return timestamps;
}

/**
 * Get video ID from current URL
 */
function getVideoIdFromUrl(): string | null {
  const url = window.location.href;
  const match = url.match(/\/video\/([a-zA-Z0-9_-]+)/);
  return match ? match[1] : null;
}

/**
 * Get video duration from YouTube Studio UI
 */
function getVideoDuration(): number | null {
  // Try multiple selectors where YouTube Studio shows video duration
  const durationSelectors = [
    // Video details page
    '.video-duration',
    '[data-testid="video-duration"]',
    // Timeline in editor
    'input.style-scope.ytcp-media-timestamp-input[role="timer"]',
    // Video metadata areas
    '.ytcp-video-metadata-duration',
    '.duration-text',
    // Look for any element containing duration-like text
    '*[title*="duration" i]',
    '*[aria-label*="duration" i]'
  ];

  for (const selector of durationSelectors) {
    const elements = document.querySelectorAll(selector);
    for (const element of elements) {
      const text = element.textContent?.trim() || element.getAttribute('value') || '';
      const duration = parseTimestamp(text);
      if (duration && duration > 0) {
        console.log(`📏 Video duration detected: ${text} (${duration}s) from selector: ${selector}`);
        return duration;
      }
    }
  }

  // Fallback: Look for any time-like patterns in the page that could be video duration
  const pageText = document.body.textContent || '';
  const timePatterns = pageText.match(/\b\d{1,2}:\d{2}(?::\d{2})?\b/g);

  if (timePatterns) {
    // Look for the longest time pattern as it's likely the video duration
    let maxDuration = 0;
    let durationText = '';

    timePatterns.forEach(pattern => {
      const duration = parseTimestamp(pattern);
      if (duration && duration > maxDuration) {
        maxDuration = duration;
        durationText = pattern;
      }
    });

    if (maxDuration > 0) {
      console.log(`📏 Video duration estimated from page content: ${durationText} (${maxDuration}s)`);
      return maxDuration;
    }
  }

  console.warn('⚠️ Could not detect video duration from YouTube Studio UI');
  return null;
}

function parseTimestamp(timeStr: string): number | null {
  if (!timeStr) return null;

  const parts = timeStr.split(':').map(Number);

  if (parts.length === 2) {
    // MM:SS format
    const [minutes, seconds] = parts;
    return minutes * 60 + seconds;
  } else if (parts.length === 3) {
    // HH:MM:SS format
    const [hours, minutes, seconds] = parts;
    return hours * 3600 + minutes * 60 + seconds;
  }

  return null;
}

function formatTimestamp(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  // Use MM:SS for times under 1 hour, HH:MM:SS for longer times
  if (hours === 0) {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * Detect likely timestamp format parsing errors that should be rejected
 */
function detectFormatParsingError(pair: TimestampPair, videoDuration?: number | null): { shouldReject: boolean; reason: string } | null {
  const duration = pair.end - pair.start;
  const rawText = pair.raw || `${formatTimestamp(pair.start)} - ${formatTimestamp(pair.end)}`;

  // Check for suspicious patterns that indicate format confusion
  if (videoDuration && videoDuration >= 3600) { // Video is 1+ hours
    // Look for cases where start time is in MM:SS but end time spans hours
    const startHours = Math.floor(pair.start / 3600);
    const endHours = Math.floor(pair.end / 3600);

    // AGGRESSIVE: Reject obvious format parsing errors
    // If start time is under 1 hour but end time is 2+ hours, and the duration is over 1 hour,
    // this is very likely a MM:SS vs H:MM:SS parsing error
    if (startHours === 0 && endHours >= 2 && duration > 60 * 60) {
      // Additional check: if the "start" time in minutes is reasonable for a claim start
      // but creates an unreasonably long duration, it's likely a format error
      const startMinutes = Math.floor(pair.start / 60);
      if (startMinutes < 60 && duration > 90 * 60) { // Start < 1 hour, duration > 1.5 hours
        return {
          shouldReject: true,
          reason: `Format parsing error: Start time ${formatTimestamp(pair.start)} (MM:SS) vs end time ${formatTimestamp(pair.end)} (H:MM:SS) creates unrealistic ${Math.floor(duration/60)}m duration`
        };
      }
    }
  }

  return null;
}

function validateTimestampPair(pair: TimestampPair, videoDuration?: number | null): boolean {
  const duration = pair.end - pair.start;
  const isValidBasic = pair.start >= 0 && pair.end > pair.start && duration >= 1;

  if (!isValidBasic) return false;

  // HARD REJECTION: Only reject if timestamps exceed video duration (impossible)
  if (videoDuration && pair.end > videoDuration) {
    console.warn(`❌ REJECTING: Timestamp range exceeds video duration: ${formatTimestamp(pair.start)} - ${formatTimestamp(pair.end)} (video is ${formatTimestamp(videoDuration)})`);
    return false;
  }

  // Check for likely format parsing errors
  const formatError = detectFormatParsingError(pair, videoDuration);
  if (formatError) {
    if (formatError.shouldReject) {
      console.warn(`❌ REJECTING: ${formatError.reason}`);
      console.warn(`   This appears to be a timestamp format parsing error due to YouTube's variable format.`);
      return false;
    } else {
      console.warn(`🚨 CAUTION: Possible format parsing error detected:`);
      console.warn(`   ${formatError.reason}`);
      console.warn(`   Consider reviewing this timestamp manually.`);
    }
  }

  // TIERED WARNING SYSTEM: Warn but don't reject based on duration
  const durationMinutes = Math.floor(duration / 60);

  if (duration >= 30 * 60) { // 30+ minutes
    console.warn(`🚨 CAUTION: Very long copyright claim detected: ${formatTimestamp(pair.start)} - ${formatTimestamp(pair.end)} (${durationMinutes}m)`);
    console.warn(`   This may be legitimate (background music) or a timestamp format parsing error. Review carefully.`);
  } else if (duration >= 10 * 60) { // 10-30 minutes
    console.info(`ℹ️ INFO: Long copyright claim detected: ${formatTimestamp(pair.start)} - ${formatTimestamp(pair.end)} (${durationMinutes}m)`);
    console.info(`   This is longer than typical but may be legitimate (extended music/audio).`);
  }
  // No warning for claims under 10 minutes (typical)

  return true;
}

function mergeRanges(ranges: TimestampPair[], mergeBuffer: number = 15): MergedRange[] {
  if (ranges.length === 0) return [];

  console.log(`\n🔄 MERGING RANGES (buffer: ${mergeBuffer}s):`);
  ranges.forEach((r, i) => {
    console.log(`  Input ${i + 1}: ${formatTimestamp(r.start)}-${formatTimestamp(r.end)} (${r.start}s-${r.end}s)`);
  });

  // Sort ranges by start time
  const sorted = [...ranges].sort((a, b) => a.start - b.start);
  console.log(`\n📊 SORTED RANGES:`);
  sorted.forEach((r, i) => {
    console.log(`  Sorted ${i + 1}: ${formatTimestamp(r.start)}-${formatTimestamp(r.end)} (${r.start}s-${r.end}s)`);
  });

  const merged: MergedRange[] = [];

  let current: MergedRange = {
    start: sorted[0].start,
    end: sorted[0].end,
    originalCount: 1,
    sources: [sorted[0].raw || formatTimestamp(sorted[0].start) + '-' + formatTimestamp(sorted[0].end)]
  };

  console.log(`\n🎯 MERGE ANALYSIS:`);
  console.log(`  Starting with: ${formatTimestamp(current.start)}-${formatTimestamp(current.end)}`);

  for (let i = 1; i < sorted.length; i++) {
    const range = sorted[i];
    const gap = range.start - current.end;
    const shouldMerge = range.start <= current.end + mergeBuffer;

    console.log(`\n  Comparing:`);
    console.log(`    Current: ${formatTimestamp(current.start)}-${formatTimestamp(current.end)} (${current.start}s-${current.end}s)`);
    console.log(`    Next:    ${formatTimestamp(range.start)}-${formatTimestamp(range.end)} (${range.start}s-${range.end}s)`);
    console.log(`    Gap:     ${gap}s (${Math.floor(gap/60)}m ${gap%60}s)`);
    console.log(`    Buffer:  ${mergeBuffer}s`);
    console.log(`    Should merge: ${shouldMerge} (${range.start} <= ${current.end} + ${mergeBuffer} = ${current.end + mergeBuffer})`);

    // Check if current range overlaps or is adjacent to the previous one
    // Adjacent means there's no gap (or minimal gap < mergeBuffer seconds for copyright claims)
    if (shouldMerge) {
      // Merge ranges
      const oldEnd = current.end;
      current.end = Math.max(current.end, range.end);
      current.originalCount++;
      current.sources.push(range.raw || formatTimestamp(range.start) + '-' + formatTimestamp(range.end));

      console.log(`    ✅ MERGED: ${formatTimestamp(current.start)}-${formatTimestamp(oldEnd)} + ${formatTimestamp(range.start)}-${formatTimestamp(range.end)} → ${formatTimestamp(current.start)}-${formatTimestamp(current.end)} [${current.originalCount} sources]`);
    } else {
      // No overlap, push current and start new range
      merged.push(current);
      console.log(`    ➕ SEPARATE: Added range ${formatTimestamp(current.start)}-${formatTimestamp(current.end)} to final list`);

      current = {
        start: range.start,
        end: range.end,
        originalCount: 1,
        sources: [range.raw || formatTimestamp(range.start) + '-' + formatTimestamp(range.end)]
      };
      console.log(`    🆕 NEW: Starting new range ${formatTimestamp(current.start)}-${formatTimestamp(current.end)}`);
    }
  }

  // Don't forget the last range
  merged.push(current);
  console.log(`    🏁 FINAL: Added last range ${formatTimestamp(current.start)}-${formatTimestamp(current.end)} to final list`);

  console.log(`\n📋 MERGE RESULTS:`);
  merged.forEach((r, i) => {
    console.log(`  Final ${i + 1}: ${formatTimestamp(r.start)}-${formatTimestamp(r.end)} [${r.originalCount} source${r.originalCount > 1 ? 's' : ''}]`);
  });

  console.log(`📊 Merged ${ranges.length} ranges into ${merged.length} ranges`);
  return merged;
}

function optimizeRanges(ranges: MergedRange[], minDuration: number = 3): MergedRange[] {
  return ranges.filter(range => range.end - range.start >= minDuration);
}

/**
 * Filter elements to find actual claim containers (based on successful browser testing)
 * Should have exactly 3 children and contain a "See details" button
 */
function filterActualRows(elements: Element[]): Element[] {
  return elements.filter(element => {
    const tagName = element.tagName.toLowerCase();
    const className = element.className;

    // ONLY accept ytcr-video-content-list-row elements with exact classes
    if (tagName === 'ytcr-video-content-list-row' &&
        className.includes('style-scope') &&
        className.includes('ytcr-video-content-list')) {

      // Additional validation: should have exactly 3 children (based on browser testing)
      const childCount = element.children.length;
      if (childCount === 3) {
        // Check if it has a "See details" button
        const hasDetailsButton = element.querySelector(COPYRIGHT_PAGE_SELECTORS.seeDetailsButton) !== null;
        if (hasDetailsButton) {
          return true;
        }
        console.log(`Found claim container without details button:`, element);
      } else {
        console.warn(`Found ytcr-video-content-list-row with ${childCount} children instead of 3:`, element);
      }
    }

    return false;
  });
}

/**
 * Get claim preview text for logging
 */
function getClaimPreview(element: Element): string {
  const text = element.textContent?.trim() || '';
  return text.substring(0, 100).replace(/\s+/g, ' ');
}

interface CollectionResult {
  timestamps: MergedRange[];
  count: number;
  rawCount: number; // Number of raw timestamps before merging
  totalClaims: number; // Total number of claims processed (regardless of timestamps found)
  errors: string[];
  rawTimestamps?: TimestampPair[]; // Keep track of original timestamps for debugging
}

/**
 * Main collection function - scrapes all copyright claim timestamps
 * Based on successful browser testing
 */
async function collectTimestamps(dryRun: boolean, mergeBuffer: number = 15): Promise<CollectionResult> {
  console.log('=== STARTING TIMESTAMP COLLECTION ===', { dryRun });

  const result: CollectionResult = {
    timestamps: [],
    count: 0,
    rawCount: 0,
    totalClaims: 0,
    errors: []
  };

  try {
    // Check if we're on the copyright page
    if (!window.location.href.includes('/copyright')) {
      result.errors.push('Not on a YouTube Studio copyright page. Please navigate to the copyright page first.');
      return result;
    }

    console.log('Page URL:', window.location.href);

    // Detect video duration for smart validation
    const videoDuration = getVideoDuration();
    if (videoDuration) {
      console.log(`📏 Video duration: ${formatTimestamp(videoDuration)} (${videoDuration}s)`);
    } else {
      console.log('📏 Video duration not detected - using fallback validation');
    }

    // Find all claim rows using the exact working selector
    const allPotentialRows = findElements(COPYRIGHT_PAGE_SELECTORS.claimRows);
    console.log(`Found ${allPotentialRows.length} potential claim elements`);

    // Filter to get only actual claim containers with details buttons
    const claimRows = filterActualRows(allPotentialRows);
    console.log(`Found ${claimRows.length} valid claims on the page`);

    if (claimRows.length === 0) {
      result.errors.push('No copyright claims found on this page. Make sure the page has finished loading and contains copyright claims.');
      return result;
    }

    // Log details about found claims
    claimRows.forEach((row, i) => {
      const preview = getClaimPreview(row);
      const hasDetailsButton = row.querySelector(COPYRIGHT_PAGE_SELECTORS.seeDetailsButton) !== null;
      console.log(`Claim ${i + 1}: Has details button = ${hasDetailsButton}`);
      console.log(`  Content preview: ${preview}`);
    });

    console.log('✅ Ready to process claims');

    // Store original URL to return to
    const originalUrl = window.location.href;

    // Set total claims count
    result.totalClaims = claimRows.length;

    // Process each claim row
    for (let i = 0; i < claimRows.length; i++) {
      const row = claimRows[i];
      console.log(`\n=== Processing claim ${i + 1}/${claimRows.length} ===`);

      try {
        const timestamps = await processClaimRow(row, i + 1, dryRun, originalUrl);
        if (timestamps.length > 0) {
          result.timestamps.push(...timestamps);
          console.log(`✅ Collected ${timestamps.length} timestamp(s) from claim ${i + 1}`);
        } else {
          console.log(`⚠️ No timestamps found for claim ${i + 1}`);
        }
      } catch (error) {
        const errorMsg = `Failed to process claim ${i + 1}: ${error}`;
        console.error(errorMsg);
        result.errors.push(errorMsg);
      }

      // Random delay between claims to avoid detection
      if (i < claimRows.length - 1) {
        await randomDelay();
      }
    }

    // Post-process collected timestamps
    console.log(`\n=== POST-PROCESSING ===`);
    console.log(`Collected ${result.timestamps.length} raw timestamp pairs`);

    if (result.timestamps.length > 0) {
      // Store raw timestamps for debugging
      const rawTimestamps = [...result.timestamps];

      // 🚀 ENHANCED DEBUG: Show all raw timestamps before any processing
      console.log(`\n🔍 RAW TIMESTAMPS COLLECTED:`);
      rawTimestamps.forEach((ts, i) => {
        console.log(`  Raw ${i + 1}: ${formatTimestamp(ts.start)}-${formatTimestamp(ts.end)} (${ts.start}s-${ts.end}s) [${ts.raw || 'no raw text'}]`);
      });

      // Validate timestamps using video duration
      const validTimestamps = result.timestamps.filter(pair => validateTimestampPair(pair, videoDuration));
      console.log(`${validTimestamps.length} valid timestamps after validation`);

      // 🚀 ENHANCED DEBUG: Show valid timestamps before merging
      console.log(`\n🔍 VALID TIMESTAMPS BEFORE MERGING:`);
      validTimestamps.forEach((ts, i) => {
        console.log(`  Valid ${i + 1}: ${formatTimestamp(ts.start)}-${formatTimestamp(ts.end)} (${ts.start}s-${ts.end}s)`);
      });

      // Merge overlapping ranges (this will show merge details in console)
      const mergedRanges = mergeRanges(validTimestamps, mergeBuffer);
      console.log(`${mergedRanges.length} ranges after merging overlaps (buffer: ${mergeBuffer}s)`);

      // Show merge details for each final range
      mergedRanges.forEach((range, i) => {
        if (range.originalCount > 1) {
          console.log(`📋 Final Range ${i + 1}: ${formatTimestamp(range.start)}-${formatTimestamp(range.end)} [MERGED from ${range.originalCount} sources: ${range.sources.join(', ')}]`);
        } else {
          console.log(`📋 Final Range ${i + 1}: ${formatTimestamp(range.start)}-${formatTimestamp(range.end)}`);
        }
      });

      // Optimize ranges (remove very short segments)
      const optimizedRanges = optimizeRanges(mergedRanges, 3);
      console.log(`${optimizedRanges.length} ranges after optimization`);

      result.timestamps = optimizedRanges;
      result.rawTimestamps = rawTimestamps;
      result.count = optimizedRanges.length;
      result.rawCount = rawTimestamps.length;
    }

    console.log('=== TIMESTAMP COLLECTION COMPLETED ===');
    return result;

  } catch (error) {
    const errorMsg = `Timestamp collection failed: ${error}`;
    console.error(errorMsg);
    result.errors.push(errorMsg);
    return result;
  }
}

/**
 * Process a single claim row to extract timestamps
 * Uses modal-based approach (click details → wait for modal → extract → close modal)
 */
async function processClaimRow(
  row: Element,
  rowNumber: number,
  dryRun: boolean,
  originalUrl: string
): Promise<TimestampPair[]> {
  const preview = getClaimPreview(row);
  console.log(`Processing claim ${rowNumber}: ${preview}`);

  // Find the "See details" button
  const seeDetailsButton = findElement(COPYRIGHT_PAGE_SELECTORS.seeDetailsButton, row);
  if (!seeDetailsButton) {
    console.warn(`❌ No "See details" button found in claim ${rowNumber}`);
    return [];
  }

  if (dryRun) {
    console.log(`[DRY RUN] Would click "See details" button for claim ${rowNumber}`);
    return [];
  }

  // Click the "See details" button (this opens a modal)
  console.log(`🔗 Clicking "See details" button for claim ${rowNumber}`);
  (seeDetailsButton as HTMLElement).click();

  // Wait for modal to appear
  console.log('⏳ Waiting for details modal to load...');
  await delay(1500);

  // Look for the modal dialog
  const modal = await waitForModal();
  if (!modal) {
    console.warn(`⚠️ No modal found for claim ${rowNumber}`);
    return [];
  }

  console.log(`✅ Modal opened for claim ${rowNumber}`);

  // Extract timestamps from the modal content
  const timestamps = extractTimestampsFromModal(modal);

  if (timestamps.length > 0) {
    console.log(`🎯 Found ${timestamps.length} timestamp(s) in modal`);
    timestamps.forEach((ts, i) => {
      const startTime = Math.floor(ts.start / 60) + ':' + String(ts.start % 60).padStart(2, '0');
      const endTime = Math.floor(ts.end / 60) + ':' + String(ts.end % 60).padStart(2, '0');
      console.log(`  Timestamp ${i + 1}: ${startTime} - ${endTime}`);
    });
  } else {
    console.log(`❌ No timestamps found in modal for claim ${rowNumber}`);
  }

  // Close the modal
  await closeModal(modal);
  console.log(`🔙 Closed modal for claim ${rowNumber}`);

  return timestamps;
}

/**
 * Wait for modal dialog to appear
 */
async function waitForModal(maxWaitTime: number = 5000): Promise<Element | null> {
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitTime) {
    // Look for common modal selectors (expanded list)
    const modalSelectors = [
      '[role="dialog"]',
      '.modal',
      '[data-testid="modal"]',
      'ytcp-dialog',
      'tp-yt-paper-dialog',
      '[aria-modal="true"]',
      'dialog',
      '.dialog',
      '.popup',
      '.overlay',
      '[data-dialog]',
      'ytcp-confirmation-dialog',
      'ytcp-entity-page-header-renderer',
      'iron-overlay-container',
      'paper-dialog',
      '.paper-dialog',
      'ytcp-video-metadata-editor-basics-tab',
      'ytcp-video-metadata-editor-advanced-tab'
    ];

    for (const selector of modalSelectors) {
      const modals = document.querySelectorAll(selector);
      for (const modal of modals) {
        if (modal && isElementVisible(modal)) {
          console.log(`✅ Found modal with selector: ${selector}`);
          return modal;
        }
      }
    }

    await delay(200);
  }

  console.warn('⚠️ No modal found within timeout period');
  return null;
}

/**
 * Extract timestamps from modal content - Enhanced for multiple ranges per claim
 */
function extractTimestampsFromModal(modal: Element): TimestampPair[] {
  console.log('🔍 Extracting timestamps from modal content...');

  // Get all text content from the modal
  const modalText = modal.textContent || '';
  console.log('📄 Modal text length:', modalText.length);

  // Enhanced timestamp patterns for multiple ranges
  const timestampPatterns = [
    // YouTube specific "Content found in" patterns with pipe separators
    // Example: "24:29 - 26:08 | 32:12 - 33:52"
    /Content found in\s+((?:\d{1,2}:\d{2}(?::\d{2})?\s*[-–—]\s*\d{1,2}:\d{2}(?::\d{2})?(?:\s*\|\s*)?)+)/gi,

    // Direct multiple ranges with pipe separators
    // Example: "22:20 - 24:18 | 30:03 - 32:01"
    /((?:\d{1,2}:\d{2}(?::\d{2})?\s*[-–—]\s*\d{1,2}:\d{2}(?::\d{2})?(?:\s*\|\s*)?)+)/g,

    // Single range patterns (fallback)
    /(\d{1,2}:\d{2}(?::\d{2})?)\s*[-–—]\s*(\d{1,2}:\d{2}(?::\d{2})?)/g,

    // "From X to Y" patterns
    /from\s+(\d{1,2}:\d{2}(?::\d{2})?)\s+to\s+(\d{1,2}:\d{2}(?::\d{2})?)/gi,

    // "Between X and Y" patterns
    /between\s+(\d{1,2}:\d{2}(?::\d{2})?)\s+and\s+(\d{1,2}:\d{2}(?::\d{2})?)/gi,

    // Parenthetical ranges "(1:23 - 2:45)"
    /\((\d{1,2}:\d{2}(?::\d{2})?)\s*[-–—]\s*(\d{1,2}:\d{2}(?::\d{2})?)\)/g,
  ];

  const timestamps: TimestampPair[] = [];
  const seenTimestamps = new Set<string>(); // Track duplicates
  let totalMatches = 0;

  timestampPatterns.forEach((pattern, patternIndex) => {
    const matches = [...modalText.matchAll(pattern)];
    totalMatches += matches.length;

    if (matches.length > 0) {
      console.log(`🎯 Pattern ${patternIndex + 1} found ${matches.length} match(es)`);
    }

    matches.forEach((match, matchIndex) => {
      // Handle pipe-separated multiple ranges (patterns 1 and 2)
      if (patternIndex <= 1 && match[1]) {
        // This is a multi-range pattern like "24:29 - 26:08 | 32:12 - 33:52"
        const multiRangeText = match[1];
        console.log(`🔍 Processing multi-range text: "${multiRangeText}"`);

        // Split by pipe separator and process each range
        const individualRanges = multiRangeText.split('|').map(r => r.trim());

        individualRanges.forEach((rangeText, rangeIndex) => {
          console.log(`  📍 Processing individual range ${rangeIndex + 1}: "${rangeText}"`);

          // Extract start and end times from this individual range
          const rangeMatch = rangeText.match(/(\d{1,2}:\d{2}(?::\d{2})?)\s*[-–—]\s*(\d{1,2}:\d{2}(?::\d{2})?)/);

          if (rangeMatch) {
            const start = parseTimestamp(rangeMatch[1]);
            const end = parseTimestamp(rangeMatch[2]);

            if (start !== null && end !== null && start < end) {
              const rawText = `${rangeMatch[1]} - ${rangeMatch[2]}`;
              const timestampKey = `${start}-${end}`;
              const duration = end - start;

              // Only add if we haven't seen this exact timestamp before
              if (!seenTimestamps.has(timestampKey)) {
                seenTimestamps.add(timestampKey);
                timestamps.push({ start, end, raw: rawText });
                console.log(`    ✅ Found timestamp ${timestamps.length}: ${rawText} (${start}s - ${end}s, duration: ${Math.floor(duration/60)}m ${duration%60}s)`);
              } else {
                console.log(`    🔄 Duplicate timestamp skipped: ${rawText}`);
              }
            } else {
              console.warn(`    ❌ Invalid timestamp range: ${rangeMatch[1]} - ${rangeMatch[2]} (start: ${start}, end: ${end})`);
            }
          } else {
            console.warn(`    ⚠️ Could not parse range: "${rangeText}"`);
          }
        });

        return; // Skip the single-range processing below
      }

      // Handle single ranges (patterns 3+)
      if (match[1] && match[2]) {
        const start = parseTimestamp(match[1]);
        const end = parseTimestamp(match[2]);

        if (start !== null && end !== null && start < end) {
          const rawText = `${match[1]} - ${match[2]}`;
          const timestampKey = `${start}-${end}`; // Unique key for deduplication
          const duration = end - start;

          // Enhanced format validation with aggressive error detection
          const startParts = match[1].split(':');
          const endParts = match[2].split(':');

          // Reject mismatched formats (this prevents parsing errors)
          if (startParts.length !== endParts.length) {
            console.warn(`⚠️ Format mismatch rejected: ${rawText} (start has ${startParts.length} parts, end has ${endParts.length} parts)`);
            return;
          }

          // Reject invalid part counts
          if (startParts.length < 2 || startParts.length > 3) {
            console.warn(`⚠️ Invalid format rejected: ${rawText} (expected 2-3 time parts, got ${startParts.length})`);
            return;
          }

          // Only add if we haven't seen this exact timestamp before
          if (!seenTimestamps.has(timestampKey)) {
            seenTimestamps.add(timestampKey);
            timestamps.push({ start, end, raw: rawText });
            console.log(`✅ Found timestamp ${timestamps.length}: ${rawText} (${start}s - ${end}s, duration: ${Math.floor(duration/60)}m ${duration%60}s)`);

            // Warn about potentially problematic ranges (but don't reject)
            if (duration > 10 * 60) { // Over 10 minutes
              console.warn(`⚠️ Large timestamp range detected: ${rawText} - verify this is correct`);
            }
          } else {
            console.log(`🔄 Duplicate timestamp skipped: ${rawText}`);
          }
        } else {
          console.warn(`❌ Invalid timestamp range: ${match[1]} - ${match[2]} (start: ${start}, end: ${end})`);
        }
      }
    });
  });

  // Enhanced debugging for multiple ranges
  console.log(`📊 Extraction summary: ${totalMatches} total matches, ${timestamps.length} valid unique timestamps`);
  console.log(`📋 All extracted timestamps:`, timestamps.map(t => `${t.raw} (${t.start}s-${t.end}s)`));

  if (timestamps.length > 1) {
    console.log(`🎵 Multiple timestamp ranges detected in single claim:`);
    timestamps.forEach((ts, i) => {
      console.log(`  Range ${i + 1}: ${ts.raw} (${Math.floor((ts.end - ts.start)/60)}m ${(ts.end - ts.start)%60}s)`);
    });
  }

  // Debug: If no timestamps found, show a sample of the modal text
  if (timestamps.length === 0) {
    console.warn('⚠️ No timestamps found in modal. Modal text sample:');
    console.warn(modalText.substring(0, 500).replace(/\s+/g, ' '));

    // Try to find any time-like patterns for debugging
    const timePatterns = /\d{1,2}:\d{2}/g;
    const timeMatches = modalText.match(timePatterns);
    if (timeMatches) {
      console.warn('Found time-like patterns:', timeMatches.slice(0, 10));
    }
  }

  return timestamps;
}

/**
 * Close modal dialog
 */
async function closeModal(modal: Element): Promise<void> {
  // Look for close button selectors
  const closeSelectors = [
    '[aria-label="Close"]',
    '[aria-label="close"]',
    '.close',
    '.close-button',
    '[data-testid="close"]',
    'button[aria-label*="close" i]',
    'button[title*="close" i]',
    '[role="button"][aria-label*="close" i]'
  ];

  for (const selector of closeSelectors) {
    const closeButton = modal.querySelector(selector);
    if (closeButton && isElementVisible(closeButton)) {
      console.log(`Clicking close button with selector: ${selector}`);
      (closeButton as HTMLElement).click();
      await delay(500);
      return;
    }
  }

  // If no close button found, try pressing Escape key
  console.log('No close button found, trying Escape key');
  document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
  await delay(500);

  // If modal still exists, try clicking outside it
  if (document.contains(modal)) {
    console.log('Modal still open, trying to click outside');
    const backdrop = document.querySelector('.backdrop, .overlay, [data-testid="backdrop"]');
    if (backdrop) {
      (backdrop as HTMLElement).click();
      await delay(500);
    }
  }
}

/**
 * Check if element is visible
 */
function isElementVisible(element: Element): boolean {
  const rect = element.getBoundingClientRect();
  const style = window.getComputedStyle(element);

  return (
    rect.width > 0 &&
    rect.height > 0 &&
    style.display !== 'none' &&
    style.visibility !== 'hidden' &&
    style.opacity !== '0'
  );
}

/**
 * Utility function for random delays (300-700ms)
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Random delay between actions to mimic human behavior
 */
function randomDelay(): Promise<void> {
  const minDelay = 300;
  const maxDelay = 700;
  const delayMs = minDelay + Math.random() * (maxDelay - minDelay);
  return delay(delayMs);
}

/**
 * Listen for messages from background script
 */
chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
  console.log('Collect-timestamps script received message:', message);

  if (message.action === 'START_COLLECTION') {
    try {
      const mergeBuffer = message.settings?.mergeBuffer || 15;
      console.log(`Using merge buffer: ${mergeBuffer} seconds`);

      const result = await collectTimestamps(message.dryRun, mergeBuffer);

      // Check for errors
      if (result.errors.length > 0) {
        console.warn('Collection completed with errors:', result.errors);
      }

      // Send results back to background script (let background handle storage)
      chrome.runtime.sendMessage({
        action: 'TIMESTAMPS_COLLECTED',
        data: {
          timestamps: result.timestamps,
          count: result.count,
          rawCount: result.rawCount, // Add raw count for UI display
          totalClaims: result.totalClaims, // Add total claims count for UI display
          errors: result.errors,
        },
        // Include batch mode information if present
        batchMode: message.batchMode,
        batchVideoIndex: message.batchVideoIndex,
        operationId: message.operationId
      });

    } catch (error) {
      console.error('Collection failed:', error);
      chrome.runtime.sendMessage({
        action: 'ERROR',
        error: 'Failed to collect timestamps',
        details: error instanceof Error ? error.message : String(error),
        // Include batch mode information if present
        batchMode: message.batchMode,
        batchVideoIndex: message.batchVideoIndex,
        operationId: message.operationId
      });
    }
  }

  return true;
});

console.log('ClaimCutter collect-timestamps script loaded');
