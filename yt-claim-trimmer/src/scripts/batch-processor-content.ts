/**
 * Batch Processor Content Script
 *
 * This script is no longer used - batch processing is now handled by the background script
 * to avoid navigation issues that terminate content script execution.
 *
 * The background script now handles:
 * - Sequential video processing
 * - Navigation between copyright and editor pages
 * - Coordination of timestamp collection and cut application
 */

console.log('🚀 Batch processor content script loaded (legacy - not used)');

// Legacy message listener for compatibility (not used in new implementation)
chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
  console.log('Batch processor received message (legacy):', message);

  if (message.action === 'START_BATCH_PROCESSING') {
    console.log('⚠️ Legacy batch processor called - this should not happen with new implementation');
    sendResponse({ error: 'Legacy batch processor - use background script implementation' });
    return true;
  }

  return false;
});
