/**
 * Content Script B: Cut Application
 * Applies cuts to YouTube Studio editor based on collected timestamps
 */

// Inline version constant (content scripts can't use imports)
const CLAIMCUTTER_VERSION = '2.1.04';
function getVersionString(component: string): string {
  return `🚀 ClaimCutter ${component} v${CLAIMCUTTER_VERSION} - Enhanced Versioning`;
}

console.log(getVersionString('Apply-Cuts Script File'));
console.log('🚀 APPLY-CUTS SCRIPT: File parsing started');

// Add message listener for ping tests
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  console.log('🔍 Apply-cuts script received message:', message);

  if (message.action === 'PING') {
    console.log('🏓 Apply-cuts script responding to ping');
    sendResponse({ status: 'alive', timestamp: Date.now() });
    return true;
  }

  return false;
});

// Inline all utilities to avoid import issues

// Selectors for YouTube Studio editor page (Updated based on actual DOM inspection)
const EDITOR_PAGE_SELECTORS = {
  // Main timeline timecode input - the HH:MM:SS field above the timeline
  timelineTimecode: 'input.style-scope.ytcp-media-timestamp-input[role="timer"]',

  // Initial "Trim & cut" button - anchor tag with exact text
  trimAndCutButton: 'a.style-scope.ytve-entrypoint-options-panel',

  // "New Cut" button - used after entering trim mode
  newCutButton: 'ytcp-button.style-scope.ytve-trim-options-panel, #new-cut-button',

  // Timestamp inputs in trim dialog (when setting start/end times)
  timestampInputs: 'input.style-scope.ytcp-media-timestamp-input[role="timer"]',

  // Cut button (checkmark) - applies the cut after entering start/end times
  // Note: Use dynamic detection instead of static selector
  cutButton: 'ytcp-icon-button[aria-label="Cut"]',

  // Cancel button in trim dialog
  cancelButton: 'ytcp-icon-button[aria-label="Cancel"].style-scope.ytve-trim-options-panel',

  // Save button (final save) - using aria-label instead of :contains()
  saveButton: '[aria-label*="save"], [aria-label*="Save"], ytcp-button[aria-label*="save"]',

  // Final confirmation dialog
  confirmChangesDialog: '[role="dialog"]',
  confirmChangesButton: '[aria-label*="confirm"], [aria-label*="Confirm"]',
};

// Timestamp pair interface
interface TimestampPair {
  start: number;
  end: number;
}

// Utility functions
function findElement(selector: string, parent: Element | Document = document): Element | null {
  try {
    return parent.querySelector(selector);
  } catch (error) {
    console.warn(`Invalid selector: ${selector}`, error);
    return null;
  }
}

function waitForElement(selector: string, timeout: number = 10000): Promise<Element> {
  return new Promise((resolve, reject) => {
    const element = findElement(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = findElement(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element not found: ${selector}`));
    }, timeout);
  });
}

/**
 * Convert seconds to timestamp format that YouTube Studio expects
 * Uses smart detection based on video length and existing input format
 */
function secondsToTimestamp(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  // Try to detect what format YouTube Studio is expecting by checking existing inputs
  const existingInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  let detectedFormat = 'auto';

  for (const input of existingInputs) {
    const value = (input as HTMLInputElement).value;
    if (value && value.includes(':')) {
      const parts = value.split(':');
      if (parts.length === 4) {
        detectedFormat = 'HH:MM:SS:FF';
        console.log(`🔍 Detected HH:MM:SS:FF format from existing input: ${value}`);
        break;
      } else if (parts.length === 3) {
        detectedFormat = 'MM:SS:FF';
        console.log(`🔍 Detected MM:SS:FF format from existing input: ${value}`);
        break;
      } else if (parts.length === 2 && parseInt(parts[0]) > 59) {
        detectedFormat = 'MM:SS_TOTAL';
        console.log(`🔍 Detected MM:SS total minutes format from existing input: ${value}`);
        break;
      } else if (parts.length === 2) {
        detectedFormat = 'MM:SS';
        console.log(`🔍 Detected MM:SS format from existing input: ${value}`);
        break;
      }
    }
  }

  // Fallback logic if no existing inputs found
  if (detectedFormat === 'auto') {
    // For videos longer than 60 minutes, use HH:MM:SS:FF
    // For videos 60 minutes or shorter, use MM:SS:FF
    if (seconds > 3600) { // More than 60 minutes
      detectedFormat = 'HH:MM:SS:FF';
      console.log(`🔍 Auto-detected HH:MM:SS:FF format (${Math.floor(seconds/3600)}h ${Math.floor((seconds%3600)/60)}m video)`);
    } else {
      detectedFormat = 'MM:SS:FF';
      console.log(`🔍 Auto-detected MM:SS:FF format (${Math.floor(seconds/60)}m video)`);
    }
  }

  // Always use 00 frames for simplicity (YouTube Studio accepts this)
  const frames = '00';

  // Return appropriate format
  switch (detectedFormat) {
    case 'HH:MM:SS:FF':
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}:${frames}`;

    case 'MM:SS:FF':
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}:${frames}`;

    case 'MM:SS_TOTAL':
      // Legacy support for total minutes format (if detected from existing inputs)
      const totalMinutesLegacy = Math.floor(seconds / 60);
      return `${totalMinutesLegacy}:${secs.toString().padStart(2, '0')}`;

    case 'MM:SS':
    default:
      // Legacy support for MM:SS format (if detected from existing inputs)
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}

function mergeRanges(ranges: TimestampPair[]): TimestampPair[] {
  if (ranges.length === 0) return [];

  console.log('🔄 Merging ranges - Input:', ranges.map(r => `${r.start}s-${r.end}s`));

  const sorted = [...ranges].sort((a, b) => a.start - b.start);
  const merged: TimestampPair[] = [sorted[0]];

  console.log('📊 Sorted ranges:', sorted.map(r => `${r.start}s-${r.end}s`));

  for (let i = 1; i < sorted.length; i++) {
    const current = sorted[i];
    const last = merged[merged.length - 1];

    // Use 5-second buffer to prevent timeline positioning conflicts
    // while still preserving meaningful content gaps between claims
    const buffer = 5;
    const shouldMerge = current.start <= last.end + buffer;

    console.log(`🔍 Checking merge: Current(${current.start}s-${current.end}s) vs Last(${last.start}s-${last.end}s)`);
    console.log(`   Gap: ${current.start - last.end}s, Buffer: ${buffer}s, Should merge: ${shouldMerge}`);

    if (shouldMerge) {
      const oldEnd = last.end;
      last.end = Math.max(last.end, current.end);
      console.log(`✅ Merged: ${last.start}s-${oldEnd}s + ${current.start}s-${current.end}s → ${last.start}s-${last.end}s`);
    } else {
      merged.push(current);
      console.log(`➕ Added separate range: ${current.start}s-${current.end}s`);
    }
  }

  console.log('🎯 Final merged ranges:', merged.map(r => `${r.start}s-${r.end}s`));
  console.log(`📈 Consolidation: ${ranges.length} → ${merged.length} ranges`);

  return merged;
}

function validateRanges(ranges: TimestampPair[]): TimestampPair[] {
  return ranges.filter(range => range.start >= 0 && range.end > range.start && range.end - range.start >= 1);
}

// Delay functions
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function shortDelay(): Promise<void> {
  return delay(200 + Math.random() * 100);
}

function mediumDelay(): Promise<void> {
  return delay(500 + Math.random() * 200);
}

function actionDelay(action: string): Promise<void> {
  const delays = {
    click: 300 + Math.random() * 200,
    type: 150 + Math.random() * 100,
    navigate: 800 + Math.random() * 400,
  };
  return delay(delays[action as keyof typeof delays] || 300);
}

function typingDelay(text: string): Promise<void> {
  return delay(text.length * 50 + Math.random() * 100);
}

function batchDelay(index: number, total: number): Promise<void> {
  const baseDelay = 1000;
  const randomDelay = Math.random() * 500;
  return delay(baseDelay + randomDelay);
}

function waitForCondition(
  condition: () => boolean,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const check = () => {
      if (condition()) {
        resolve();
      } else if (Date.now() - startTime > timeout) {
        reject(new Error('Condition timeout'));
      } else {
        setTimeout(check, interval);
      }
    };

    check();
  });
}

/**
 * Set value in an input field with proper event triggering
 * Uses the character-by-character approach that worked in console testing
 */
async function setInputValue(input: HTMLInputElement, value: string, fieldName?: string): Promise<void> {
  const fieldLabel = fieldName || 'input field';
  console.log(`🔍 DEBUG: Setting ${fieldLabel} value to: "${value}"`);
  console.log(`🔍 DEBUG: Input element:`, input);
  console.log(`🔍 DEBUG: Input current value before setting: "${input.value}"`);

  // Focus the input
  input.focus();
  await shortDelay();

  // Clear existing content by selecting all and deleting
  input.select();
  await shortDelay();

  // Clear the input
  const oldValue = input.value;
  input.value = '';
  input.dispatchEvent(new Event('input', { bubbles: true }));
  console.log(`🔍 DEBUG: ${fieldLabel} cleared from "${oldValue}" to "${input.value}"`);
  await shortDelay();

  // Type character by character (confirmed working approach)
  console.log(`🔍 DEBUG: Starting character-by-character input for ${fieldLabel}...`);
  for (let i = 0; i < value.length; i++) {
    input.value += value[i];
    input.dispatchEvent(new Event('input', { bubbles: true }));
    console.log(`🔍 DEBUG: ${fieldLabel} after char ${i + 1}/${value.length}: "${input.value}"`);
    await delay(50); // Small delay between characters
  }

  // Final change event
  input.dispatchEvent(new Event('change', { bubbles: true }));
  console.log(`✅ DEBUG: ${fieldLabel} FINAL VALUE: "${input.value}" (expected: "${value}")`);
  console.log(`✅ DEBUG: ${fieldLabel} match: ${input.value === value ? 'SUCCESS' : 'MISMATCH!'}`);
  await shortDelay();
}

// Modal functions
function showConfirmationModal(options: {
  title: string;
  message?: string;
  ranges: TimestampPair[];
  dryRun: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}): HTMLElement {
  const modal = document.createElement('div');
  modal.id = 'claimcutter-confirmation-modal';

  const totalDuration = options.ranges.reduce((sum, range) => sum + (range.end - range.start), 0);
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  modal.innerHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    ">
      <div style="
        background: white;
        border-radius: 8px;
        padding: 24px;
        max-width: 500px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.3);
      ">
        <h2 style="margin: 0 0 16px 0; color: #1f2937;">${options.title}</h2>

        ${options.message ? `<p style="color: #f59e0b; margin-bottom: 16px;">${options.message}</p>` : ''}

        <div style="margin-bottom: 16px;">
          <strong>Summary:</strong>
          <ul style="margin: 8px 0; padding-left: 20px;">
            <li>${options.ranges.length} cuts to apply</li>
            <li>Total duration: ${formatTime(totalDuration)}</li>
            <li>Mode: ${options.dryRun ? 'Preview only' : 'Apply cuts'}</li>
          </ul>
        </div>

        <div style="margin-bottom: 20px;">
          <strong>Cuts to apply:</strong>
          <div style="max-height: 200px; overflow-y: auto; border: 1px solid #e5e7eb; border-radius: 4px; padding: 8px; margin-top: 8px;">
            ${options.ranges.map((range, i) => `
              <div style="padding: 4px 0; border-bottom: 1px solid #f3f4f6;">
                Cut ${i + 1}: ${secondsToTimestamp(range.start)} - ${secondsToTimestamp(range.end)} (${formatTime(range.end - range.start)})
              </div>
            `).join('')}
          </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: flex-end;">
          <button id="claimcutter-cancel" style="
            background: #6b7280;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          ">Cancel</button>
          <button id="claimcutter-confirm" style="
            background: ${options.dryRun ? '#3b82f6' : '#ef4444'};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          ">${options.dryRun ? 'Preview' : 'Apply Cuts'}</button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  modal.querySelector('#claimcutter-confirm')?.addEventListener('click', options.onConfirm);
  modal.querySelector('#claimcutter-cancel')?.addEventListener('click', options.onCancel);

  return modal;
}

function removeModal(modal: HTMLElement): void {
  if (modal.parentNode) {
    modal.parentNode.removeChild(modal);
  }
}

interface CutApplicationResult {
  applied: number;
  failed: number;
  errors: string[];
}

/**
 * Main cut application function
 */
async function applyCuts(): Promise<CutApplicationResult> {
  console.log(getVersionString('Apply-Cuts Function'));
  console.log('Starting cut application...');

  try {
    // 🚀 CRITICAL FIX: Always get the LATEST timestamp data with timestamp validation
    console.log('🔍 Getting latest timestamp data with validation...');

    // Get both storage sources and compare timestamps to ensure we use the most recent
    const directStorageResult = await chrome.runtime.sendMessage({
      action: 'GET_STORAGE',
      keys: [
        'claimcutter_timestamps',
        'claimcutter_collection_complete',
        'claimcutter_collection_results',
        'claimcutter_popup_state',
        'claimcutter_dry_run'
      ]
    });

    const popupStateResult = await chrome.runtime.sendMessage({
      action: 'GET_POPUP_STATE'
    });

    console.log('📊 Data source comparison:');
    console.log('  Direct storage keys:', Object.keys(directStorageResult || {}));
    console.log('  Popup state keys:', Object.keys(popupStateResult || {}));

    // Try both approaches - popup state first, then fallback to direct storage
    let timestamps: TimestampPair[] = [];
    let collectionErrors: string[] = [];
    let dryRun = false;
    let settings: any = null;
    let dataSource = 'none';

    // 🎯 PRIORITY: Use the most recent data based on completion timestamp
    let directTimestamp = 0;
    let popupTimestamp = 0;

    if (directStorageResult.claimcutter_collection_results?.completedAt) {
      directTimestamp = directStorageResult.claimcutter_collection_results.completedAt;
    }

    if (popupStateResult.collectionResults?.completedAt) {
      popupTimestamp = popupStateResult.collectionResults.completedAt;
    }

    console.log(`⏰ Timestamp comparison - Direct: ${directTimestamp}, Popup: ${popupTimestamp}`);

    // Use the data source with the most recent completion timestamp
    if (popupTimestamp > directTimestamp && popupStateResult.collectionComplete && popupStateResult.collectionResults) {
      console.log('✅ Using popup state data (most recent)...');
      timestamps = popupStateResult.collectionResults.timestamps || [];
      collectionErrors = popupStateResult.collectionResults.errors || [];
      settings = popupStateResult.settings || null;
      dataSource = 'popup';
    } else if (directTimestamp > 0 && directStorageResult.claimcutter_collection_complete && directStorageResult.claimcutter_timestamps) {
      console.log('✅ Using direct storage data (most recent)...');
      timestamps = directStorageResult.claimcutter_timestamps || [];
      collectionErrors = directStorageResult.claimcutter_errors || [];
      dataSource = 'storage';
    } else {
      throw new Error('No valid timestamp collection found - both sources are empty or invalid');
    }

    console.log(`📋 Using ${dataSource} data: ${timestamps.length} timestamps found`);
    console.log('🔍 Timestamp details:', timestamps.map(t => `${t.start}s-${t.end}s`));

    // 🚀 CRITICAL VALIDATION: Ensure we have valid timestamp data
    if (!timestamps || timestamps.length === 0) {
      throw new Error(`No timestamps found in ${dataSource} data source`);
    }

    // Validate each timestamp pair
    for (let i = 0; i < timestamps.length; i++) {
      const ts = timestamps[i];
      if (typeof ts.start !== 'number' || typeof ts.end !== 'number') {
        throw new Error(`Invalid timestamp format at index ${i}: start=${ts.start}, end=${ts.end}`);
      }
      if (ts.start >= ts.end) {
        throw new Error(`Invalid timestamp range at index ${i}: start=${ts.start} >= end=${ts.end}`);
      }
      console.log(`✅ Validated timestamp ${i + 1}: ${ts.start}s (${secondsToTimestamp(ts.start)}) → ${ts.end}s (${secondsToTimestamp(ts.end)})`);
    }

    // Get dry run flag
    dryRun = directStorageResult.claimcutter_dry_run || false;

    console.log('Settings retrieved:', settings);
    console.log('🚀 Turbo mode status:', {
      turboMode: settings?.turboMode,
      autoSave: settings?.autoSave,
      dryRun: dryRun
    });

    if (timestamps.length === 0) {
      throw new Error('No timestamps found in collection results');
    }

    console.log(`Processing ${timestamps.length} timestamp pairs`, { dryRun });

    // Timestamps are already merged and optimized from collection phase
    // Just validate them to ensure they're in the correct format
    const validTimestamps = validateRanges(timestamps);

    console.log(`${validTimestamps.length} ranges after validation (already merged from collection)`);

    if (validTimestamps.length === 0) {
      throw new Error('No valid timestamp ranges to process');
    }

    // User has already confirmed by clicking "Apply Cuts" in the popup
    console.log(`Ready to apply ${validTimestamps.length} cuts to editor...`);

    if (dryRun) {
      console.log('Dry run completed - no cuts applied');
      return {
        applied: 0,
        failed: 0,
        errors: [],
      };
    }

    // Apply cuts to the editor
    const applicationResult = await applyCutsToEditor(validTimestamps);

    // Check if turbo mode with auto-save is enabled
    const isTurboMode = settings?.turboMode || false;
    const isAutoSave = settings?.autoSave || false;

    if (isTurboMode && isAutoSave) {
      console.log('🚀 Turbo mode with auto-save enabled - automatically saving...');
      await autoSaveChanges();
    } else {
      console.log('Manual save required - highlighting save button...');
      await highlightSaveButton();
    }

    return applicationResult;

  } catch (error) {
    console.error('Cut application failed:', error);
    return {
      applied: 0,
      failed: 1,
      errors: [error instanceof Error ? error.message : String(error)],
    };
  }
}

/**
 * Show confirmation modal and wait for user decision
 */
async function showConfirmationModalAndWait(
  ranges: TimestampPair[],
  dryRun: boolean,
  collectionErrors: string[]
): Promise<boolean> {
  return new Promise((resolve) => {
    let modal: HTMLElement | null = null;

    const handleConfirm = () => {
      if (modal) {
        removeModal(modal);
      }
      resolve(true);
    };

    const handleCancel = () => {
      if (modal) {
        removeModal(modal);
      }
      resolve(false);
    };

    const title = dryRun ? 'Preview Cuts (Dry Run)' : 'Confirm Cut Application';
    const message = collectionErrors.length > 0
      ? `Note: ${collectionErrors.length} warnings occurred during collection. Please review the cuts below.`
      : undefined;

    modal = showConfirmationModal({
      title,
      message,
      ranges,
      dryRun,
      onConfirm: handleConfirm,
      onCancel: handleCancel,
    });
  });
}

/**
 * Apply cuts to the YouTube Studio editor
 */
async function applyCutsToEditor(ranges: TimestampPair[]): Promise<CutApplicationResult> {
  console.log(`Applying ${ranges.length} cuts to editor...`);

  const result: CutApplicationResult = {
    applied: 0,
    failed: 0,
    errors: [],
  };

  try {
    // Wait for editor to be ready
    console.log('Waiting for editor timeline to load...');
    await waitForElement(EDITOR_PAGE_SELECTORS.timelineTimecode, 15000);
    await mediumDelay(); // Give editor time to fully initialize

    // 🚀 NEW: Start persistent debugger session for the entire cut application process
    console.log('🚀 Starting persistent debugger session for cut application...');
    try {
      await startDebuggerSession();
      console.log('✅ Persistent debugger session started successfully');
    } catch (error) {
      console.warn('⚠️ Failed to start debugger session, continuing without trusted events:', error);
    }

    // Apply each cut
    for (let i = 0; i < ranges.length; i++) {
      const range = ranges[i];
      console.log(`Applying cut ${i + 1}/${ranges.length}: ${range.start}s - ${range.end}s`);

      try {
        await applySingleCut(range, i + 1, ranges.length);
        result.applied++;
        console.log(`✓ Cut ${i + 1} applied successfully`);
      } catch (error) {
        result.failed++;
        const errorMsg = `Failed to apply cut ${i + 1}: ${error}`;
        console.error(errorMsg);
        console.error('❌ Cut application error details:', error);
        result.errors.push(errorMsg);

        // In turbo mode, we might want to continue with other cuts
        console.log(`⚠️ Continuing with remaining cuts (${ranges.length - i - 1} left)...`);
      }

      // Batch delay between cuts
      if (i < ranges.length - 1) {
        await batchDelay(i, ranges.length);
      }
    }

    console.log(`Cut application completed: ${result.applied} applied, ${result.failed} failed`);

    // Position timeline at the first cut location for visual feedback
    if (result.applied > 0 && ranges.length > 0) {
      console.log('🎯 Positioning timeline at first cut location for visual feedback...');
      try {
        const firstCutStart = ranges[0].start;
        await navigateToTimestamp(firstCutStart);
        console.log(`✅ Timeline positioned at ${firstCutStart}s (first cut location)`);

        // Show visual confirmation of timeline positioning
        showTimelinePositionFeedback(firstCutStart, ranges.length);
      } catch (error) {
        console.warn('⚠️ Failed to position timeline at cut location:', error);
        // Non-critical error - don't fail the entire operation
      }
    }

    return result;

  } catch (error) {
    const errorMsg = `Editor interaction failed: ${error}`;
    console.error(errorMsg);
    result.errors.push(errorMsg);
    return result;
  } finally {
    // 🚀 NEW: Always end the persistent debugger session when done
    console.log('🔧 Ending persistent debugger session...');
    try {
      await endDebuggerSession();
      console.log('✅ Persistent debugger session ended successfully');
    } catch (error) {
      console.warn('⚠️ Failed to end debugger session:', error);
    }
  }
}

/**
 * Start persistent debugger session for cut application
 */
async function startDebuggerSession(): Promise<void> {
  console.log('📨 Starting persistent debugger session...');

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(
      {
        action: 'START_DEBUGGER_SESSION'
      },
      (response) => {
        if (chrome.runtime.lastError) {
          console.error('❌ Failed to start debugger session:', chrome.runtime.lastError.message);
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        if (response && response.success) {
          console.log('✅ Debugger session started successfully');
          resolve();
        } else {
          const error = response?.error || 'Unknown error';
          console.error('❌ Debugger session start failed:', error);
          reject(new Error(error));
        }
      }
    );
  });
}

/**
 * End persistent debugger session
 */
async function endDebuggerSession(): Promise<void> {
  console.log('📨 Ending persistent debugger session...');

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(
      {
        action: 'END_DEBUGGER_SESSION'
      },
      (response) => {
        if (chrome.runtime.lastError) {
          console.error('❌ Failed to end debugger session:', chrome.runtime.lastError.message);
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        if (response && response.success) {
          console.log('✅ Debugger session ended successfully');
          resolve();
        } else {
          const error = response?.error || 'Unknown error';
          console.error('❌ Debugger session end failed:', error);
          reject(new Error(error));
        }
      }
    );
  });
}

/**
 * Request trusted Tab event from background script
 * Content scripts cannot use chrome.debugger API, so we delegate to background script
 */
async function requestTrustedTabEvent(): Promise<void> {
  console.log('📨 Requesting trusted Tab event from background script...');

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(
      {
        action: 'TRUSTED_TAB_EVENT'
      },
      (response) => {
        if (chrome.runtime.lastError) {
          console.error('❌ Failed to request trusted Tab event:', chrome.runtime.lastError.message);
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        if (response && response.success) {
          console.log('✅ Trusted Tab event completed successfully');
          resolve();
        } else {
          const error = response?.error || 'Unknown error';
          console.error('❌ Trusted Tab event failed:', error);
          reject(new Error(error));
        }
      }
    );
  });
}

/**
 * Request trusted Enter event from background script
 * Content scripts cannot use chrome.debugger API, so we delegate to background script
 */
async function requestTrustedEnterEvent(): Promise<void> {
  console.log('📨 Requesting trusted Enter event from background script...');

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(
      {
        action: 'TRUSTED_ENTER_EVENT'
      },
      (response) => {
        if (chrome.runtime.lastError) {
          console.error('❌ Failed to request trusted Enter event:', chrome.runtime.lastError.message);
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        if (response && response.success) {
          console.log('✅ Trusted Enter event completed successfully');
          resolve();
        } else {
          const error = response?.error || 'Unknown error';
          console.error('❌ Trusted Enter event failed:', error);
          reject(new Error(error));
        }
      }
    );
  });
}

/**
 * Attempt to trigger timeline update by simulating focus change from start to end input
 */
async function attemptTrustedFocusChange(startInput: HTMLInputElement, endInput: HTMLInputElement): Promise<void> {
  console.log('🚀 Attempting trusted focus change to trigger timeline update...');

  try {
    // Get current tab ID
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.id) {
      throw new Error('Could not get current tab ID');
    }

    console.log('📋 Attaching debugger to tab:', tab.id);

    // Attach debugger
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.attach({ tabId: tab.id! }, '1.3', () => {
        if (chrome.runtime.lastError) {
          reject(new Error(`Failed to attach debugger: ${chrome.runtime.lastError.message}`));
          return;
        }
        console.log('✅ Debugger attached successfully');
        resolve();
      });
    });

    // Focus the start input first (where we just set the value)
    startInput.focus();
    await delay(100);

    console.log('⌨️ Dispatching trusted Tab key event to move from start to end input...');

    // Dispatch trusted Tab key event to move focus to end input
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId: tab.id! },
        'Input.dispatchKeyEvent',
        {
          type: 'keyDown',
          key: 'Tab',
          keyCode: 9,
          code: 'Tab',
          windowsVirtualKeyCode: 9
        },
        (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Tab keyDown failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve();
        }
      );
    });

    // Small delay between keyDown and keyUp
    await delay(50);

    // Dispatch keyUp event
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId: tab.id! },
        'Input.dispatchKeyEvent',
        {
          type: 'keyUp',
          key: 'Tab',
          keyCode: 9,
          code: 'Tab',
          windowsVirtualKeyCode: 9
        },
        (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Tab keyUp failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve();
        }
      );
    });

    console.log('✅ Trusted Tab event dispatched successfully');

    // Wait for potential timeline update
    await delay(300);

    // Detach debugger
    chrome.debugger.detach({ tabId: tab.id! }, () => {
      if (chrome.runtime.lastError) {
        console.warn('⚠️ Error detaching debugger:', chrome.runtime.lastError.message);
      } else {
        console.log('🔧 Debugger detached');
      }
    });

    console.log('🎯 Trusted focus change sequence completed');

  } catch (error) {
    console.error('❌ Trusted focus change failed:', error);

    // Try to detach debugger in case of error
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab.id) {
        chrome.debugger.detach({ tabId: tab.id }, () => {
          console.log('🔧 Debugger detached after error');
        });
      }
    } catch (detachError) {
      console.warn('⚠️ Failed to detach debugger after error:', detachError);
    }

    throw error;
  }
}

/**
 * Attempt to trigger timeline update using Chrome Debugger API trusted events
 * This simulates the manual focus change that triggers timeline bracket updates
 */
async function attemptTrustedTimelineUpdate(currentInput: HTMLInputElement, targetInput: HTMLInputElement): Promise<void> {
  console.log('🚀 Attempting trusted events for timeline update...');

  try {
    // Get current tab ID
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.id) {
      throw new Error('Could not get current tab ID');
    }

    console.log('📋 Attaching debugger to tab:', tab.id);

    // Attach debugger
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.attach({ tabId: tab.id! }, '1.3', () => {
        if (chrome.runtime.lastError) {
          reject(new Error(`Failed to attach debugger: ${chrome.runtime.lastError.message}`));
          return;
        }
        console.log('✅ Debugger attached successfully');
        resolve();
      });
    });

    // Focus the current input first (where we just set the value)
    currentInput.focus();
    await delay(100);

    console.log('⌨️ Dispatching trusted Tab key event to trigger focus change...');

    // Dispatch trusted Tab key event to move focus to target input
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId: tab.id! },
        'Input.dispatchKeyEvent',
        {
          type: 'keyDown',
          key: 'Tab',
          keyCode: 9,
          code: 'Tab',
          windowsVirtualKeyCode: 9
        },
        (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Tab keyDown failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve();
        }
      );
    });

    // Small delay between keyDown and keyUp
    await delay(50);

    // Dispatch keyUp event
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId: tab.id! },
        'Input.dispatchKeyEvent',
        {
          type: 'keyUp',
          key: 'Tab',
          keyCode: 9,
          code: 'Tab',
          windowsVirtualKeyCode: 9
        },
        (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Tab keyUp failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve();
        }
      );
    });

    console.log('✅ Trusted Tab event dispatched successfully');

    // Wait for potential timeline update
    await delay(500); // Increased delay to allow timeline to update

    // Detach debugger
    chrome.debugger.detach({ tabId: tab.id! }, () => {
      if (chrome.runtime.lastError) {
        console.warn('⚠️ Error detaching debugger:', chrome.runtime.lastError.message);
      } else {
        console.log('🔧 Debugger detached');
      }
    });

    console.log('🎯 Trusted focus change sequence completed');

  } catch (error) {
    console.error('❌ Trusted events failed:', error);

    // Try to detach debugger in case of error
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab.id) {
        chrome.debugger.detach({ tabId: tab.id }, () => {
          console.log('🔧 Debugger detached after error');
        });
      }
    } catch (detachError) {
      console.warn('⚠️ Failed to detach debugger after error:', detachError);
    }

    throw error;
  }
}

/**
 * Apply a single cut using the Chrome Debugger API approach
 *
 * SIMPLIFIED WORKFLOW (based on user discovery):
 * 1. Position timeline to start time (visible timeline input) + FOCUS EVENT #1
 * 2. Click "New Cut" button → start time is AUTOMATICALLY populated from timeline!
 * 3. Set ONLY end time in cut dialog + FOCUS EVENT #2 to update end bracket
 * 4. Click Cut (checkmark) button to apply
 *
 * KEY INSIGHT: YouTube Studio automatically carries timeline position to start time
 * when "New Cut" is clicked, so we only need to set the end time manually!
 *
 * FOCUS EVENTS REQUIRED:
 * - Focus Event #1: After setting timeline position (updates timeline needle)
 * - Focus Event #2: After setting end time (updates end bracket on timeline)
 */
async function applySingleCut(
  range: TimestampPair,
  cutNumber: number,
  totalCuts: number
): Promise<void> {
  console.log(`Applying cut ${cutNumber}/${totalCuts}: ${range.start}s - ${range.end}s`);

  // Step 1: Click appropriate button based on whether this is the first cut or not
  if (cutNumber === 1) {
    // First cut: Click "Trim & cut" to enter trimming mode
    console.log('Looking for "Trim & cut" button (first cut)...');

    // Find the "Trim & cut" button by text content
    const trimAndCutButtons = document.querySelectorAll('a.style-scope.ytve-entrypoint-options-panel');
    let trimAndCutButton: Element | null = null;

    for (const button of trimAndCutButtons) {
      if (button.textContent?.trim() === 'Trim & cut') {
        trimAndCutButton = button;
        break;
      }
    }

    if (!trimAndCutButton) {
      throw new Error('Trim & cut button not found for first cut');
    }
    console.log('Clicking "Trim & cut" button...');
    (trimAndCutButton as HTMLElement).click();
    await actionDelay('click');

    // Debug: Check if interface changed after clicking Trim & cut
    console.log('🔍 Checking interface after Trim & cut click...');
    await shortDelay(); // Give interface time to update

    const trimButtons = document.querySelectorAll('ytcp-button.style-scope.ytve-trim-options-panel');
    console.log(`Found ${trimButtons.length} trim option buttons after click`);

    trimButtons.forEach((btn, i) => {
      console.log(`Trim button ${i+1}:`, {
        textContent: btn.textContent?.trim(),
        className: btn.className,
        id: btn.id,
        visible: (btn as HTMLElement).offsetParent !== null
      });
    });
  }

  // 🚀 SIMPLIFIED: Position timeline to start time (simple approach from working implementation)
  console.log('🎯 Positioning timeline to start time...');

  // Find the visible timeline timecode input (always available)
  const timelineInput = findElement(EDITOR_PAGE_SELECTORS.timelineTimecode);
  if (!timelineInput) {
    throw new Error('Could not find visible timeline timecode input');
  }

  // Set the start time in the visible timeline input
  const timelineStartFormatted = secondsToTimestamp(range.start);
  console.log(`Setting timeline to start time: ${timelineStartFormatted}`);
  await setInputValue(timelineInput as HTMLInputElement, timelineStartFormatted, 'TIMELINE INPUT');
  await actionDelay('type');

  // 🚀 FOCUS EVENT #1: Update timeline position after setting start time
  console.log('🎯 Triggering focus event to update timeline position...');
  try {
    await requestTrustedTabEvent();
    console.log('✅ Timeline position updated successfully');
  } catch (error) {
    console.warn('⚠️ Timeline position update failed, continuing:', error);
  }

  // Step 2: Click "New Cut" button (now that timeline is properly positioned)
  console.log('Looking for "New Cut" button...');
  console.log('New Cut button selector:', EDITOR_PAGE_SELECTORS.newCutButton);

  try {
    const newCutButton = await waitForElement(EDITOR_PAGE_SELECTORS.newCutButton, 10000); // Increased timeout
    if (!newCutButton) {
      throw new Error('New Cut button not found');
    }
    console.log('✅ New Cut button found successfully');
  } catch (error) {
    console.error('❌ Failed to find New Cut button:', error);

    // Debug: Check what buttons are actually available
    const allButtons = document.querySelectorAll('ytcp-button, button, [role="button"]');
    console.log(`Found ${allButtons.length} total buttons on page`);

    // Log first 10 buttons for debugging
    Array.from(allButtons).slice(0, 10).forEach((btn, i) => {
      console.log(`Button ${i+1}:`, {
        tagName: btn.tagName,
        textContent: btn.textContent?.trim(),
        className: btn.className,
        id: btn.id,
        ariaLabel: btn.getAttribute('aria-label')
      });
    });

    throw error;
  }

  const newCutButton = await waitForElement(EDITOR_PAGE_SELECTORS.newCutButton, 10000);

  // Check if button is disabled/greyed out
  const isDisabled = (newCutButton as HTMLElement).hasAttribute('disabled') ||
                    (newCutButton as HTMLElement).getAttribute('aria-disabled') === 'true' ||
                    (newCutButton as HTMLElement).classList.contains('disabled');

  console.log('New Cut button state:', {
    disabled: isDisabled,
    ariaDisabled: (newCutButton as HTMLElement).getAttribute('aria-disabled'),
    classList: (newCutButton as HTMLElement).className,
    textContent: (newCutButton as HTMLElement).textContent?.trim()
  });

  // Since we already positioned the timeline to start time, New Cut should be enabled
  // If it's still disabled, that means we're in an already cut region
  if (isDisabled) {
    console.log('⚠️ New Cut button still disabled after positioning - this region may already be cut');
    // We could try alternative positions, but for now let's proceed and see what happens
  }

  console.log('Clicking "New Cut" button...');
  (newCutButton as HTMLElement).click();
  await actionDelay('click');

  // 🚀 CRITICAL FIX: For first cut, YouTube auto-populates BOTH start AND end times
  // For subsequent cuts, YouTube only auto-populates start time
  // We need to wait longer for first cut's auto-population to complete
  if (cutNumber === 1) {
    console.log('⏳ First cut: Waiting longer for YouTube to auto-populate BOTH start AND end times...');
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2 seconds for first cut
  } else {
    console.log('⏳ Subsequent cut: Waiting for YouTube to auto-populate start time only...');
    await new Promise(resolve => setTimeout(resolve, 500)); // 0.5 seconds for subsequent cuts
  }

  // 🚀 NEW: Additional validation wait to ensure auto-population is complete
  console.log('🔍 Waiting for auto-population to stabilize...');
  await new Promise(resolve => setTimeout(resolve, 1000)); // Extra 1 second buffer

  // 🚀 VALIDATION: Check what YouTube auto-populated before we proceed
  console.log('🔍 Checking YouTube auto-population status...');
  const preCheckInputs = document.querySelectorAll(EDITOR_PAGE_SELECTORS.timestampInputs);
  const preCheckVisible = Array.from(preCheckInputs).filter(input => (input as HTMLElement).offsetParent !== null);
  preCheckVisible.forEach((input, i) => {
    const htmlInput = input as HTMLInputElement;
    const parentClass = htmlInput.parentElement?.className || '';
    console.log(`🔍 Pre-check Input ${i}: value="${htmlInput.value}", parent="${parentClass}"`);
  });

  // Step 3: Wait for timestamp inputs to appear and set start/end times
  console.log('Waiting for timestamp inputs...');
  await waitForElement(EDITOR_PAGE_SELECTORS.timestampInputs, 5000);

  // Wait specifically for 2 visible timestamp inputs (cut dialog fully loaded)
  console.log('Waiting for cut dialog to fully load (2 visible inputs)...');
  await waitForCondition(() => {
    const allInputs = document.querySelectorAll(EDITOR_PAGE_SELECTORS.timestampInputs);
    const visibleInputs = Array.from(allInputs).filter(input =>
      (input as HTMLElement).offsetParent !== null
    );
    return visibleInputs.length >= 2;
  }, 10000, 200); // Check every 200ms for up to 10 seconds

  console.log('Cut dialog should be fully loaded now...');

  // Get all timestamp inputs and filter for visible ones only
  const allTimestampInputs = document.querySelectorAll(EDITOR_PAGE_SELECTORS.timestampInputs);
  const visibleTimestampInputs = Array.from(allTimestampInputs).filter(input =>
    (input as HTMLElement).offsetParent !== null
  );

  console.log(`Found ${allTimestampInputs.length} total timestamp inputs, ${visibleTimestampInputs.length} visible`);

  // Debug: Log details about each input
  visibleTimestampInputs.forEach((input, i) => {
    const htmlInput = input as HTMLInputElement;
    console.log(`Visible input ${i+1}:`, {
      value: htmlInput.value,
      placeholder: htmlInput.placeholder,
      ariaLabel: htmlInput.getAttribute('aria-label'),
      parentElement: htmlInput.parentElement?.tagName,
      parentClass: htmlInput.parentElement?.className
    });
  });

  // 🔍 DEBUG: Log all found input elements
  console.log(`🔍 DEBUG: Found ${visibleTimestampInputs.length} visible timestamp inputs:`);
  visibleTimestampInputs.forEach((input, index) => {
    const element = input as HTMLInputElement;
    console.log(`  Input ${index + 1}: value="${element.value}", placeholder="${element.placeholder}", id="${element.id}", class="${element.className}"`);
  });

  // 🚀 CRITICAL FIX: Better input field selection logic
  // When in cut dialog mode, we should have 3 inputs: timeline + 2 cut dialog inputs
  // We need to identify the correct 2 cut dialog inputs (not the timeline input)
  let cutTimestampInputs: Element[] = [];

  if (visibleTimestampInputs.length === 3) {
    // Most common case: timeline input + 2 cut dialog inputs
    // Based on logs: Input 0 & 1 are dialog inputs, Input 2 is timeline input
    // We want the FIRST 2 inputs (dialog inputs), not the last 2
    cutTimestampInputs = visibleTimestampInputs.slice(0, 2); // Take the first 2 inputs
    console.log('🔍 DEBUG: Using first 2 inputs as cut dialog inputs (3 total found)');
  } else if (visibleTimestampInputs.length === 2) {
    // Fallback: only 2 inputs visible, assume both are cut dialog inputs
    cutTimestampInputs = visibleTimestampInputs;
    console.log('🔍 DEBUG: Using both inputs as cut dialog inputs (2 total found)');
  } else {
    // Try to filter out timeline input by checking parent elements or position
    cutTimestampInputs = visibleTimestampInputs.filter((input, index) => {
      const element = input as HTMLInputElement;
      const parentClass = element.parentElement?.className || '';
      // Timeline input often has different parent classes
      const isTimelineInput = parentClass.includes('timeline') ||
                             parentClass.includes('scrubber') ||
                             index === 0; // First input is often timeline
      return !isTimelineInput;
    }).slice(0, 2);
    console.log(`🔍 DEBUG: Filtered inputs to exclude timeline (${visibleTimestampInputs.length} total, ${cutTimestampInputs.length} selected)`);
  }

  console.log(`🔍 DEBUG: Selected ${cutTimestampInputs.length} cut dialog inputs:`);
  cutTimestampInputs.forEach((input, index) => {
    const element = input as HTMLInputElement;
    console.log(`  Cut Input ${index + 1}: value="${element.value}", placeholder="${element.placeholder}", parent="${element.parentElement?.className}"`);
  });

  if (cutTimestampInputs.length < 2) {
    console.error('❌ Not enough cut dialog timestamp inputs found');
    console.error('Available visible inputs:', visibleTimestampInputs.length);
    console.error('All visible inputs:', visibleTimestampInputs.map((input, i) => {
      const el = input as HTMLInputElement;
      return `${i + 1}: value="${el.value}", parent="${el.parentElement?.className}"`;
    }));
    throw new Error(`Expected at least 2 cut dialog timestamp inputs, found ${cutTimestampInputs.length}`);
  }

  // 🚀 VALIDATION: Ensure we're not using the same input twice
  if (cutTimestampInputs[0] === cutTimestampInputs[1]) {
    console.error('❌ CRITICAL ERROR: Same input element selected for both start and end times!');
    console.error('This will cause the start time to be overwritten by the end time.');
    throw new Error('Input field selection error: same element selected twice');
  }

  console.log('✅ Input field validation passed - using 2 different input elements');

  // 🚀 CORRECTED SEQUENCE: Set start time, Tab to end time, set end time, Tab to checkmark, Enter to apply
  const startTimeFormatted = secondsToTimestamp(range.start);
  const endTimeFormatted = secondsToTimestamp(range.end);

  console.log(`🔍 DEBUG: Range values - start: ${range.start}s (${startTimeFormatted}), end: ${range.end}s (${endTimeFormatted})`);

  // 🚀 CRITICAL DEBUG: Log the exact values being applied
  console.log(`🎯 APPLYING CUT ${cutNumber}:`);
  console.log(`   Start: ${range.start} seconds → ${startTimeFormatted}`);
  console.log(`   End: ${range.end} seconds → ${endTimeFormatted}`);
  console.log(`   Duration: ${range.end - range.start} seconds`);

  // 🚀 MAJOR SIMPLIFICATION: Start time is automatically populated from timeline position!
  // We only need to set the END time in the cut dialog
  console.log('✅ Start time automatically populated from timeline position');
  console.log(`   Expected start time: ${startTimeFormatted}`);

  // Verify the start time was populated correctly (optional debug)
  const startInput = cutTimestampInputs[0] as HTMLInputElement;
  console.log(`✅ Start time auto-populated: "${startInput.value}" (expected: ${startTimeFormatted})`);

  // Note: Auto-population delay is now handled immediately after clicking "New Cut" above

  // 🚀 ENHANCED DEBUG: Check end input before and after setting
  const endInput = cutTimestampInputs[1] as HTMLInputElement;
  console.log(`🔍 End input BEFORE setting: value="${endInput.value}", placeholder="${endInput.placeholder}"`);

  // 🚀 CRITICAL DEBUG: Check if YouTube auto-populated the end time
  if (cutNumber === 1 && endInput.value && endInput.value !== endTimeFormatted) {
    console.warn(`⚠️ YouTube auto-populated end time for first cut: "${endInput.value}"`);
    console.warn(`   We need to override this with our target: "${endTimeFormatted}"`);
  } else if (cutNumber > 1 && endInput.value) {
    console.warn(`⚠️ Unexpected: YouTube auto-populated end time for subsequent cut: "${endInput.value}"`);
  }

  console.log(`🕐 Page: Setting end time to ${endTimeFormatted} (AFTER auto-population delay)`);

  // 🚀 RACE CONDITION FIX: Clear the field first to prevent conflicts
  endInput.value = '';
  endInput.dispatchEvent(new Event('input', { bubbles: true }));
  await new Promise(resolve => setTimeout(resolve, 200)); // Increased delay after clearing

  await setInputValue(endInput, endTimeFormatted, 'CUT END TIME');
  await actionDelay('type');

  console.log(`🔍 End input AFTER setting: value="${endInput.value}" (expected: "${endTimeFormatted}")`);

  // 🚀 RACE CONDITION FIX: Verify and retry if YouTube overwrote our value
  let retryCount = 0;
  const maxRetries = 5; // Increased retries
  while (endInput.value !== endTimeFormatted && retryCount < maxRetries) {
    retryCount++;
    console.warn(`⚠️ End time was overwritten! Retry ${retryCount}/${maxRetries}`);
    console.warn(`   Expected: "${endTimeFormatted}", Got: "${endInput.value}"`);

    // Clear and wait longer before retry
    endInput.value = '';
    endInput.dispatchEvent(new Event('input', { bubbles: true }));
    await new Promise(resolve => setTimeout(resolve, 300 + (retryCount * 200))); // Increasing delay

    await setInputValue(endInput, endTimeFormatted, `CUT END TIME (RETRY ${retryCount})`);
    await actionDelay('type');

    console.log(`🔍 End input after retry ${retryCount}: value="${endInput.value}"`);
  }

  if (endInput.value !== endTimeFormatted) {
    console.error(`❌ CRITICAL: End time still incorrect after ${maxRetries} retries!`);
    console.error(`   Expected: "${endTimeFormatted}"`);
    console.error(`   Actual: "${endInput.value}"`);
    console.error(`   This will cause wrong cut times in YouTube Studio!`);

    // 🚀 FINAL ATTEMPT: Force the value and trigger all possible events
    console.log(`🔧 Making final attempt to force correct end time...`);
    endInput.focus();
    endInput.select();
    endInput.value = endTimeFormatted;
    endInput.dispatchEvent(new Event('input', { bubbles: true }));
    endInput.dispatchEvent(new Event('change', { bubbles: true }));
    endInput.blur();

    await new Promise(resolve => setTimeout(resolve, 500));
    console.log(`🔍 End input after final attempt: value="${endInput.value}"`);
  } else {
    console.log(`✅ End time successfully set: "${endInput.value}"`);
  }

  // 🚀 FOCUS EVENT #2: Update end bracket on timeline after setting end time
  console.log('🎯 Triggering focus event to update end bracket on timeline...');
  try {
    await requestTrustedTabEvent();
    console.log('✅ End bracket updated successfully');
  } catch (error) {
    console.warn('⚠️ End bracket update failed, continuing:', error);
  }

  // 🚀 SIMPLIFIED: Use reliable programmatic click method for Cut button (checkmark)
  console.log('🎯 Looking for Cut button (checkmark) to apply cut...');

  const cutButton = Array.from(document.querySelectorAll('ytcp-icon-button')).find(btn =>
    (btn as HTMLElement).offsetParent !== null && btn.getAttribute('aria-label') === 'Cut'
  );

  if (!cutButton) {
    throw new Error('Cut button (checkmark) not found');
  }

  console.log('✅ Cut button found, clicking to apply cut...');
  (cutButton as HTMLElement).click();
  await actionDelay('click');

  // 🚀 NEW: Short delay after applying cut to let timeline update with the saved cut
  console.log('⏱️ Waiting for timeline to update with saved cut...');
  await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay for timeline update

  // 🚀 VERIFICATION: Check if the cut was applied with correct timestamps
  console.log('🔍 Verifying cut was applied with correct timestamps...');
  await new Promise(resolve => setTimeout(resolve, 500)); // Wait for UI to update

  try {
    // Look for the cut in the timeline or cut list
    const cutElements = document.querySelectorAll('[class*="cut"], [class*="trim"]');
    console.log(`🔍 Found ${cutElements.length} cut elements after applying cut ${cutNumber}`);

    // Log the expected vs actual for debugging
    console.log(`📊 Cut ${cutNumber} verification:`);
    console.log(`   Expected: ${startTimeFormatted} → ${endTimeFormatted}`);
    console.log(`   Range: ${range.start}s → ${range.end}s`);

    // Check if we can find timestamp displays in the UI
    const timestampElements = document.querySelectorAll('input[role="timer"], [class*="timestamp"]');
    for (let i = 0; i < Math.min(timestampElements.length, 5); i++) {
      const elem = timestampElements[i] as HTMLInputElement;
      if (elem.value) {
        console.log(`   UI timestamp ${i}: "${elem.value}"`);
      }
    }

  } catch (verificationError) {
    console.warn('⚠️ Cut verification failed (non-critical):', verificationError);
  }

  // Step 5: Wait for return to main trim mode (New Cut button should be visible again)
  console.log('Waiting for return to main trim mode...');
  await waitForElement(EDITOR_PAGE_SELECTORS.newCutButton, 5000);

  // 🚀 CRITICAL FIX: Additional delay after cut completion to ensure interface is fully ready
  console.log('⏱️ Ensuring interface is fully ready after cut completion...');
  await new Promise(resolve => setTimeout(resolve, 1500)); // 1.5 second delay for interface stabilization

  console.log(`✅ Cut ${cutNumber} applied successfully with verification`);
}

/**
 * Navigate timeline to specific timestamp
 */
async function navigateToTimestamp(seconds: number): Promise<void> {
  const timelineInput = findElement(EDITOR_PAGE_SELECTORS.timelineTimecode);
  if (!timelineInput) {
    throw new Error('Could not find timeline timecode input');
  }

  const timestampText = secondsToTimestamp(seconds);
  console.log(`Navigating to timestamp: ${timestampText}`);

  // Use the enhanced setInputValue function
  await setInputValue(timelineInput as HTMLInputElement, timestampText, 'TIMELINE NAVIGATION');

  // Press Enter to navigate
  timelineInput.dispatchEvent(new KeyboardEvent('keydown', {
    key: 'Enter',
    bubbles: true
  }));
  await actionDelay('navigate');
}



/**
 * Handle confirmation dialog that appears after clicking Save
 * PROVEN WORKING METHOD (100% Tested) - From batch-mode-implementation-plan.md
 */
async function handleConfirmationDialog(): Promise<boolean> {
  console.log('💾 Handling confirmation dialog...');

  // Find and click confirmation button (PROVEN WORKING)
  const confirmButton = Array.from(document.querySelectorAll('button, ytcp-button')).find(btn =>
    btn.textContent && btn.textContent.trim().includes('Confirm changes')
  );

  if (!confirmButton) {
    console.log('❌ Confirmation button not found');
    return false;
  }

  console.log('✅ Found confirmation button - clicking...');
  (confirmButton as HTMLElement).click();

  // Wait longer for YouTube Studio to process
  await new Promise(resolve => setTimeout(resolve, 4000));

  // Validate success
  const dialogGone = !document.querySelector('[role="dialog"]');
  const processingStarted = document.body.textContent?.includes('Video editing is in progress') || false;
  const onEditorPage = window.location.href.includes('/editor');

  const success = dialogGone && onEditorPage && processingStarted;

  console.log(success ? '🎉 Confirmation successful!' : '⚠️ Validation needs more time');
  return success;
}

/**
 * Automatically click the Save button in turbo mode
 */
async function autoSaveChanges(): Promise<void> {
  console.log('🚀 Auto-clicking Save button in turbo mode...');

  try {
    const saveButton = await waitForElement(EDITOR_PAGE_SELECTORS.saveButton, 10000);

    // Scroll save button into view
    saveButton.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    });
    await shortDelay();

    console.log('Clicking Save button automatically...');
    (saveButton as HTMLElement).click();
    await actionDelay('click');

    console.log('✅ Save button clicked successfully! Waiting for confirmation dialog...');

    // In turbo mode, also handle the confirmation dialog automatically
    await handleConfirmationDialog();

  } catch (error) {
    console.error('Failed to auto-click save button:', error);
    // Fallback to manual save highlighting
    console.log('Falling back to manual save highlighting...');
    await highlightSaveButton();
  }
}

/**
 * Highlight the native Save button for manual user action
 */
async function highlightSaveButton(): Promise<void> {
  console.log('Highlighting Save button for manual user action...');

  try {
    const saveButton = await waitForElement(EDITOR_PAGE_SELECTORS.saveButton, 10000);

    // Scroll save button into view
    saveButton.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    });
    await mediumDelay();

    // Add pulsing animation
    addPulseAnimation(saveButton as HTMLElement);

    // Show instruction overlay
    showSaveInstruction(saveButton as HTMLElement);

    console.log('Save button highlighted - waiting for manual user action');

  } catch (error) {
    console.error('Failed to highlight save button:', error);
    // Fallback: show generic instruction
    showGenericSaveInstruction();
  }
}

/**
 * Add CSS pulse animation to save button
 */
function addPulseAnimation(button: HTMLElement): void {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes claimcutter-pulse {
      0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
      100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
    }
    .claimcutter-pulse {
      animation: claimcutter-pulse 2s infinite;
      border: 2px solid #3b82f6 !important;
    }
  `;
  document.head.appendChild(style);

  button.classList.add('claimcutter-pulse');
}

/**
 * Show visual feedback about timeline positioning
 */
function showTimelinePositionFeedback(timestamp: number, totalCuts: number): void {
  const timeFormatted = secondsToTimestamp(timestamp);

  const feedback = document.createElement('div');
  feedback.id = 'claimcutter-timeline-feedback';
  feedback.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      font-family: 'YouTube Sans', Roboto, Arial, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 10000;
      animation: slideInRight 0.3s ease-out;
      border: 1px solid rgba(255, 255, 255, 0.2);
    ">
      <div style="display: flex; align-items: center; gap: 8px;">
        <span style="font-size: 16px;">🎯</span>
        <div>
          <div style="font-weight: 600;">Timeline Positioned</div>
          <div style="font-size: 12px; opacity: 0.9;">
            At ${timeFormatted} (first of ${totalCuts} cut${totalCuts > 1 ? 's' : ''})
          </div>
        </div>
      </div>
    </div>
    <style>
      @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    </style>
  `;

  document.body.appendChild(feedback);

  // Auto-remove after 4 seconds
  setTimeout(() => {
    if (feedback.parentNode) {
      feedback.parentNode.removeChild(feedback);
    }
  }, 4000);
}

/**
 * Show instruction overlay near save button
 */
function showSaveInstruction(button: HTMLElement): void {
  const instruction = document.createElement('div');
  instruction.id = 'claimcutter-save-instruction';
  instruction.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: #1f2937;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      max-width: 300px;
      animation: slideInRight 0.3s ease-out;
    ">
      <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
        <span style="font-size: 18px;">✂️</span>
        <strong>Cuts Applied Successfully!</strong>
      </div>
      <p style="margin: 0; color: #d1d5db; line-height: 1.4;">
        Click the highlighted <strong>Save</strong> button to finalize your changes.
      </p>
    </div>
    <style>
      @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    </style>
  `;

  document.body.appendChild(instruction);

  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (instruction.parentNode) {
      instruction.parentNode.removeChild(instruction);
    }
  }, 10000);
}

/**
 * Show generic save instruction if button highlighting fails
 */
function showGenericSaveInstruction(): void {
  const instruction = document.createElement('div');
  instruction.innerHTML = `
    <div style="
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #1f2937;
      color: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.3);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      text-align: center;
      max-width: 400px;
    ">
      <div style="font-size: 24px; margin-bottom: 12px;">✂️</div>
      <h3 style="margin: 0 0 12px 0; color: white;">Cuts Applied Successfully!</h3>
      <p style="margin: 0; color: #d1d5db; line-height: 1.4;">
        Please manually click the <strong>Save</strong> button in YouTube Studio to finalize your changes.
      </p>
      <button onclick="this.parentElement.parentElement.remove()" style="
        margin-top: 16px;
        background: #3b82f6;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
      ">Got it</button>
    </div>
  `;

  document.body.appendChild(instruction);
}

/**
 * Auto-execute when script is injected
 */
(async () => {
  try {
    console.log('🚀 ClaimCutter apply-cuts script starting...');
    console.log('🔍 Script loaded at:', new Date().toISOString());
    console.log('🔍 Current URL:', window.location.href);
    console.log('🔍 Document ready state:', document.readyState);

    // Wait for page to be fully loaded
    if (document.readyState !== 'complete') {
      console.log('⏳ Waiting for page to load...');
      await new Promise(resolve => {
        window.addEventListener('load', resolve);
      });
      console.log('✅ Page loaded');
    }

    // Additional delay to ensure YouTube Studio is ready
    console.log('⏳ Waiting for YouTube Studio to be ready...');
    await mediumDelay();
    console.log('✅ YouTube Studio ready');

    // Start cut application
    console.log(getVersionString('Apply-Cuts Main Execution'));
    console.log('🎯 Starting cut application...');
    const result = await applyCuts();

    console.log('✅ Cut application completed successfully:', result);
    console.log('📊 Final result summary:', {
      applied: result.applied,
      failed: result.failed,
      errorCount: result.errors.length
    });

    // Send results back to background script
    console.log('📨 Sending CUTS_APPLIED message to background script...');
    try {
      await new Promise<void>((resolve, reject) => {
        chrome.runtime.sendMessage({
          action: 'CUTS_APPLIED',
          data: result,
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('❌ Failed to send CUTS_APPLIED message:', chrome.runtime.lastError.message);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          console.log('✅ CUTS_APPLIED message sent successfully:', response);
          resolve();
        });
      });
    } catch (messageError) {
      console.error('❌ Error sending success message:', messageError);
      // Don't throw here - cuts were applied successfully
    }

    console.log('🎉 Apply-cuts script completed successfully!');

    // Send final success message to popup
    try {
      await chrome.runtime.sendMessage({
        action: 'OPERATION_COMPLETE',
        data: {
          success: true,
          message: `Successfully applied ${result.applied} cuts!`,
          stats: result
        }
      });
    } catch (error) {
      console.log('Note: Could not send completion message to popup (popup may be closed)');
    }

  } catch (error) {
    console.error('❌ Apply-cuts script failed:', error);
    console.error('❌ Error details:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    try {
      await new Promise<void>((resolve, reject) => {
        chrome.runtime.sendMessage({
          action: 'ERROR',
          error: 'Failed to apply cuts',
          details: error instanceof Error ? error.message : String(error),
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('❌ Failed to send ERROR message:', chrome.runtime.lastError.message);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          console.log('✅ ERROR message sent successfully:', response);
          resolve();
        });
      });
    } catch (messageError) {
      console.error('❌ Error sending error message:', messageError);
    }
  }
})();

console.log('🚀 ClaimCutter apply-cuts script loaded at:', new Date().toISOString());
console.log('🔍 Apply-cuts script file loaded successfully!');
