/**
 * Channel Content Detector Content Script
 *
 * Injected into YouTube Studio channel content pages to discover copyright videos
 * and handle auto-activation of copyright filter.
 */

console.log('🔍 Channel content detector content script loaded');

/**
 * Validate if current page is a YouTube Studio channel content page
 */
function validateChannelContentPage(): boolean {
  const isChannelContent = window.location.href.includes('/channel/') &&
                          window.location.href.includes('/videos');
  const hasVideoRows = document.querySelectorAll('ytcp-video-row').length > 0;

  console.log('🔍 Channel content page validation:', {
    isChannelContent,
    hasVideoRows,
    url: window.location.href
  });

  return isChannelContent && hasVideoRows;
}

/**
 * Validate current copyright filter state
 */
function validateCopyrightFilter(): any {
  // Check URL for filter parameter
  const hasUrlFilter = window.location.href.includes('HAS_COPYRIGHT_CLAIM');

  // Check filter chip state
  const filterChip = document.querySelector('ytcp-chip[aria-label="Copyright"]');
  const hasChipFilter = filterChip && filterChip.getAttribute('aria-pressed') === 'true';

  // Check selection summary for video count
  const selectionSummary = document.querySelector('.selection-summary');
  const videoCountMatch = selectionSummary?.textContent?.match(/(\d+)\s+selected/);
  const videoCount = videoCountMatch ? parseInt(videoCountMatch[1]) : 0;

  const isActive = hasUrlFilter || hasChipFilter;
  const method = hasUrlFilter ? 'URL' : hasChipFilter ? 'Chip' : 'None';

  console.log('🔍 Copyright filter validation:', {
    isActive,
    method,
    videoCount,
    hasUrlFilter,
    hasChipFilter
  });

  return {
    isActive,
    method,
    videoCount
  };
}

/**
 * Auto-activate copyright filter using proven methods (100% tested)
 */
async function autoActivateCopyrightFilter(settings: any): Promise<boolean> {
  console.log('🔧 Checking copyright filter activation (user-initiated)...');

  // Check if auto-activation is disabled in settings
  if (!settings.autoCopyrightFilter) {
    console.log('⚙️ Auto copyright filter disabled in settings');
    return validateCopyrightFilter().isActive;
  }

  console.log('🔧 Auto-activating copyright filter (user requested discovery)...');

  // Method 1: Click copyright chip if visible and not already active
  const copyrightChip = document.querySelector('ytcp-chip[aria-label="Copyright"]');
  if (copyrightChip) {
    const isActive = copyrightChip.getAttribute('aria-pressed') === 'true';
    if (!isActive) {
      console.log('📌 Clicking copyright filter chip');
      (copyrightChip as HTMLElement).click();
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for filter to apply
      return true;
    } else {
      console.log('✅ Copyright filter already active');
      return true;
    }
  }

  // Method 2: URL manipulation (proven fallback)
  const currentUrl = window.location.href;
  const hasFilter = currentUrl.includes('HAS_COPYRIGHT_CLAIM');

  if (!hasFilter) {
    console.log('🔗 Using URL manipulation method');
    const copyrightFilterUrl = currentUrl.replace(
      'filter=%5B%5D',
      'filter=%5B%7B%22name%22%3A%22HAS_COPYRIGHT_CLAIM%22%2C%22value%22%3A%22VIDEO_HAS_COPYRIGHT_CLAIM%22%7D%5D'
    );

    if (copyrightFilterUrl !== currentUrl) {
      console.log('🔄 Redirecting to activate copyright filter...');
      window.location.href = copyrightFilterUrl;
      // Return false to indicate page will redirect - discovery should be retried after reload
      return false;
    }
  }

  console.log('✅ Copyright filter confirmed active');
  return true;
}

/**
 * Wait for video content to fully load after page navigation/filter changes
 */
async function waitForVideoContent(): Promise<void> {
  const maxWaitTime = 10000; // 10 seconds max
  const checkInterval = 500; // Check every 500ms
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitTime) {
    const videoContainers = document.querySelectorAll('ytcp-video-row');

    // Check if we have video containers and they appear to be fully loaded
    if (videoContainers.length > 0) {
      // Additional check: ensure the containers have actual content
      const hasContent = Array.from(videoContainers).some(container => {
        const titleLink = container.querySelector('a[href*="/editor/"]') ||
                         container.querySelector('a[href*="/video/"]');
        return titleLink && titleLink.getAttribute('href');
      });

      if (hasContent) {
        console.log(`✅ Video content loaded: ${videoContainers.length} containers found`);
        return;
      }
    }

    console.log(`⏳ Waiting for content... (${videoContainers.length} containers found)`);
    await new Promise(resolve => setTimeout(resolve, checkInterval));
  }

  console.log('⚠️ Timeout waiting for video content - proceeding with current state');
}

/**
 * Main discovery function - called when user clicks "Discover Videos" button
 */
async function discoverCopyrightVideos(settings: any): Promise<any> {
  console.log('🔍 Starting video discovery (user-initiated)...');

  // Step 1: Auto-activate copyright filter if setting is enabled
  if (settings.autoCopyrightFilter) {
    console.log('🔧 Auto-activating copyright filter (user setting enabled)...');
    const filterActivated = await autoActivateCopyrightFilter(settings);

    if (filterActivated === false) {
      console.log('🔄 Page redirected to activate filter - discovery will continue after reload');
      // Return early - the page is redirecting and discovery will be retried
      return {
        videos: [],
        filterActive: false,
        filterMethod: 'redirect_pending',
        totalFound: 0,
        discoveredAt: Date.now(),
        redirected: true,
        message: 'Page redirected to activate copyright filter - please try discovery again'
      };
    }
  } else {
    console.log('⚙️ Auto copyright filter disabled in settings');
  }

  // Step 2: Validate current filter state
  const filterValidation = validateCopyrightFilter();

  // Step 3: Check if copyright filter is active - CRITICAL for accurate discovery
  if (!filterValidation.isActive) {
    console.log('⚠️ Copyright filter is NOT active - cannot guarantee accurate results');
    console.log('💡 Please activate the copyright filter manually or enable auto-activation in settings');

    return {
      videos: [],
      filterActive: false,
      filterMethod: filterValidation.method,
      totalFound: 0,
      discoveredAt: Date.now(),
      error: 'Copyright filter not active - results would include non-copyright videos'
    };
  }

  console.log('✅ Copyright filter confirmed active - proceeding with filter-based discovery');
  console.log(`📊 Expected video count from filter: ${filterValidation.videoCount}`);

  // Step 4: Wait for page content to load, then extract videos
  console.log('⏳ Waiting for video content to load...');
  await waitForVideoContent();

  // Extract videos using filter-based logic (100% success rate when filter is active)
  // KEY INSIGHT: When copyright filter is active, ALL visible videos are copyright videos by definition
  const videoContainers = document.querySelectorAll('ytcp-video-row');
  const videos: any[] = [];

  console.log(`📊 Found ${videoContainers.length} video containers (filter-based)`);

  videoContainers.forEach((container, index) => {
    const titleLink = container.querySelector('a[href*="/editor/"]') ||
                     container.querySelector('a[href*="/video/"]');

    if (!titleLink) {
      console.log(`Row ${index + 1}: No video link found`);
      return;
    }

    const href = titleLink.getAttribute('href');
    const videoId = href?.match(/\/(?:editor|video)\/([^\/\?]+)/)?.[1];

    if (!videoId) {
      console.log(`Row ${index + 1}: Could not extract video ID from ${href}`);
      return;
    }

    const title = titleLink.textContent?.trim() ||
                 container.querySelector('[class*="title"]')?.textContent?.trim() ||
                 `Video ${index + 1}`;

    const video = {
      videoId,
      title,
      copyrightUrl: `https://studio.youtube.com/video/${videoId}/copyright`,
      editorUrl: `https://studio.youtube.com/video/${videoId}/editor`,
      status: 'pending',
      index: videos.length + 1
    };

    videos.push(video);
    console.log(`✅ ${video.index}. ${video.title} (ID: ${videoId})`);
  });

  // Validate discovery results against expected count
  const expectedCount = filterValidation.videoCount;
  if (expectedCount > 0 && videos.length !== expectedCount) {
    console.log(`⚠️ Discovery count mismatch: found ${videos.length}, expected ${expectedCount}`);
    console.log('💡 This may indicate the page is still loading or filter state changed');
  }

  console.log(`\n🎯 Filter-based discovery complete: ${videos.length} copyright videos found`);

  return {
    videos,
    filterActive: filterValidation.isActive,
    filterMethod: filterValidation.method,
    totalFound: videos.length,
    discoveredAt: Date.now()
  };
}

// Listen for discovery start message from background script
chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
  console.log('Channel content detector received message:', message);

  if (message.action === 'START_DISCOVERY') {
    try {
      console.log('🎯 Starting video discovery...');

      const discoveryResult = await discoverCopyrightVideos(message.settings);

      console.log(`✅ Discovery complete: ${discoveryResult.videos.length} videos found`);

      // Send discovery results back to background script
      chrome.runtime.sendMessage({
        action: 'BATCH_DISCOVERY_COMPLETE',
        data: discoveryResult
      });

      sendResponse({ success: true });

    } catch (error) {
      console.error('❌ Video discovery failed:', error);

      chrome.runtime.sendMessage({
        action: 'ERROR_REPORT',
        error: {
          code: 'DISCOVERY_ERROR',
          message: 'Video discovery failed',
          details: error instanceof Error ? error.message : String(error),
          timestamp: Date.now(),
          recoverable: true,
          userMessage: 'Failed to discover videos. Please try again.'
        }
      });

      sendResponse({ error: error instanceof Error ? error.message : String(error) });
    }

    return true; // Keep message channel open for async response
  }

  return false; // Don't handle other messages
});
