/**
 * Enhanced Background Service Worker for ClaimCutter Chrome Extension
 * Handles orchestration, error propagation, and comprehensive state management
 */

// Inline version constant (service workers can't use imports in some contexts)
const CLAIMCUTTER_VERSION = '2.1.03';
function getVersionString(component: string): string {
  return `🚀 ClaimCutter ${component} v${CLAIMCUTTER_VERSION} - Enhanced Versioning`;
}

console.log(getVersionString('Background Script'));

// Inline error handling to avoid import issues
interface ExtensionError {
  code: string;
  message: string;
  details?: string;
  timestamp: number;
  context?: string;
  recoverable: boolean;
  userMessage: string;
}

function createError(
  code: string,
  message: string,
  details?: string,
  context?: string
): ExtensionError {
  return {
    code,
    message,
    details,
    timestamp: Date.now(),
    context,
    recoverable: code !== 'EXTENSION_ERROR',
    userMessage: message,
  };
}

function reportError(error: ExtensionError): void {
  console.error('[<PERSON><PERSON><PERSON><PERSON>utter] Error:', error);
  try {
    chrome.runtime.sendMessage({
      action: 'ERROR_REPORT',
      error,
    });
  } catch (e) {
    console.error('[Claim<PERSON>utter] Failed to report error:', e);
  }
}

// Enhanced message types
interface StartOperationMessage {
  action: 'START_OPERATION';
  dryRun: boolean;
  pageType: 'copyright' | 'editor';
  url: string;
  tabId?: number; // Optional - provided by popup, not by content scripts
  settings?: {
    mergeBuffer: number;
    turboMode: boolean;
    autoSave: boolean;
    defaultSettings: boolean;
  };
}

interface ValidatePageMessage {
  action: 'VALIDATE_PAGE';
  url: string;
  tabId: number;
  pageType: string;
}

interface ProgressUpdateMessage {
  action: 'PROGRESS_UPDATE';
  phase: string;
  percentage: number;
  message: string;
  stats?: any;
}

interface OperationCompleteMessage {
  action: 'OPERATION_COMPLETE';
  data: any;
}

interface ErrorReportMessage {
  action: 'ERROR_REPORT';
  error: ExtensionError;
}

type Message = StartOperationMessage | ValidatePageMessage | ProgressUpdateMessage |
               OperationCompleteMessage | ErrorReportMessage;

// Enhanced state tracking
interface TabInfo {
  id: number;
  url: string;
  isProcessing: boolean;
  pageType: 'copyright' | 'editor' | 'other';
  operationId?: string;
  startTime?: number;
}

interface OperationState {
  id: string;
  tabId: number;
  phase: 'collecting' | 'applying' | 'complete' | 'error';
  dryRun: boolean;
  startTime: number;
  settings?: {
    mergeBuffer: number;
    turboMode: boolean;
    autoSave: boolean;
    defaultSettings: boolean;
  };
  progress: {
    percentage: number;
    message: string;
  };
  stats: {
    claimsFound: number;
    cutsApplied: number;
    timeSaved: string;
  };
  errors: ExtensionError[];
}

const activeTabs = new Map<number, TabInfo>();
const activeOperations = new Map<string, OperationState>();

/**
 * Enhanced message handler with comprehensive error handling
 */
chrome.runtime.onMessage.addListener((message: any, sender, sendResponse) => {
  console.log('Background received message:', message, 'from tab:', sender.tab && sender.tab.id);

  // Wrap all handlers with error handling
  const handleMessage = async () => {
    try {
      switch (message.action) {
        case 'START_OPERATION':
          return await handleStartOperation(message, sender);

        case 'VALIDATE_PAGE':
          return await handleValidatePage(message, sender);

        case 'TIMESTAMPS_COLLECTED':
          return await handleTimestampsCollected(message, sender);

        case 'CUTS_APPLIED':
          return await handleCutsApplied(message, sender);

        case 'PROGRESS_UPDATE':
          return await handleProgressUpdate(message, sender);

        case 'ERROR_REPORT':
          return await handleErrorReport(message, sender);

        case 'ERROR':
          return await handleContentScriptError(message, sender);

        case 'GET_STORAGE':
          return await handleGetStorage(message, sender);

        case 'APPLY_CUTS':
          return await handleApplyCuts(message, sender);

        case 'GET_POPUP_STATE':
          return await handleGetPopupState(message, sender);

        case 'UPDATE_POPUP_STATE':
          return await handleUpdatePopupState(message, sender);

        // NEW: Batch mode message handlers
        case 'DISCOVER_VIDEOS':
          return await handleDiscoverVideos(message, sender);

        case 'BATCH_DISCOVERY_COMPLETE':
          return await handleBatchDiscoveryComplete(message, sender);

        case 'START_BATCH_OPERATION':
          return await handleStartBatchOperation(message, sender);

        case 'BATCH_PROGRESS_UPDATE':
          return await handleBatchProgressUpdate(message, sender);

        case 'BATCH_COMPLETE':
          return await handleBatchComplete(message, sender);

        case 'PAUSE_BATCH_OPERATION':
          return await handlePauseBatchOperation(message, sender);

        case 'CONTINUE_BATCH_OPERATION':
          return await handleContinueBatchOperation(message, sender);

        case 'STOP_BATCH_OPERATION':
          return await handleStopBatchOperation(message, sender);

        case 'CLEAR_BATCH_OPERATIONS':
          return await handleClearBatchOperations(message, sender);

        case 'TRUSTED_TAB_EVENT':
          return await handleTrustedTabEvent(message, sender);

        case 'TRUSTED_ENTER_EVENT':
          return await handleTrustedEnterEvent(message, sender);

        case 'START_DEBUGGER_SESSION':
          return await handleStartDebuggerSession(message, sender);

        case 'END_DEBUGGER_SESSION':
          return await handleEndDebuggerSession(message, sender);

        default:
          throw createError(
            'EXTENSION_ERROR',
            `Unknown message action: ${message.action}`,
            JSON.stringify(message)
          );
      }
    } catch (error) {
      const extensionError = error instanceof Error
        ? createError('EXTENSION_ERROR', error.message, error.stack)
        : error as ExtensionError;

      reportError(extensionError);

      // Send error to popup
      sendToPopup({
        action: 'OPERATION_ERROR',
        error: extensionError,
      });

      return { error: extensionError };
    }
  };

  // Handle async responses
  handleMessage()
    .then(response => {
      console.log(`Background: ${message.action} response:`, response);
      sendResponse(response || { success: true });
    })
    .catch(error => {
      console.error('Message handler failed:', error);
      // Ensure error is serializable
      const serializableError = {
        message: error.message || 'Unknown error',
        code: error.code || 'EXTENSION_ERROR',
        details: error.details || error.stack || 'No additional details',
        timestamp: Date.now(),
        recoverable: true,
        userMessage: error.userMessage || error.message || 'An error occurred'
      };
      sendResponse({ error: serializableError });
    });

  return true; // Keep message channel open for async response
});

/**
 * Enhanced operation start handler
 */
async function handleStartOperation(message: StartOperationMessage, sender: chrome.runtime.MessageSender): Promise<any> {
  // Get tab ID from message (sent by popup) or sender (sent by content script)
  const tabId = message.tabId || (sender.tab && sender.tab.id);
  if (!tabId) {
    throw createError('EXTENSION_ERROR', 'No tab ID provided for operation');
  }

  try {
    // 🚀 CRITICAL FIX: Clear any stale timestamp data before starting new operation
    console.log('🧹 Clearing stale timestamp data before starting new operation...');
    await chrome.storage.session.remove([
      'claimcutter_timestamps',
      'claimcutter_collection_complete',
      'claimcutter_collection_results',
      'claimcutter_errors'
    ]);

    // Create operation state
    const operationId = generateOperationId();
    const operation: OperationState = {
      id: operationId,
      tabId,
      phase: 'collecting',
      dryRun: message.dryRun,
      startTime: Date.now(),
      settings: message.settings,
      progress: { percentage: 0, message: 'Starting operation...' },
      stats: { claimsFound: 0, cutsApplied: 0, timeSaved: '0s' },
      errors: [],
    };

    activeOperations.set(operationId, operation);

    // Update tab info
    activeTabs.set(tabId, {
      id: tabId,
      url: message.url,
      isProcessing: true,
      pageType: message.pageType,
      operationId,
      startTime: Date.now(),
    });

    // Send progress update to popup
    sendToPopup({
      action: 'PROGRESS_UPDATE',
      phase: 'collecting',
      percentage: 10,
      message: message.dryRun ? 'Starting dry run...' : 'Starting operation...',
    });

    // Start appropriate workflow based on page type
    if (message.pageType === 'copyright') {
      await startTimestampCollection(tabId, message.dryRun, operationId, message.settings);
    } else if (message.pageType === 'editor') {
      await startCutApplication(tabId, message.dryRun, operationId);
    } else {
      throw createError('INVALID_PAGE', `Invalid page type: ${message.pageType}`);
    }

    return { operationId };

  } catch (error) {
    // Clean up on error
    const tabInfo = activeTabs.get(tabId);
    if (tabInfo && tabInfo.operationId) {
      activeOperations.delete(tabInfo.operationId);
    }
    activeTabs.delete(tabId);

    throw error;
  }
}

/**
 * Validate page compatibility
 */
async function handleValidatePage(message: ValidatePageMessage, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    const tab = await chrome.tabs.get(message.tabId);

    if (!tab.url) {
      throw createError('PAGE_LOAD_FAILED', 'Cannot access tab URL');
    }

    const isYouTubeStudio = tab.url.includes('studio.youtube.com');
    const isCopyrightPage = tab.url.includes('/copyright');
    const isEditorPage = tab.url.includes('/editor');

    let isValid = false;
    let details = '';

    if (!isYouTubeStudio) {
      details = 'Not a YouTube Studio page';
    } else if (isCopyrightPage) {
      isValid = true;
      details = 'Copyright page detected - ready for claim collection';
    } else if (isEditorPage) {
      isValid = true;
      details = 'Editor page detected - ready for cut application';
    } else {
      details = 'Please navigate to Copyright or Editor tab';
    }

    // Return validation result directly to the popup (don't use sendToPopup)
    return {
      action: 'PAGE_VALIDATED',
      isValid,
      details,
    };

  } catch (error) {
    const extensionError = createError(
      'PAGE_LOAD_FAILED',
      'Failed to validate page',
      error instanceof Error ? error.message : String(error)
    );

    // Return error result directly to the popup
    return {
      action: 'PAGE_VALIDATED',
      isValid: false,
      details: 'Page validation failed',
      error: extensionError,
    };
  }
}

/**
 * Enhanced timestamp collection completion handler
 */
async function handleTimestampsCollected(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = sender.tab && sender.tab.id;
  if (!tabId) {
    throw createError('EXTENSION_ERROR', 'No tab ID provided for timestamp handling');
  }

  try {
    const { timestamps, count, rawCount, totalClaims, errors = [] } = message.data;
    console.log(`Collected ${count} timestamps from ${totalClaims} claims, storing results...`);

    // Check if this is batch mode FIRST - before any regular processing
    if (message.batchMode && message.batchVideoIndex !== undefined) {
      return await handleBatchTimestampsCollected(message, tabId);
    }

    // Regular (non-batch) operation handling
    // Find the operation
    const tabInfo = activeTabs.get(tabId);
    const operation = tabInfo && tabInfo.operationId ? activeOperations.get(tabInfo.operationId) : null;

    if (!operation) {
      throw createError('EXTENSION_ERROR', 'No active operation found for timestamp collection');
    }

    // Update operation state - STOP at collection phase, don't auto-navigate
    operation.phase = 'collecting'; // Keep in collecting phase
    operation.stats.claimsFound = totalClaims || rawCount || count; // Use totalClaims for claims found
    operation.progress = {
      percentage: 100,
      message: count > 0 ? `Collection complete! Found ${count} timestamp(s)` : 'Collection complete - no timestamps found'
    };

    // Store timestamps, errors, and collection results in session storage
    await chrome.storage.session.set({
      claimcutter_timestamps: timestamps,
      claimcutter_dry_run: operation.dryRun,
      claimcutter_errors: errors,
      claimcutter_operation_id: operation.id,
      claimcutter_collection_complete: true,
      claimcutter_collection_results: {
        count,
        rawCount,
        totalClaims,
        timestamps,
        errors,
        completedAt: Date.now()
      }
    });

    // Update popup state for persistence
    await updatePopupState({
      phase: 'collection_complete',
      collectionResults: {
        count,
        rawCount,
        totalClaims,
        timestamps,
        errors,
        completedAt: Date.now()
      },
      stats: operation.stats,
      settings: operation.settings // Include settings for apply-cuts script
    });

    // Send completion update to popup (NOT navigation)
    sendToPopup({
      action: 'COLLECTION_COMPLETE',
      data: {
        count,
        rawCount,
        totalClaims,
        timestamps,
        errors,
        stats: operation.stats
      }
    });

    // Check if turbo mode is enabled for auto-navigation
    if (operation.settings?.turboMode && !operation.dryRun && count > 0) {
      console.log('🚀 Turbo mode enabled - auto-navigating to editor...');

      // Auto-navigate to editor and apply cuts
      setTimeout(async () => {
        try {
          await autoNavigateToEditor(tabId, operation.id);
        } catch (error) {
          console.error('Failed to auto-navigate to editor:', error);
          // Send error to popup
          sendToPopup({
            action: 'OPERATION_ERROR',
            error: createError('TURBO_MODE_ERROR', 'Failed to auto-navigate to editor', error instanceof Error ? error.message : String(error)),
          });
        }
      }, 2000); // 2 second delay to allow UI to update
    } else {
      console.log('✅ Timestamp collection completed. Waiting for user to proceed to editor.');
    }

    return { success: true };

  } catch (error) {
    throw createError(
      'EXTENSION_ERROR',
      'Failed to handle collected timestamps',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Handle cut application completion
 */
async function handleCutsApplied(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = sender.tab && sender.tab.id;
  if (!tabId) {
    throw createError('EXTENSION_ERROR', 'No tab ID provided for cuts applied handling');
  }

  try {
    const { applied, failed, errors = [] } = message.data;

    console.log(`🎯 CUTS_APPLIED received: ${applied} applied, ${failed} failed`);

    // Check if this is part of a batch operation
    const allStorage = await chrome.storage.session.get();
    const activeBatchOp = Object.keys(allStorage).find(key => key.startsWith('batchOperation_'));

    if (activeBatchOp) {
      const batchOp = allStorage[activeBatchOp];
      if (batchOp && !batchOp.completedAt) {
        // This is a batch operation - handle batch completion
        console.log(`✂️ Batch video ${batchOp.currentIndex + 1} cuts applied: ${applied} cuts, ${failed} failed`);

        await completeBatchVideoProcessing(batchOp.id, batchOp.currentIndex, {
          success: failed === 0,
          claimsFound: applied + failed, // Total attempts
          cutsApplied: applied,
          errors: errors
        });

        return { success: true };
      }
    }

    // Regular (non-batch) operation handling
    const tabInfo = activeTabs.get(tabId);
    const operation = tabInfo && tabInfo.operationId ? activeOperations.get(tabInfo.operationId) : null;

    if (!operation) {
      console.warn('No active operation found for cuts applied, but continuing...');
    } else {
      // Update operation state
      operation.phase = 'complete';
      operation.stats.cutsApplied = applied;
      operation.progress = { percentage: 100, message: 'Operation completed successfully!' };
      operation.errors.push(...errors);

      // Calculate time saved (rough estimate)
      const totalDuration = applied * 30; // Assume 30 seconds per cut on average
      operation.stats.timeSaved = formatDuration(totalDuration);
    }

    // Clean up tab state
    activeTabs.delete(tabId);
    if (operation) {
      activeOperations.delete(operation.id);
    }

    // Send completion message to popup
    console.log(`🎉 Sending OPERATION_COMPLETE to popup: ${applied} applied, ${failed} failed`);
    sendToPopup({
      action: 'OPERATION_COMPLETE',
      data: {
        applied,
        failed,
        errors,
        stats: operation && operation.stats || { claimsFound: 0, cutsApplied: applied, timeSaved: '0s' },
      },
    });

    return { success: true };

  } catch (error) {
    throw createError(
      'EXTENSION_ERROR',
      'Failed to handle cuts applied',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Handle progress updates from content scripts
 */
async function handleProgressUpdate(message: ProgressUpdateMessage, sender: chrome.runtime.MessageSender): Promise<any> {
  // Forward progress updates to popup
  sendToPopup({
    action: 'PROGRESS_UPDATE',
    phase: message.phase,
    percentage: message.percentage,
    message: message.message,
    stats: message.stats,
  });

  return { success: true };
}

/**
 * Handle error reports from content scripts
 */
async function handleErrorReport(message: ErrorReportMessage, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = sender.tab && sender.tab.id;

  // Log the error
  reportError(message.error);

  // Update operation state if applicable
  if (tabId) {
    const tabInfo = activeTabs.get(tabId);
    const operation = tabInfo && tabInfo.operationId ? activeOperations.get(tabInfo.operationId) : null;

    if (operation) {
      operation.phase = 'error';
      operation.errors.push(message.error);
      operation.progress = { percentage: 0, message: 'Operation failed' };
    }

    // Clean up on critical errors
    if (!message.error.recoverable) {
      activeTabs.delete(tabId);
      if (operation) {
        activeOperations.delete(operation.id);
      }
    }
  }

  // Send error to popup
  sendToPopup({
    action: 'OPERATION_ERROR',
    error: message.error,
  });

  return { success: true };
}

/**
 * Handle simple error messages from content scripts (legacy format)
 */
async function handleContentScriptError(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = sender.tab && sender.tab.id;

  // Convert simple error message to ExtensionError format
  const extensionError = createError(
    'CONTENT_SCRIPT_ERROR',
    message.error || 'Content script error',
    message.details || 'No additional details provided'
  );

  // Log the error
  reportError(extensionError);

  // Update operation state if applicable
  if (tabId) {
    const tabInfo = activeTabs.get(tabId);
    const operation = tabInfo && tabInfo.operationId ? activeOperations.get(tabInfo.operationId) : null;

    if (operation) {
      operation.phase = 'error';
      operation.errors.push(extensionError);
      operation.progress = { percentage: 0, message: 'Operation failed' };
    }

    // Clean up on errors
    activeTabs.delete(tabId);
    if (operation) {
      activeOperations.delete(operation.id);
    }
  }

  // Send error to popup
  sendToPopup({
    action: 'OPERATION_ERROR',
    error: extensionError,
  });

  return { success: true };
}

/**
 * Handle storage access requests from content scripts
 */
async function handleGetStorage(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    const keys = message.keys || [];
    const result = await chrome.storage.session.get(keys);
    return result;
  } catch (error) {
    throw createError(
      'EXTENSION_ERROR',
      'Failed to access storage',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Handle apply cuts request from popup
 */
async function handleApplyCuts(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = message.tabId;
  if (!tabId) {
    throw createError('EXTENSION_ERROR', 'No tab ID provided for cut application');
  }

  try {
    // Get stored timestamps
    const storage = await chrome.storage.session.get([
      'claimcutter_timestamps',
      'claimcutter_operation_id',
      'claimcutter_collection_complete'
    ]);

    if (!storage.claimcutter_collection_complete) {
      throw createError('EXTENSION_ERROR', 'No completed timestamp collection found');
    }

    if (!storage.claimcutter_timestamps || storage.claimcutter_timestamps.length === 0) {
      throw createError('EXTENSION_ERROR', 'No timestamps found to apply');
    }

    // Navigate to editor and apply cuts
    await navigateToEditor(tabId, storage.claimcutter_operation_id);

    return { success: true };

  } catch (error) {
    throw createError(
      'EXTENSION_ERROR',
      'Failed to apply cuts',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Handle popup state request
 */
async function handleGetPopupState(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    const storage = await chrome.storage.session.get([
      'claimcutter_popup_state',
      'claimcutter_collection_results',
      'claimcutter_collection_complete',
      'claimcutter_batch_discovery',
      'claimcutter_batch_discovery_timestamp',
      'claimcutter_batch_settings'
    ]);

    console.log('handleGetPopupState - storage result:', storage);

    // The popup state contains the collection results
    const popupState = storage.claimcutter_popup_state || null;

    // Check for ongoing batch operations
    const allStorage = await chrome.storage.session.get();
    const activeBatchOp = Object.keys(allStorage).find(key => key.startsWith('batchOperation_'));
    let batchProcessingState = null;

    if (activeBatchOp) {
      const batchOp = allStorage[activeBatchOp];
      if (batchOp && !batchOp.completedAt) {
        batchProcessingState = {
          isProcessing: true,
          currentIndex: batchOp.currentIndex || 0,
          totalVideos: batchOp.videos?.length || 0,
          operationId: batchOp.id
        };
      }
    }

    // Return the popup state structure, with fallback to individual storage keys
    const result = {
      popupState: popupState,
      collectionResults: popupState?.collectionResults || storage.claimcutter_collection_results || null,
      collectionComplete: popupState?.phase === 'collection_complete' || storage.claimcutter_collection_complete || false,
      settings: popupState?.settings || null, // Include settings for apply-cuts script
      batchDiscovery: storage.claimcutter_batch_discovery || null,
      batchSettings: storage.claimcutter_batch_settings || null,
      batchProcessingState: batchProcessingState
    };

    console.log('handleGetPopupState - returning:', result);
    return result;

  } catch (error) {
    console.error('handleGetPopupState error:', error);
    throw createError(
      'EXTENSION_ERROR',
      'Failed to get popup state',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Handle popup state update request
 */
async function handleUpdatePopupState(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    await updatePopupState(message.state);
    return { success: true };
  } catch (error) {
    throw createError(
      'EXTENSION_ERROR',
      'Failed to update popup state',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Update popup state in storage for persistence
 */
async function updatePopupState(stateUpdate: any): Promise<void> {
  try {
    await chrome.storage.session.set({
      claimcutter_popup_state: stateUpdate
    });
  } catch (error) {
    console.error('Failed to update popup state:', error);
  }
}

/**
 * NEW: Handle video discovery request (batch mode)
 */
async function handleDiscoverVideos(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = message.tabId;
  if (!tabId) {
    throw createError('EXTENSION_ERROR', 'No tab ID provided for video discovery');
  }

  try {
    console.log('🔍 Starting video discovery on channel content page...');

    // Send progress update to popup
    sendToPopup({
      action: 'PROGRESS_UPDATE',
      phase: 'discovering',
      percentage: 10,
      message: 'Discovering copyright videos...',
    });

    // Inject the channel content detector content script
    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['channel-content-detector-content.js'],
    });

    // Send discovery message to the injected script
    await chrome.tabs.sendMessage(tabId, {
      action: 'START_DISCOVERY',
      settings: message.settings
    });

    return { success: true };

  } catch (error) {
    throw createError(
      'DISCOVERY_ERROR',
      'Failed to start video discovery',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * NEW: Handle batch operation start request
 */
async function handleStartBatchOperation(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = message.tabId;
  if (!tabId) {
    throw createError('EXTENSION_ERROR', 'No tab ID provided for batch operation');
  }

  try {
    const { videos, settings } = message;
    console.log(`🚀 Starting batch operation with ${videos.length} videos...`);

    // Create batch operation state
    const operationId = generateOperationId();
    const batchOperation = {
      id: operationId,
      videos,
      settings,
      tabId,
      startedAt: Date.now(),
      currentIndex: 0,
      totalClaimsFound: 0,
      totalCutsApplied: 0,
      successCount: 0,
      failureCount: 0,
      results: []
    };

    // Store batch operation
    await chrome.storage.session.set({
      [`batchOperation_${operationId}`]: batchOperation
    });

    // Send progress update to popup
    sendToPopup({
      action: 'PROGRESS_UPDATE',
      phase: 'batch_processing',
      percentage: 0,
      message: 'Starting batch processing...',
    });

    // Start processing the first video
    await processBatchVideo(tabId, operationId, 0);

    return { operationId };

  } catch (error) {
    throw createError(
      'BATCH_ERROR',
      'Failed to start batch operation',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * NEW: Process a single video in batch mode
 */
async function processBatchVideo(tabId: number, operationId: string, videoIndex: number): Promise<void> {
  try {
    // Get batch operation
    const storage = await chrome.storage.session.get([`batchOperation_${operationId}`]);
    const batchOperation = storage[`batchOperation_${operationId}`];

    if (!batchOperation) {
      throw new Error('Batch operation not found');
    }

    // Check if batch is paused
    if (batchOperation.isPaused) {
      console.log('⏸️ Batch operation is paused, stopping processing');
      return;
    }

    const { videos, settings } = batchOperation;
    const video = videos[videoIndex];

    if (!video) {
      // All videos processed - complete batch
      await completeBatchOperation(operationId);
      return;
    }

    console.log(`🎯 Processing video ${videoIndex + 1}/${videos.length}: ${video.title}`);

    // Update progress
    const percentage = Math.round((videoIndex / videos.length) * 100);
    sendToPopup({
      action: 'BATCH_PROGRESS_UPDATE',
      currentIndex: videoIndex,
      totalVideos: videos.length,
      currentVideo: video,
      status: 'processing'
    });

    // Update batch operation state
    batchOperation.currentIndex = videoIndex;
    await chrome.storage.session.set({
      [`batchOperation_${operationId}`]: batchOperation
    });

    // Navigate to copyright page
    console.log(`🔍 Navigating to copyright page: ${video.copyrightUrl}`);
    await chrome.tabs.update(tabId, { url: video.copyrightUrl });

    // Wait for page load, then start timestamp collection
    chrome.tabs.onUpdated.addListener(function listener(updatedTabId, changeInfo) {
      if (updatedTabId === tabId && changeInfo.status === 'complete') {
        chrome.tabs.onUpdated.removeListener(listener);

        // Small delay to ensure page is fully loaded
        setTimeout(async () => {
          try {
            // Check again if batch is still not paused before proceeding
            const currentStorage = await chrome.storage.session.get([`batchOperation_${operationId}`]);
            const currentBatchOp = currentStorage[`batchOperation_${operationId}`];

            if (currentBatchOp && !currentBatchOp.isPaused) {
              await startBatchTimestampCollection(tabId, operationId, videoIndex, settings);
            } else {
              console.log('⏸️ Batch paused during navigation, stopping processing');
            }
          } catch (error) {
            console.error('Failed to start batch timestamp collection:', error);
            await handleBatchVideoError(operationId, videoIndex, error);
          }
        }, 2000);
      }
    });

  } catch (error) {
    console.error(`Failed to process batch video ${videoIndex}:`, error);
    await handleBatchVideoError(operationId, videoIndex, error);
  }
}

/**
 * NEW: Handle batch discovery completion
 */
async function handleBatchDiscoveryComplete(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    console.log('📋 Batch discovery complete:', message.data);

    // Store discovered videos in session storage
    await chrome.storage.session.set({
      discoveredVideos: message.data.videos,
      discoveryComplete: true,
      discoveryTimestamp: Date.now()
    });

    // Send progress update to popup
    sendToPopup({
      action: 'PROGRESS_UPDATE',
      phase: 'discovery_complete',
      percentage: 100,
      message: `Discovery complete: ${message.data.videos.length} videos found`,
    });

    return { success: true };

  } catch (error) {
    throw createError(
      'DISCOVERY_ERROR',
      'Failed to handle batch discovery completion',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * NEW: Handle batch progress updates
 */
async function handleBatchProgressUpdate(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    console.log('📊 Batch progress update:', message);

    // Handle different message structures
    let progressData;
    if (message.progress) {
      // Structure from batch-processor.ts: { action: 'BATCH_PROGRESS_UPDATE', progress }
      progressData = message.progress;
    } else if (message.currentIndex !== undefined) {
      // Structure from batch-processor-content.ts: { action: 'BATCH_PROGRESS_UPDATE', currentIndex, totalVideos, currentVideo, status }
      progressData = {
        currentIndex: message.currentIndex,
        totalVideos: message.totalVideos,
        currentVideo: message.currentVideo,
        status: message.status
      };
    } else {
      console.warn('Unknown batch progress message structure:', message);
      return { success: false };
    }

    // Forward progress update to popup using BATCH_PROGRESS_UPDATE action
    sendToPopup({
      action: 'BATCH_PROGRESS_UPDATE',
      ...progressData
    });

    return { success: true };

  } catch (error) {
    throw createError(
      'BATCH_ERROR',
      'Failed to handle batch progress update',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * NEW: Handle batch timestamp collection completion
 */
async function handleBatchTimestampsCollected(message: any, tabId: number): Promise<any> {
  try {
    const { timestamps, count, rawCount, totalClaims, errors = [] } = message.data;
    const { operationId, batchVideoIndex } = message;

    console.log(`📋 Batch video ${batchVideoIndex + 1} timestamps collected: ${count} ranges from ${totalClaims} claims`);

    // Extract the base operation ID (remove the _video_X suffix)
    const baseOperationId = operationId.replace(/_video_\d+$/, '');

    // Get batch operation
    const storage = await chrome.storage.session.get([`batchOperation_${baseOperationId}`]);
    const batchOperation = storage[`batchOperation_${baseOperationId}`];

    if (!batchOperation) {
      throw new Error('Batch operation not found');
    }

    const video = batchOperation.videos[batchVideoIndex];

    if (count > 0) {
      // Navigate to editor and apply cuts
      console.log(`🎬 Navigating to editor for video ${batchVideoIndex + 1}...`);

      // Extract video ID and construct editor URL
      const videoIdMatch = video.copyrightUrl.match(/\/video\/([^\/]+)\/copyright/);
      if (!videoIdMatch) {
        throw new Error('Cannot extract video ID from copyright URL');
      }

      const videoId = videoIdMatch[1];
      const editorUrl = `https://studio.youtube.com/video/${videoId}/editor`;

      // Navigate to editor
      await chrome.tabs.update(tabId, { url: editorUrl });

      // Wait for page load, then apply cuts
      chrome.tabs.onUpdated.addListener(function listener(updatedTabId, changeInfo) {
        if (updatedTabId === tabId && changeInfo.status === 'complete') {
          chrome.tabs.onUpdated.removeListener(listener);

          // Small delay to ensure page is fully loaded
          setTimeout(async () => {
            try {
              await startBatchCutApplication(tabId, baseOperationId, batchVideoIndex, timestamps, batchOperation.settings);
            } catch (error) {
              console.error('Failed to start batch cut application:', error);
              await handleBatchVideoError(baseOperationId, batchVideoIndex, error);
            }
          }, 2000);
        }
      });
    } else {
      // No timestamps found, mark as completed and continue
      console.log(`⚠️ No timestamps found for video ${batchVideoIndex + 1}, skipping...`);
      await completeBatchVideoProcessing(baseOperationId, batchVideoIndex, {
        success: true,
        claimsFound: 0,
        cutsApplied: 0,
        message: 'No copyright claims found'
      });
    }

    return { success: true };

  } catch (error) {
    console.error('Failed to handle batch timestamps collected:', error);
    throw error;
  }
}

/**
 * NEW: Start batch timestamp collection for a single video
 */
async function startBatchTimestampCollection(tabId: number, operationId: string, videoIndex: number, settings: any): Promise<void> {
  try {
    console.log(`📋 Starting timestamp collection for video ${videoIndex + 1}...`);

    // Inject the timestamp collection script
    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['collect-timestamps.js'],
    });

    // Send start message to the injected script
    await chrome.tabs.sendMessage(tabId, {
      action: 'START_COLLECTION',
      dryRun: false,
      operationId: `${operationId}_video_${videoIndex}`,
      settings: settings || { mergeBuffer: 15, turboMode: false, autoSave: false },
      batchMode: true,
      batchVideoIndex: videoIndex
    });

  } catch (error) {
    throw new Error(`Failed to start batch timestamp collection: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * NEW: Start batch cut application
 */
async function startBatchCutApplication(tabId: number, operationId: string, videoIndex: number, timestamps: any[], settings: any): Promise<void> {
  try {
    console.log(`✂️ Starting cut application for video ${videoIndex + 1}...`);

    // Store timestamps in session storage for apply-cuts script
    await chrome.storage.session.set({
      claimcutter_timestamps: timestamps,
      claimcutter_collection_complete: true,
      claimcutter_collection_results: {
        count: timestamps.length,
        timestamps: timestamps,
        errors: [],
        completedAt: Date.now()
      },
      claimcutter_popup_state: {
        phase: 'collection_complete',
        collectionResults: {
          count: timestamps.length,
          timestamps: timestamps,
          errors: [],
          completedAt: Date.now()
        },
        settings: settings
      }
    });

    // Inject the apply-cuts script
    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['apply-cuts.js'],
    });

    // The apply-cuts script will auto-execute and handle the cuts
    // We'll listen for the CUTS_APPLIED message to continue the batch

  } catch (error) {
    throw new Error(`Failed to start batch cut application: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * NEW: Complete batch video processing
 */
async function completeBatchVideoProcessing(operationId: string, videoIndex: number, result: any): Promise<void> {
  try {
    // Get batch operation
    const storage = await chrome.storage.session.get([`batchOperation_${operationId}`]);
    const batchOperation = storage[`batchOperation_${operationId}`];

    if (batchOperation) {
      // Update results
      if (result.success) {
        batchOperation.successCount++;
        batchOperation.totalClaimsFound += result.claimsFound || 0;
        batchOperation.totalCutsApplied += result.cutsApplied || 0;
      } else {
        batchOperation.failureCount++;
      }

      batchOperation.results[videoIndex] = result;

      await chrome.storage.session.set({
        [`batchOperation_${operationId}`]: batchOperation
      });

      console.log(`✅ Video ${videoIndex + 1} completed: ${result.success ? 'success' : 'failed'}`);

      // Check if batch operation has been stopped or completed before continuing
      if (batchOperation.stopped || batchOperation.completedAt) {
        console.log('⏹️ Batch operation has been stopped, not continuing to next video');
        return;
      }

      // Check if batch operation is paused before continuing
      if (batchOperation.isPaused) {
        console.log('⏸️ Batch operation is paused, not continuing to next video');
        return;
      }

      // Continue with next video
      const nextIndex = videoIndex + 1;
      if (nextIndex < batchOperation.videos.length) {
        console.log(`⏭️ Continuing with next video (${nextIndex + 1}/${batchOperation.videos.length})...`);
        setTimeout(() => {
          processBatchVideo(batchOperation.tabId, operationId, nextIndex);
        }, 2000);
      } else {
        await completeBatchOperation(operationId);
      }
    }
  } catch (error) {
    console.error('Failed to complete batch video processing:', error);
  }
}

/**
 * NEW: Handle batch video error
 */
async function handleBatchVideoError(operationId: string, videoIndex: number, error: any): Promise<void> {
  try {
    await completeBatchVideoProcessing(operationId, videoIndex, {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      claimsFound: 0,
      cutsApplied: 0
    });
  } catch (err) {
    console.error('Failed to handle batch video error:', err);
  }
}

/**
 * NEW: Complete batch operation
 */
async function completeBatchOperation(operationId: string): Promise<void> {
  try {
    // Get batch operation
    const storage = await chrome.storage.session.get([`batchOperation_${operationId}`]);
    const batchOperation = storage[`batchOperation_${operationId}`];

    if (batchOperation) {
      batchOperation.completedAt = Date.now();

      console.log(`🎉 Batch operation complete: ${batchOperation.successCount}/${batchOperation.videos.length} successful`);

      // Store final results
      await chrome.storage.session.set({
        [`batchOperation_${operationId}`]: batchOperation,
        batchResults: batchOperation,
        batchComplete: true,
        batchCompletedAt: Date.now()
      });

      // Send completion update to popup
      sendToPopup({
        action: 'BATCH_COMPLETE',
        data: batchOperation
      });
    }
  } catch (error) {
    console.error('Failed to complete batch operation:', error);
  }
}

/**
 * NEW: Handle batch completion
 */
async function handleBatchComplete(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    console.log('🎉 Batch operation complete:', message.data);

    // Store batch results
    await chrome.storage.session.set({
      batchResults: message.data,
      batchComplete: true,
      batchCompletedAt: Date.now()
    });

    // Send completion update to popup
    sendToPopup({
      action: 'BATCH_COMPLETE',
      data: message.data
    });

    return { success: true };

  } catch (error) {
    throw createError(
      'BATCH_ERROR',
      'Failed to handle batch completion',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * NEW: Handle pause batch operation
 */
async function handlePauseBatchOperation(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    console.log('⏸️ Pausing batch operation...');

    // Find active batch operation
    const allStorage = await chrome.storage.session.get();
    const activeBatchOp = Object.keys(allStorage).find(key => key.startsWith('batchOperation_'));

    if (activeBatchOp) {
      const batchOp = allStorage[activeBatchOp];
      if (batchOp && !batchOp.completedAt) {
        // Mark as paused
        batchOp.pausedAt = Date.now();
        batchOp.isPaused = true;

        await chrome.storage.session.set({
          [activeBatchOp]: batchOp
        });

        console.log('✅ Batch operation paused');
        return { success: true, paused: true };
      }
    }

    return { success: false, error: 'No active batch operation found' };

  } catch (error) {
    throw createError(
      'BATCH_ERROR',
      'Failed to pause batch operation',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * NEW: Handle continue batch operation
 */
async function handleContinueBatchOperation(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    console.log('▶️ Continuing batch operation...');

    // Find paused batch operation
    const allStorage = await chrome.storage.session.get();
    const activeBatchOp = Object.keys(allStorage).find(key => key.startsWith('batchOperation_'));

    if (activeBatchOp) {
      const batchOp = allStorage[activeBatchOp];
      if (batchOp && batchOp.isPaused) {
        // Resume from current index
        batchOp.isPaused = false;
        batchOp.resumedAt = Date.now();
        delete batchOp.pausedAt;

        await chrome.storage.session.set({
          [activeBatchOp]: batchOp
        });

        // Continue processing from current video
        const tabId = batchOp.tabId;
        const currentIndex = batchOp.currentIndex || 0;

        console.log(`✅ Resuming batch operation from video ${currentIndex + 1}`);

        // Continue with current video
        setTimeout(() => {
          processBatchVideo(tabId, batchOp.id, currentIndex);
        }, 1000);

        return { success: true, resumed: true };
      }
    }

    return { success: false, error: 'No paused batch operation found' };

  } catch (error) {
    throw createError(
      'BATCH_ERROR',
      'Failed to continue batch operation',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * NEW: Handle stop batch operation
 */
async function handleStopBatchOperation(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    console.log('⏹️ Stopping batch operation...');

    // Find active batch operation
    const allStorage = await chrome.storage.session.get();
    const activeBatchOp = Object.keys(allStorage).find(key => key.startsWith('batchOperation_'));

    if (activeBatchOp) {
      const batchOp = allStorage[activeBatchOp];
      if (batchOp && !batchOp.completedAt) {
        // Mark as stopped
        batchOp.stoppedAt = Date.now();
        batchOp.completedAt = Date.now();
        batchOp.stopped = true;

        await chrome.storage.session.set({
          [activeBatchOp]: batchOp
        });

        // Send partial results to popup
        sendToPopup({
          action: 'BATCH_COMPLETE',
          data: batchOp
        });

        console.log('✅ Batch operation stopped');
        return { success: true, stopped: true };
      }
    }

    return { success: false, error: 'No active batch operation found' };

  } catch (error) {
    throw createError(
      'BATCH_ERROR',
      'Failed to stop batch operation',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * NEW: Handle clear batch operations
 */
async function handleClearBatchOperations(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    console.log('🧹 Clearing batch operations...');

    const { operationKeys } = message;

    if (operationKeys && operationKeys.length > 0) {
      await chrome.storage.session.remove(operationKeys);
      console.log(`✅ Cleared ${operationKeys.length} batch operations`);
    }

    return { success: true, cleared: operationKeys?.length || 0 };

  } catch (error) {
    throw createError(
      'BATCH_ERROR',
      'Failed to clear batch operations',
      error instanceof Error ? error.message : String(error)
    );
  }
}

// Utility Functions

/**
 * Start timestamp collection workflow
 */
async function startTimestampCollection(tabId: number, dryRun: boolean, operationId: string, settings?: any): Promise<void> {
  try {
    // Inject the timestamp collection script
    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['collect-timestamps.js'],
    });

    // Send start message to the injected script with settings
    await chrome.tabs.sendMessage(tabId, {
      action: 'START_COLLECTION',
      dryRun,
      operationId,
      settings: settings || { mergeBuffer: 15, turboMode: false, autoSave: false },
    });

  } catch (error) {
    throw createError(
      'EXTENSION_ERROR',
      'Failed to start timestamp collection',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Start cut application workflow
 */
async function startCutApplication(tabId: number, dryRun: boolean, operationId: string): Promise<void> {
  try {
    // Inject the cut application script
    await injectApplyCutsScript(tabId);

  } catch (error) {
    throw createError(
      'EXTENSION_ERROR',
      'Failed to start cut application',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Navigate to editor page
 */
async function navigateToEditor(tabId: number, operationId: string): Promise<void> {
  try {
    // Get current tab URL and construct editor URL
    const tab = await chrome.tabs.get(tabId);
    const currentUrl = tab.url || '';
    const videoIdMatch = currentUrl.match(/\/video\/([^/]+)/);

    if (!videoIdMatch) {
      throw createError('NAVIGATION_FAILED', 'Could not extract video ID from current URL');
    }

    const videoId = videoIdMatch[1];
    const editorUrl = `https://studio.youtube.com/video/${videoId}/editor`;

    // Update progress
    sendToPopup({
      action: 'PROGRESS_UPDATE',
      phase: 'applying',
      percentage: 60,
      message: 'Navigating to editor...',
    });

    // Navigate to editor page
    await chrome.tabs.update(tabId, { url: editorUrl });

    // Wait for page load, then inject cut application script
    chrome.tabs.onUpdated.addListener(function listener(updatedTabId, changeInfo) {
      if (updatedTabId === tabId && changeInfo.status === 'complete') {
        chrome.tabs.onUpdated.removeListener(listener);

        // Small delay to ensure page is fully loaded
        setTimeout(async () => {
          try {
            console.log(`🔍 Background: Page loaded, starting direct cut application after 2s delay`);
            await applyCutsDirectly(tabId);
            console.log(`✅ Background: Direct cut application completed successfully`);
          } catch (error) {
            console.error('❌ Background: Failed to apply cuts directly:', error);
            sendToPopup({
              action: 'OPERATION_ERROR',
              error: createError(
                'EXTENSION_ERROR',
                'Failed to apply cuts',
                error instanceof Error ? error.message : String(error)
              ),
            });
          }
        }, 2000);
      }
    });

  } catch (error) {
    throw createError(
      'NAVIGATION_FAILED',
      'Failed to navigate to editor',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * 🚀 IMPROVED APPROACH: Apply cuts directly using background script with proper workflow
 * This uses the improved logic that handles subsequent cuts correctly
 */
async function applyCutsDirectly(tabId: number): Promise<void> {
  try {
    console.log(`🚀 Background: Starting improved direct cut application for tab ${tabId}`);

    // Get stored timestamps
    const storage = await chrome.storage.session.get([
      'claimcutter_timestamps',
      'claimcutter_collection_results',
      'claimcutter_popup_state'
    ]);

    let timestamps: any[] = [];

    // Get the most recent timestamp data
    if (storage.claimcutter_collection_results?.timestamps) {
      timestamps = storage.claimcutter_collection_results.timestamps;
      console.log(`📋 Background: Using collection results timestamps: ${timestamps.length} found`);
    } else if (storage.claimcutter_timestamps) {
      timestamps = storage.claimcutter_timestamps;
      console.log(`📋 Background: Using direct timestamps: ${timestamps.length} found`);
    } else {
      throw new Error('No timestamps found for cut application');
    }

    if (timestamps.length === 0) {
      throw new Error('No valid timestamps to apply');
    }

    console.log(`🎯 Background: Applying ${timestamps.length} cuts with improved workflow:`,
      timestamps.map(t => `${t.start}s-${t.end}s`));

    // 🚀 IMPROVED: Apply cuts one by one using the working logic
    let appliedCount = 0;
    let failedCount = 0;
    const errors: string[] = [];

    for (let i = 0; i < timestamps.length; i++) {
      const timestamp = timestamps[i];
      const cutNumber = i + 1;

      try {
        console.log(`🎯 Background: Applying cut ${cutNumber}/${timestamps.length}: ${timestamp.start}s-${timestamp.end}s`);

        // Update progress
        sendToPopup({
          action: 'PROGRESS_UPDATE',
          phase: 'applying',
          percentage: 60 + (30 * i / timestamps.length),
          message: `Applying cut ${cutNumber}/${timestamps.length}...`,
        });

        await applySingleCutDirectly(tabId, timestamp, cutNumber);
        appliedCount++;
        console.log(`✅ Background: Cut ${cutNumber} applied successfully`);

        // Add delay between cuts to prevent conflicts
        if (i < timestamps.length - 1) {
          console.log(`⏱️ Background: Waiting before next cut...`);
          await new Promise(resolve => setTimeout(resolve, 1500));
        }

      } catch (error) {
        failedCount++;
        const errorMsg = `Cut ${cutNumber} failed: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.error(`❌ Background: ${errorMsg}`);

        // Continue with next cut even if one fails
        continue;
      }
    }

    console.log(`🎯 Background: Cut application completed - ${appliedCount} applied, ${failedCount} failed`);

    // Send completion message
    await chrome.runtime.sendMessage({
      action: 'CUTS_APPLIED',
      data: {
        applied: appliedCount,
        failed: failedCount,
        errors
      }
    });

    // Update final progress
    sendToPopup({
      action: 'PROGRESS_UPDATE',
      phase: 'complete',
      percentage: 100,
      message: `Cuts applied: ${appliedCount} successful, ${failedCount} failed`,
    });

  } catch (error) {
    console.error(`❌ Background: Direct cut application failed:`, error);

    // Clean up debugger session
    try {
      await handleEndDebuggerSession({ action: 'END_DEBUGGER_SESSION' }, { tab: { id: tabId } });
    } catch (cleanupError) {
      console.warn(`⚠️ Background: Failed to clean up debugger session:`, cleanupError);
    }

    throw createError(
      'EXTENSION_ERROR',
      'Failed to apply cuts directly',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Apply a single cut using direct DOM manipulation + debugger API
 *
 * SIMPLIFIED WORKFLOW (based on user discovery):
 * 1. Set timeline position to start time + FOCUS EVENT #1 + DELAY (updates timeline needle)
 * 2. Click "New Cut" button → start time is AUTOMATICALLY populated from timeline!
 * 3. Set ONLY end time in cut dialog + FOCUS EVENT #2 + DELAY (updates end bracket)
 * 4. Click Cut (checkmark) button to apply
 *
 * CRITICAL SEQUENCE WITH DELAYS:
 * - Timeline input value → Focus event → Delay → "New Cut" click
 * - End time input → Focus event → Delay → "Cut" button click
 * - Each delay ensures timeline is fully updated before next action
 *
 * KEY INSIGHT: YouTube Studio automatically carries timeline position to start time
 * when "New Cut" is clicked, so we only need to set the end time manually!
 */
async function applySingleCutDirectly(tabId: number, timestamp: any, cutNumber: number): Promise<void> {
  console.log(`🎯 Background: Applying cut ${cutNumber} - ${timestamp.start}s to ${timestamp.end}s`);

  // Convert seconds to timestamp format
  const startTime = secondsToTimestamp(timestamp.start);
  const endTime = secondsToTimestamp(timestamp.end);

  console.log(`🕐 Background: Formatted times - Start: ${startTime}, End: ${endTime}`);

  try {
    // Use chrome.scripting to execute DOM manipulation directly
    console.log(`🔧 Background: Injecting cut application function for cut ${cutNumber}`);

    // Apply the actual cut
    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: async (startTime, endTime, cutNumber) => {
        console.log(`🎯 Page: Applying cut ${cutNumber} with times ${startTime} → ${endTime} (SIMPLIFIED WORKFLOW)`);

        // Helper function for delays
        const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

        try {
          // Step 1: Enter trim mode (only for first cut)
          if (cutNumber === 1) {
            console.log(`🔧 Page: Entering trim mode for first cut`);
            const trimButtons = document.querySelectorAll('a.style-scope.ytve-entrypoint-options-panel');
            let trimButton = null;

            for (const button of trimButtons) {
              if (button.textContent?.trim() === 'Trim & cut') {
                trimButton = button;
                break;
              }
            }

            if (!trimButton) {
              throw new Error('Trim & cut button not found');
            }

            trimButton.click();
            await delay(2000); // Wait for trim mode to load
          }

          // 🚀 CRITICAL WORKFLOW RESTART: For cuts after the first, restart the entire workflow
          console.log(`🎯 Page: Setting timeline position to start time: ${startTime} (Cut ${cutNumber})`);

          // 🔧 CRITICAL FIX: Find the VISIBLE timeline input (not hidden by dialog)
          const allTimelineInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
          let timelineInput = null;

          // Find the timeline input that's NOT in a dialog (the main timeline toolbar input)
          for (const input of allTimelineInputs) {
            const parent = input.closest('ytcp-media-timestamp-input');
            const parentClass = parent ? parent.className : '';

            // Skip dialog inputs, find the toolbar input
            if (!parentClass.includes('dialog')) {
              timelineInput = input;
              console.log(`🔍 Page: Found timeline toolbar input with parent class: ${parentClass}`);
              break;
            }
          }

          if (!timelineInput) {
            throw new Error('Timeline toolbar input not found');
          }

          // 🚀 CRITICAL: Always clear and set timeline position (essential for subsequent cuts)
          console.log(`🧹 Page: Current timeline value before setting: "${timelineInput.value}"`);
          timelineInput.focus();
          timelineInput.select();
          timelineInput.value = ''; // Clear first
          await delay(100); // Small delay
          timelineInput.value = startTime; // Set new value
          timelineInput.dispatchEvent(new Event('input', { bubbles: true }));
          timelineInput.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✅ Page: Timeline value after setting: "${timelineInput.value}"`);

          // 🚀 FOCUS EVENT #1: CRITICAL - Update timeline position with focus event
          console.log(`🔧 Page: Triggering focus event to update timeline position`);
          timelineInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Tab', bubbles: true }));
          await delay(800); // Longer delay for subsequent cuts

          console.log(`📍 Page: Timeline positioned at: ${timelineInput.value}`);

          // 🚀 CRITICAL: Longer delay for subsequent cuts to ensure timeline is fully updated
          console.log(`⏱️ Page: Waiting for timeline update before clicking New Cut (Cut ${cutNumber})...`);
          await delay(800); // Longer delay for subsequent cuts

          // Step 2: Click "New Cut" button (start time will be auto-populated!)
          console.log(`🔧 Page: Clicking New Cut button`);
          const newCutButton = document.querySelector('ytcp-button.style-scope.ytve-trim-options-panel');
          if (!newCutButton) {
            throw new Error('New Cut button not found');
          }

          // Check if button is disabled
          const isDisabled = newCutButton.hasAttribute('disabled') || newCutButton.getAttribute('aria-disabled') === 'true';
          console.log(`🔍 Page: New Cut button disabled: ${isDisabled} (Cut ${cutNumber})`);

          if (isDisabled) {
            console.log(`🔧 Page: New Cut button is disabled for cut ${cutNumber}, implementing full workflow restart`);

            // 🚀 CRITICAL FIX: For subsequent cuts, we need to restart the entire workflow
            // The timeline needle is likely positioned over an already-cut region

            // Calculate a safe position that's definitely not in a cut region
            // Use the start time of the current cut as the safe position
            const safeTime = startTime;

            console.log(`🕐 Page: Restarting workflow - setting timeline to start position ${safeTime}`);
            console.log(`🧹 Page: Current timeline value before restart: "${timelineInput.value}"`);

            // 🚀 FULL RESTART: Clear and set timeline position again
            timelineInput.focus();
            timelineInput.select();
            timelineInput.value = ''; // Clear completely
            await delay(200); // Longer delay for restart
            timelineInput.value = safeTime; // Set to start time
            timelineInput.dispatchEvent(new Event('input', { bubbles: true }));
            timelineInput.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✅ Page: Timeline value after restart: "${timelineInput.value}"`);

            // 🚀 CRITICAL: Full focus event sequence for restart
            console.log(`🔧 Page: Triggering full focus event sequence for workflow restart`);
            timelineInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Tab', bubbles: true }));
            await delay(1000); // Longer delay for restart

            // 🚀 ADDITIONAL: Try clicking on the timeline input to ensure it's active
            timelineInput.click();
            await delay(500);

            // 🚀 CRITICAL: Check if New Cut is enabled after full restart
            const isStillDisabled = newCutButton.hasAttribute('disabled') || newCutButton.getAttribute('aria-disabled') === 'true';
            console.log(`🔍 Page: After full workflow restart, New Cut button disabled: ${isStillDisabled}`);

            if (isStillDisabled) {
              // 🚀 LAST RESORT: Try moving to a different position first, then back
              console.log(`🔧 Page: Last resort - moving to different position then back to start`);

              // Move to a position 5 seconds after start
              const [startMinutes, startSeconds] = startTime.split(':').map(Number);
              const tempSeconds = startMinutes * 60 + startSeconds + 5;
              const tempMinutes = Math.floor(tempSeconds / 60);
              const remainingSeconds = tempSeconds % 60;
              const tempTime = `${tempMinutes}:${remainingSeconds.toString().padStart(2, '0')}`;

              timelineInput.value = tempTime;
              timelineInput.dispatchEvent(new Event('input', { bubbles: true }));
              timelineInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Tab', bubbles: true }));
              await delay(500);

              // Now move back to start
              timelineInput.value = startTime;
              timelineInput.dispatchEvent(new Event('input', { bubbles: true }));
              timelineInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Tab', bubbles: true }));
              await delay(500);

              const isFinallyEnabled = !newCutButton.hasAttribute('disabled') && newCutButton.getAttribute('aria-disabled') !== 'true';
              if (!isFinallyEnabled) {
                throw new Error(`New Cut button still disabled after all restart attempts for cut ${cutNumber}`);
              }
            }

            console.log(`✅ Page: Workflow restarted successfully, New Cut is now enabled for cut ${cutNumber}`);
          }

          console.log(`🔧 Page: Clicking New Cut button (final attempt)`);
          newCutButton.click();
          await delay(1000); // Wait for cut dialog to appear

          // Step 3: Find timestamp inputs in cut dialog
          console.log(`🔧 Page: Finding timestamp inputs in cut dialog`);
          const allInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
          const visibleInputs = Array.from(allInputs).filter(input =>
            input.offsetParent !== null
          );

          console.log(`🔍 Page: Found ${visibleInputs.length} visible timestamp inputs`);

          // Debug: Log all visible inputs with their values and parent info
          visibleInputs.forEach((input, index) => {
            const value = input.value || 'empty';
            const parent = input.closest('ytcp-media-timestamp-input');
            const parentClass = parent ? parent.className : 'no-parent';
            console.log(`🔍 Page: Input ${index}: value="${value}", parent="${parentClass}"`);
          });

          if (visibleInputs.length < 2) {
            throw new Error(`Expected at least 2 timestamp inputs, found ${visibleInputs.length}`);
          }

          // Use the first 2 inputs (cut dialog inputs, not timeline toolbar input)
          // Based on debug logs: Input 0 and 1 are dialog inputs, Input 2 is timeline toolbar
          const startInput = visibleInputs[0];
          const endInput = visibleInputs[1];

          // 🚀 SIMPLIFIED: Verify start time was auto-populated from timeline position
          console.log(`✅ Page: Start time auto-populated: "${startInput.value}" (expected: ${startTime})`);
          console.log(`🔍 Page: End input current value: "${endInput.value}"`);

          // 🚀 IMPROVED: Clear end input first to prevent race conditions, then set end time
          console.log(`🕐 Page: Setting end time to ${endTime} (Cut ${cutNumber})`);
          console.log(`🔍 Page: End input current value before clearing: "${endInput.value}"`);

          // 🚀 CRITICAL: Clear the end input first to prevent auto-population conflicts
          endInput.focus();
          endInput.select();
          endInput.value = ''; // Clear any auto-populated value
          endInput.dispatchEvent(new Event('input', { bubbles: true }));
          await delay(200); // Wait for clear to process

          // Now set the actual end time
          endInput.value = endTime;
          endInput.dispatchEvent(new Event('input', { bubbles: true }));
          endInput.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✅ Page: End input value after setting: "${endInput.value}"`);

          // 🚀 FOCUS EVENT #2: Use Tab key to trigger timeline bracket update for end time
          console.log(`🔧 Page: Pressing Tab to trigger timeline update for end time`);
          endInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Tab', bubbles: true }));
          await delay(800); // Longer delay for subsequent cuts

          // 🚀 CRITICAL: Additional delay to give timeline time to update end bracket
          console.log(`⏱️ Page: Waiting for timeline end bracket to update (Cut ${cutNumber})...`);
          await delay(800); // Longer delay for subsequent cuts

          // 🚀 VERIFICATION: Check that end time wasn't reverted by auto-population
          console.log(`🔍 Page: Final end input value: "${endInput.value}" (expected: "${endTime}")`);
          if (endInput.value !== endTime) {
            console.warn(`⚠️ Page: End time was reverted! Re-setting to ${endTime}`);
            endInput.value = endTime;
            endInput.dispatchEvent(new Event('input', { bubbles: true }));
            endInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Tab', bubbles: true }));
            await delay(500);
          }

          console.log(`✅ Page: End time set and timeline brackets fully updated for cut ${cutNumber}`);

          // Step 4: Click Cut button (checkmark) to apply the cut
          console.log(`✂️ Page: Clicking Cut button to apply cut ${cutNumber}`);
          const allIconButtons = document.querySelectorAll('ytcp-icon-button');
          const cutButton = Array.from(allIconButtons).find(btn =>
            btn.offsetParent !== null && btn.getAttribute('aria-label') === 'Cut'
          );

          if (!cutButton) {
            // Debug: Log all available icon buttons
            console.log(`🔍 Page: Available icon buttons:`, Array.from(allIconButtons).map(btn => ({
              label: btn.getAttribute('aria-label'),
              visible: btn.offsetParent !== null,
              disabled: btn.hasAttribute('disabled')
            })));
            throw new Error('Cut button (checkmark) not found');
          }

          // Check if cut button is disabled
          const cutButtonDisabled = cutButton.hasAttribute('disabled') || cutButton.getAttribute('aria-disabled') === 'true';
          console.log(`🔍 Page: Cut button disabled: ${cutButtonDisabled}`);

          if (cutButtonDisabled) {
            throw new Error(`Cut button is disabled for cut ${cutNumber} - timeline brackets may not be properly set`);
          }

          cutButton.click();
          console.log(`✂️ Page: Cut button clicked for cut ${cutNumber}, waiting for application...`);
          await delay(1500); // Longer wait for cut to be applied

          // 🚀 VERIFICATION: Check if cut was actually applied by looking for cut indicators
          console.log(`🔍 Page: Verifying cut ${cutNumber} was applied...`);
          const cutElements = document.querySelectorAll('[class*="cut"], [aria-label*="Cut"]');
          console.log(`🔍 Page: Found ${cutElements.length} cut-related elements after applying cut ${cutNumber}`);

          console.log(`✅ Page: Cut ${cutNumber} applied successfully with improved workflow`);
          return { success: true, cutNumber, startTime, endTime };

        } catch (error) {
          console.error(`❌ Page: Failed to apply cut ${cutNumber}:`, error);
          return { success: false, cutNumber, error: error.message };
        }
      },
      args: [startTime, endTime, cutNumber]
    });

    console.log(`✅ Background: Cut ${cutNumber} function injection completed`, result);

    // Check if the function executed successfully
    if (result && result[0] && result[0].result !== undefined) {
      console.log(`🔍 Background: Cut ${cutNumber} function result:`, JSON.stringify(result[0].result, null, 2));

      // Check if the function reported success
      if (result[0].result.success) {
        console.log(`✅ Background: Cut ${cutNumber} applied successfully in page context`);
      } else {
        console.error(`❌ Background: Cut ${cutNumber} failed in page context:`, result[0].result.error);
      }
    } else {
      console.warn(`⚠️ Background: Cut ${cutNumber} function returned no result`);
    }

  } catch (error) {
    console.error(`❌ Background: Failed to inject cut ${cutNumber} function:`, error);
    throw error;
  }
}

/**
 * Function to be injected and executed in the page context
 * This applies a single cut using the correct timestamp values
 */
async function applyCutInPage(startTime: string, endTime: string, cutNumber: number): Promise<any> {
  console.log(`🎯 Page: FUNCTION STARTED - Applying cut ${cutNumber} with times ${startTime} → ${endTime}`);
  console.log(`🔍 Page: Function arguments received:`, { startTime, endTime, cutNumber });

  // Helper function for delays
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  console.log(`🔍 Page: Helper function defined, starting try block...`);

  try {
    // Step 1: Enter trim mode (only for first cut)
    if (cutNumber === 1) {
      console.log(`🔧 Page: Entering trim mode for first cut`);
      const trimButtons = document.querySelectorAll('a.style-scope.ytve-entrypoint-options-panel');
      let trimButton: Element | null = null;

      for (const button of trimButtons) {
        if (button.textContent?.trim() === 'Trim & cut') {
          trimButton = button;
          break;
        }
      }

      if (!trimButton) {
        throw new Error('Trim & cut button not found');
      }

      (trimButton as HTMLElement).click();
      await delay(2000); // Wait for trim mode to load
    }

    // Step 2: Click "New Cut" button
    console.log(`🔧 Page: Clicking New Cut button`);
    const newCutButton = document.querySelector('ytcp-button.style-scope.ytve-trim-options-panel');
    if (!newCutButton) {
      throw new Error('New Cut button not found');
    }

    (newCutButton as HTMLElement).click();
    await delay(1000); // Wait for cut dialog to appear

    // Step 3: Find timestamp inputs
    console.log(`🔧 Page: Finding timestamp inputs`);
    const allInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
    const visibleInputs = Array.from(allInputs).filter(input =>
      (input as HTMLElement).offsetParent !== null
    );

    console.log(`🔍 Page: Found ${visibleInputs.length} visible timestamp inputs`);

    if (visibleInputs.length < 2) {
      throw new Error(`Expected at least 2 timestamp inputs, found ${visibleInputs.length}`);
    }

    // Use the last 2 inputs (cut dialog inputs, not timeline input)
    const cutInputs = visibleInputs.slice(-2);
    const startInput = cutInputs[0] as HTMLInputElement;
    const endInput = cutInputs[1] as HTMLInputElement;

    // Step 4: Set start time
    console.log(`🕐 Page: Setting start time to ${startTime}`);
    startInput.focus();
    startInput.select();
    startInput.value = startTime;

    // Trigger input events
    startInput.dispatchEvent(new Event('input', { bubbles: true }));
    startInput.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(500);

    // Step 5: Set end time
    console.log(`🕐 Page: Setting end time to ${endTime}`);
    endInput.focus();
    endInput.select();
    endInput.value = endTime;

    // Trigger input events
    endInput.dispatchEvent(new Event('input', { bubbles: true }));
    endInput.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(500);

    // Step 6: Click Cut button (checkmark)
    console.log(`✂️ Page: Clicking Cut button to apply cut`);
    const allIconButtons = document.querySelectorAll('ytcp-icon-button');
    const cutButton = Array.from(allIconButtons).find(btn =>
      (btn as HTMLElement).offsetParent !== null && btn.getAttribute('aria-label') === 'Cut'
    );

    if (!cutButton) {
      throw new Error('Cut button (checkmark) not found');
    }

    (cutButton as HTMLElement).click();
    await delay(1000); // Wait for cut to be applied

    console.log(`✅ Page: Cut ${cutNumber} applied successfully`);
    console.log(`🎯 Page: FUNCTION ENDING - Returning success for cut ${cutNumber}`);
    return { success: true, cutNumber, startTime, endTime };

  } catch (error) {
    console.error(`❌ Page: Failed to apply cut ${cutNumber}:`, error);
    console.log(`🎯 Page: FUNCTION ENDING - Returning error for cut ${cutNumber}`);
    return { success: false, cutNumber, error: error.message };
  }
}

/**
 * Convert seconds to YouTube Studio timestamp format
 */
function secondsToTimestamp(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * Send message to popup
 */
function sendToPopup(message: any): void {
  try {
    chrome.runtime.sendMessage(message);
  } catch (error) {
    console.error('Failed to send message to popup:', error);
  }
}

/**
 * Generate unique operation ID
 */
function generateOperationId(): string {
  return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Format duration in seconds to human readable format
 */
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;

  if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

/**
 * Enhanced extension installation handler
 */
chrome.runtime.onInstalled.addListener(async (details) => {
  try {
    console.log('ClaimCutter extension installed:', details.reason);

    if (details.reason === 'install') {
      // Clear any existing session data
      await chrome.storage.session.clear();

      // Set up initial configuration
      await chrome.storage.session.set({
        claimcutter_version: chrome.runtime.getManifest().version,
        claimcutter_install_time: Date.now(),
      });

      console.log('Extension installed and configured');
    }
  } catch (error) {
    console.error('Extension installation failed:', error);
    reportError(createError(
      'EXTENSION_ERROR',
      'Extension installation failed',
      error instanceof Error ? error.message : String(error),
      'Extension installation'
    ));
  }
});

/**
 * Auto-navigate to editor for turbo mode
 */
async function autoNavigateToEditor(tabId: number, operationId: string): Promise<void> {
  try {
    // Get current tab URL
    const tab = await chrome.tabs.get(tabId);
    if (!tab.url) {
      throw new Error('Cannot access tab URL');
    }

    // Extract video ID from copyright page URL
    const videoIdMatch = tab.url.match(/\/video\/([^\/]+)\/copyright/);
    if (!videoIdMatch) {
      throw new Error('Cannot extract video ID from URL');
    }

    const videoId = videoIdMatch[1];
    const editorUrl = `https://studio.youtube.com/video/${videoId}/editor`;

    console.log(`🚀 Turbo mode: Navigating to editor: ${editorUrl}`);

    // Update progress
    sendToPopup({
      action: 'PROGRESS_UPDATE',
      phase: 'applying',
      percentage: 50,
      message: 'Turbo mode: Navigating to editor...',
    });

    // Navigate to editor
    await chrome.tabs.update(tabId, { url: editorUrl });

    // Wait for page to load, then start cut application
    setTimeout(async () => {
      try {
        await startCutApplication(tabId, false, operationId);
      } catch (error) {
        console.error('Failed to start cut application in turbo mode:', error);
        throw error;
      }
    }, 3000); // 3 second delay for page load

  } catch (error) {
    throw createError(
      'TURBO_MODE_ERROR',
      'Failed to auto-navigate to editor',
      error instanceof Error ? error.message : String(error)
    );
  }
}

// Global debugger session state
let activeDebuggerSession: { tabId: number; attached: boolean } | null = null;

/**
 * Start a persistent debugger session for the entire cut application process
 */
async function handleStartDebuggerSession(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = sender.tab && sender.tab.id;
  if (!tabId) {
    throw createError('EXTENSION_ERROR', 'No tab ID provided for debugger session start');
  }

  try {
    console.log('🚀 Background: Starting persistent debugger session for tab:', tabId);

    // If already attached to this tab, return success
    if (activeDebuggerSession && activeDebuggerSession.tabId === tabId && activeDebuggerSession.attached) {
      console.log('✅ Background: Debugger already attached to this tab');
      return { success: true, alreadyAttached: true };
    }

    // Detach from any previous session first
    if (activeDebuggerSession && activeDebuggerSession.attached) {
      console.log('🔧 Background: Detaching from previous debugger session...');
      try {
        chrome.debugger.detach({ tabId: activeDebuggerSession.tabId }, () => {
          console.log('🔧 Background: Previous session detached');
        });
      } catch (error) {
        console.warn('⚠️ Background: Error detaching previous session:', error);
      }
    }

    // Attach debugger to new tab
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.attach({ tabId }, '1.3', () => {
        if (chrome.runtime.lastError) {
          reject(new Error(`Failed to attach debugger: ${chrome.runtime.lastError.message}`));
          return;
        }
        console.log('✅ Background: Persistent debugger session attached successfully');
        resolve();
      });
    });

    // Update session state
    activeDebuggerSession = { tabId, attached: true };

    return { success: true };

  } catch (error) {
    console.error('❌ Background: Failed to start debugger session:', error);
    activeDebuggerSession = null;
    throw createError(
      'DEBUGGER_ERROR',
      'Failed to start debugger session',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * End the persistent debugger session
 */
async function handleEndDebuggerSession(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  try {
    console.log('🔧 Background: Ending persistent debugger session...');

    if (!activeDebuggerSession || !activeDebuggerSession.attached) {
      console.log('ℹ️ Background: No active debugger session to end');
      return { success: true, wasAttached: false };
    }

    const tabId = activeDebuggerSession.tabId;

    // Detach debugger
    chrome.debugger.detach({ tabId }, () => {
      if (chrome.runtime.lastError) {
        console.warn('⚠️ Background: Error detaching debugger session:', chrome.runtime.lastError.message);
      } else {
        console.log('🔧 Background: Persistent debugger session detached successfully');
      }
    });

    // Clear session state
    activeDebuggerSession = null;

    return { success: true, wasAttached: true };

  } catch (error) {
    console.error('❌ Background: Failed to end debugger session:', error);
    activeDebuggerSession = null;
    throw createError(
      'DEBUGGER_ERROR',
      'Failed to end debugger session',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Handle trusted Tab event request from content script
 * Uses Chrome Debugger API to dispatch trusted keyboard events
 * Now works with persistent debugger session
 */
async function handleTrustedTabEvent(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = sender.tab && sender.tab.id;
  if (!tabId) {
    throw createError('EXTENSION_ERROR', 'No tab ID provided for trusted Tab event');
  }

  try {
    console.log('🚀 Background: Handling trusted Tab event request for tab:', tabId);

    // Check if we have an active debugger session for this tab
    if (!activeDebuggerSession || activeDebuggerSession.tabId !== tabId || !activeDebuggerSession.attached) {
      throw new Error('No active debugger session for this tab. Start session first.');
    }

    console.log('⌨️ Background: Dispatching trusted Tab key event using persistent session...');

    // Dispatch trusted Tab keyDown event
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId },
        'Input.dispatchKeyEvent',
        {
          type: 'keyDown',
          key: 'Tab',
          keyCode: 9,
          code: 'Tab',
          windowsVirtualKeyCode: 9
        },
        (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Tab keyDown failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve();
        }
      );
    });

    // Small delay between keyDown and keyUp
    await new Promise(resolve => setTimeout(resolve, 50));

    // Dispatch trusted Tab keyUp event
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId },
        'Input.dispatchKeyEvent',
        {
          type: 'keyUp',
          key: 'Tab',
          keyCode: 9,
          code: 'Tab',
          windowsVirtualKeyCode: 9
        },
        (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Tab keyUp failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve();
        }
      );
    });

    console.log('✅ Background: Trusted Tab event dispatched successfully');

    // Wait for potential timeline update
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('🎯 Background: Trusted Tab event sequence completed');

    return { success: true };

  } catch (error) {
    console.error('❌ Background: Trusted Tab event failed:', error);

    throw createError(
      'DEBUGGER_ERROR',
      'Failed to execute trusted Tab event',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Handle trusted Enter event request from content script
 * Uses Chrome Debugger API to dispatch trusted keyboard events
 * Works with persistent debugger session
 */
async function handleTrustedEnterEvent(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  const tabId = sender.tab && sender.tab.id;
  if (!tabId) {
    throw createError('EXTENSION_ERROR', 'No tab ID provided for trusted Enter event');
  }

  try {
    console.log('🚀 Background: Handling trusted Enter event request for tab:', tabId);

    // Check if we have an active debugger session for this tab
    if (!activeDebuggerSession || activeDebuggerSession.tabId !== tabId || !activeDebuggerSession.attached) {
      throw new Error('No active debugger session for this tab. Start session first.');
    }

    console.log('⌨️ Background: Dispatching trusted Enter key event using persistent session...');

    // Dispatch trusted Enter keyDown event
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId },
        'Input.dispatchKeyEvent',
        {
          type: 'keyDown',
          key: 'Enter',
          keyCode: 13,
          code: 'Enter',
          windowsVirtualKeyCode: 13
        },
        (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Enter keyDown failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve();
        }
      );
    });

    // Small delay between keyDown and keyUp
    await new Promise(resolve => setTimeout(resolve, 50));

    // Dispatch trusted Enter keyUp event
    await new Promise<void>((resolve, reject) => {
      chrome.debugger.sendCommand(
        { tabId },
        'Input.dispatchKeyEvent',
        {
          type: 'keyUp',
          key: 'Enter',
          keyCode: 13,
          code: 'Enter',
          windowsVirtualKeyCode: 13
        },
        (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Enter keyUp failed: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve();
        }
      );
    });

    console.log('✅ Background: Trusted Enter event dispatched successfully');

    // Wait for potential UI update
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('🎯 Background: Trusted Enter event sequence completed');

    return { success: true };

  } catch (error) {
    console.error('❌ Background: Trusted Enter event failed:', error);

    throw createError(
      'DEBUGGER_ERROR',
      'Failed to execute trusted Enter event',
      error instanceof Error ? error.message : String(error)
    );
  }
}

console.log('ClaimCutter enhanced background service worker loaded');
