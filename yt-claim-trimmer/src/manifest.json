{"manifest_version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "YouTube Copyright Claim <PERSON> - Automate copyright claim resolution in YouTube Studio by batch trimming claimed segments", "permissions": ["activeTab", "scripting", "storage", "debugger"], "host_permissions": ["https://studio.youtube.com/*"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "content_scripts": [], "web_accessible_resources": [], "minimum_chrome_version": "115"}