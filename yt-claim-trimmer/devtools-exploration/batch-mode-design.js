/**
 * Batch Mode Design - Optimal Workflow Implementation
 * 
 * This approach leverages existing ClaimCutter flows by:
 * 1. Collecting video list from channel content page
 * 2. Navigating to each video's copyright page
 * 3. Using existing proven timestamp collection and cut application
 * 4. Processing videos sequentially with progress tracking
 */

console.log('🚀 ClaimCutter Batch Mode - Optimal Workflow Design');

// ============================================================================
// BATCH MODE CORE FUNCTIONS
// ============================================================================

/**
 * Main batch mode orchestrator
 */
async function startBatchMode() {
  console.log('\n🎯 STARTING BATCH MODE');
  
  try {
    // Step 1: Validate we're on channel content page
    if (!isChannelContentPage()) {
      throw new Error('Not on channel content page');
    }
    
    // Step 2: Discover videos with copyright issues
    const videoQueue = discoverCopyrightVideos();
    if (videoQueue.length === 0) {
      console.log('✅ No videos with copyright issues found');
      return;
    }
    
    console.log(`📊 Found ${videoQueue.length} videos to process`);
    
    // Step 3: Process each video using existing flow
    const results = await processBatchQueue(videoQueue);
    
    // Step 4: Show summary
    showBatchSummary(results);
    
    return results;
    
  } catch (error) {
    console.error('❌ Batch mode failed:', error);
  }
}

/**
 * Detect if we're on channel content page
 */
function isChannelContentPage() {
  const url = window.location.href;
  const isChannelPage = url.includes('/channel/') && url.includes('/videos');
  const hasVideoTable = document.querySelectorAll('tr, [role="row"]').length > 5;
  
  console.log('Page Detection:', {
    url,
    isChannelPage,
    hasVideoTable,
    compatible: isChannelPage && hasVideoTable
  });
  
  return isChannelPage && hasVideoTable;
}

/**
 * Discover videos with copyright issues and extract video IDs
 */
function discoverCopyrightVideos() {
  console.log('🔍 Discovering videos with copyright issues...');
  
  const videoRows = document.querySelectorAll('tr, [role="row"]');
  const copyrightVideos = [];
  
  videoRows.forEach((row, index) => {
    const rowText = row.textContent || '';
    
    // Look for copyright indicators
    const copyrightIndicators = [
      'Copyright',
      'See details',
      'Take action',
      'Monetization',
      'Limited'
    ];
    
    const hasCopyrightIssue = copyrightIndicators.some(indicator => 
      rowText.includes(indicator)
    );
    
    if (!hasCopyrightIssue) return;
    
    // Extract video information
    const videoInfo = extractVideoInfo(row, index);
    if (videoInfo.videoId) {
      copyrightVideos.push(videoInfo);
    }
  });
  
  console.log(`Found ${copyrightVideos.length} videos with copyright issues:`);
  copyrightVideos.forEach((video, i) => {
    console.log(`${i + 1}. "${video.title}" (${video.videoId})`);
  });
  
  return copyrightVideos;
}

/**
 * Extract video information from table row
 */
function extractVideoInfo(row, index) {
  // Extract title
  const titleSelectors = [
    '[data-testid="video-title"]',
    '.video-title',
    'h3',
    'h4',
    'a[href*="/video/"]'
  ];
  
  let title = `Video ${index + 1}`;
  for (const selector of titleSelectors) {
    const element = row.querySelector(selector);
    if (element?.textContent?.trim()) {
      title = element.textContent.trim();
      break;
    }
  }
  
  // Extract video ID from href attributes
  let videoId = null;
  const links = row.querySelectorAll('a[href*="/video/"]');
  
  for (const link of links) {
    const href = link.getAttribute('href');
    const match = href?.match(/\/video\/([a-zA-Z0-9_-]{11})/);
    if (match) {
      videoId = match[1];
      break;
    }
  }
  
  // Alternative: look for video ID in data attributes
  if (!videoId) {
    const dataElements = row.querySelectorAll('[data-video-id]');
    for (const element of dataElements) {
      const id = element.getAttribute('data-video-id');
      if (id && id.length === 11) {
        videoId = id;
        break;
      }
    }
  }
  
  return {
    title,
    videoId,
    copyrightUrl: videoId ? `https://studio.youtube.com/video/${videoId}/copyright` : null,
    editorUrl: videoId ? `https://studio.youtube.com/video/${videoId}/editor` : null,
    row
  };
}

/**
 * Process the batch queue using existing ClaimCutter flows
 */
async function processBatchQueue(videoQueue) {
  console.log(`\n🔄 Processing ${videoQueue.length} videos in batch mode...`);
  
  const results = [];
  
  for (let i = 0; i < videoQueue.length; i++) {
    const video = videoQueue[i];
    console.log(`\n📹 Processing ${i + 1}/${videoQueue.length}: "${video.title}"`);
    
    try {
      // Navigate to copyright page
      console.log(`Navigating to: ${video.copyrightUrl}`);
      window.location.href = video.copyrightUrl;
      
      // Wait for page load
      await waitForPageLoad();
      
      // Here we would trigger existing ClaimCutter flow
      // For now, we'll simulate the process
      const result = await simulateExistingFlow(video);
      
      results.push({
        ...video,
        success: result.success,
        timestampCount: result.timestampCount,
        cutsApplied: result.cutsApplied,
        error: result.error
      });
      
      console.log(`${result.success ? '✅' : '❌'} Video ${i + 1} completed`);
      
    } catch (error) {
      console.error(`❌ Failed to process video ${i + 1}:`, error);
      results.push({
        ...video,
        success: false,
        error: error.message
      });
    }
    
    // Delay between videos
    await delay(2000);
  }
  
  return results;
}

/**
 * Simulate existing ClaimCutter flow (for testing)
 * In real implementation, this would trigger the actual ClaimCutter process
 */
async function simulateExistingFlow(video) {
  console.log('🔧 Running existing ClaimCutter flow...');
  
  // Simulate timestamp collection
  await delay(1000);
  const timestampCount = Math.floor(Math.random() * 5) + 1;
  
  // Simulate cut application
  await delay(2000);
  const cutsApplied = timestampCount;
  
  return {
    success: true,
    timestampCount,
    cutsApplied
  };
}

/**
 * Wait for page to load
 */
function waitForPageLoad() {
  return new Promise((resolve) => {
    if (document.readyState === 'complete') {
      resolve();
    } else {
      window.addEventListener('load', resolve);
    }
  });
}

/**
 * Show batch processing summary
 */
function showBatchSummary(results) {
  console.log('\n📊 BATCH PROCESSING SUMMARY');
  console.log('=' .repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`Total Videos: ${results.length}`);
  console.log(`Successful: ${successful.length}`);
  console.log(`Failed: ${failed.length}`);
  
  if (successful.length > 0) {
    console.log('\n✅ Successful Videos:');
    successful.forEach((video, i) => {
      console.log(`${i + 1}. "${video.title}" - ${video.cutsApplied} cuts applied`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed Videos:');
    failed.forEach((video, i) => {
      console.log(`${i + 1}. "${video.title}" - ${video.error}`);
    });
  }
  
  const totalCuts = successful.reduce((sum, video) => sum + (video.cutsApplied || 0), 0);
  console.log(`\n🎯 Total Cuts Applied: ${totalCuts}`);
}

/**
 * Utility delay function
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ============================================================================
// INTEGRATION WITH EXISTING CLAIMCUTTER
// ============================================================================

/**
 * Integration point with existing ClaimCutter
 * This would be called from the main extension
 */
function integrateBatchModeWithClaimCutter() {
  console.log('🔗 Integrating Batch Mode with ClaimCutter...');
  
  // This would extend the existing popup to detect batch mode
  // and show appropriate UI for video selection and progress tracking
  
  const batchModeConfig = {
    // Video queue from channel content page
    videoQueue: window.claimCutterBatchQueue || [],
    
    // Current video being processed
    currentVideoIndex: 0,
    
    // Results tracking
    results: [],
    
    // Settings (reuse existing settings)
    settings: {
      turboMode: true, // Use turbo mode for batch processing
      mergeBuffer: 15, // Use existing merge buffer setting
      autoSave: true   // Auto-save for batch efficiency
    }
  };
  
  return batchModeConfig;
}

// ============================================================================
// USAGE INSTRUCTIONS
// ============================================================================

console.log(`
🎯 BATCH MODE USAGE:

1. Navigate to: studio.youtube.com/channel/{CHANNEL_ID}/videos/upload
2. Run: startBatchMode()

This will:
- Discover videos with copyright issues
- Extract video IDs and build processing queue
- Navigate to each video's copyright page
- Use existing ClaimCutter flow for each video
- Provide batch processing summary

For testing individual components:
- isChannelContentPage() - Check page compatibility
- discoverCopyrightVideos() - Find videos to process
- extractVideoInfo(row, index) - Test video info extraction

Integration with ClaimCutter:
- integrateBatchModeWithClaimCutter() - Get batch mode config
`);

// Auto-detect page on load
setTimeout(() => {
  if (isChannelContentPage()) {
    console.log('✅ Channel content page detected - ready for batch mode');
    const videos = discoverCopyrightVideos();
    if (videos.length > 0) {
      console.log(`🎯 Ready to process ${videos.length} videos. Run: startBatchMode()`);
    }
  }
}, 1000);
