/**
 * Integration Test Script for Channel Content Page
 * 
 * This script tests the complete integration flow for the channel content page:
 * 1. Detect videos with copyright issues
 * 2. Extract video IDs and metadata
 * 3. Test modal interaction and timestamp extraction
 * 4. Simulate the complete ClaimCutter workflow
 */

console.log('🔧 ClaimCutter Channel Content Integration Test');

// ============================================================================
// INTEGRATION TEST FUNCTIONS
// ============================================================================

/**
 * Complete integration test that simulates ClaimCutter workflow
 */
async function testCompleteIntegration() {
  console.log('\n🚀 TESTING COMPLETE INTEGRATION WORKFLOW');
  
  try {
    // Step 1: Detect page compatibility
    const pageInfo = await testPageDetection();
    if (!pageInfo.isCompatible) {
      console.error('❌ Page not compatible with ClaimCutter');
      return;
    }
    
    // Step 2: Find videos with copyright issues
    const copyrightVideos = await findCopyrightVideos();
    if (copyrightVideos.length === 0) {
      console.log('✅ No videos with copyright issues found');
      return;
    }
    
    console.log(`📊 Found ${copyrightVideos.length} videos with copyright issues`);
    
    // Step 3: Test timestamp extraction for each video
    const results = [];
    for (let i = 0; i < Math.min(copyrightVideos.length, 3); i++) { // Test first 3 videos
      console.log(`\n🎬 Testing video ${i + 1}: "${copyrightVideos[i].title}"`);
      
      const videoResult = await testVideoTimestampExtraction(copyrightVideos[i]);
      results.push(videoResult);
      
      // Wait between tests to avoid overwhelming the interface
      await delay(2000);
    }
    
    // Step 4: Summarize results
    console.log('\n📋 INTEGRATION TEST SUMMARY:');
    results.forEach((result, i) => {
      console.log(`Video ${i + 1}: ${result.success ? '✅' : '❌'} ${result.timestampCount} timestamps`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    return results;
    
  } catch (error) {
    console.error('❌ Integration test failed:', error);
  }
}

/**
 * Test page detection and compatibility
 */
function testPageDetection() {
  console.log('🔍 Testing page detection...');
  
  const url = window.location.href;
  const isChannelContentPage = url.includes('/channel/') && url.includes('/videos');
  const hasVideoList = document.querySelectorAll('tr, [role="row"]').length > 0;
  
  const pageInfo = {
    url,
    isCompatible: isChannelContentPage && hasVideoList,
    pageType: isChannelContentPage ? 'channel-content' : 'unknown',
    videoRowCount: document.querySelectorAll('tr, [role="row"]').length
  };
  
  console.log('Page Info:', pageInfo);
  return pageInfo;
}

/**
 * Find and analyze videos with copyright issues
 */
function findCopyrightVideos() {
  console.log('🎯 Finding videos with copyright issues...');
  
  const videoRows = document.querySelectorAll('tr, [role="row"]');
  const copyrightVideos = [];
  
  videoRows.forEach((row, index) => {
    // Look for copyright indicators
    const rowText = row.textContent || '';
    const hasCopyright = rowText.includes('Copyright') || 
                        rowText.includes('See details') ||
                        rowText.includes('Take action');
    
    if (!hasCopyright) return;
    
    // Extract video information
    const titleElement = row.querySelector('[data-testid="video-title"], .video-title, h3, h4, a[href*="/video/"]');
    const title = titleElement?.textContent?.trim() || `Video ${index + 1}`;
    
    // Extract video ID
    let videoId = null;
    const videoLinks = row.querySelectorAll('a[href*="/video/"]');
    videoLinks.forEach(link => {
      const href = link.getAttribute('href');
      const match = href?.match(/\/video\/([a-zA-Z0-9_-]{11})/);
      if (match) {
        videoId = match[1];
      }
    });
    
    // Find "See details" button
    const seeDetailsButton = Array.from(row.querySelectorAll('button')).find(btn => 
      btn.textContent?.includes('See details') || 
      btn.getAttribute('aria-label')?.includes('See details')
    );
    
    if (seeDetailsButton) {
      copyrightVideos.push({
        index,
        row,
        title,
        videoId,
        seeDetailsButton,
        editorUrl: videoId ? `https://studio.youtube.com/video/${videoId}/editor` : null
      });
    }
  });
  
  console.log(`Found ${copyrightVideos.length} videos with copyright issues`);
  return copyrightVideos;
}

/**
 * Test timestamp extraction for a specific video
 */
async function testVideoTimestampExtraction(video) {
  console.log(`Testing timestamp extraction for: "${video.title}"`);
  
  try {
    // Click "See details" button
    video.seeDetailsButton.click();
    
    // Wait for modal to appear
    await delay(1500);
    
    // Find the modal
    const modal = document.querySelector('[role="dialog"]') || 
                  document.querySelector('.modal') ||
                  document.querySelector('[data-testid*="modal"]');
    
    if (!modal) {
      throw new Error('Modal not found after clicking "See details"');
    }
    
    // Extract timestamps from modal
    const timestamps = extractTimestampsFromModal(modal);
    
    // Close modal (look for close button)
    const closeButton = modal.querySelector('[aria-label*="close"], [aria-label*="Close"], button[data-testid*="close"]') ||
                       modal.querySelector('button[aria-label="Close"]');
    
    if (closeButton) {
      closeButton.click();
      await delay(500);
    } else {
      // Try pressing Escape
      document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
      await delay(500);
    }
    
    return {
      success: true,
      videoId: video.videoId,
      title: video.title,
      timestampCount: timestamps.length,
      timestamps,
      editorUrl: video.editorUrl
    };
    
  } catch (error) {
    return {
      success: false,
      videoId: video.videoId,
      title: video.title,
      timestampCount: 0,
      timestamps: [],
      error: error.message
    };
  }
}

/**
 * Extract timestamps from modal (reusing existing logic)
 */
function extractTimestampsFromModal(modal) {
  const modalText = modal.textContent || '';
  
  // Use patterns similar to existing ClaimCutter logic
  const patterns = [
    /Content found in\s+(\d{1,2}:\d{2})\s*[-–]\s*(\d{1,2}:\d{2})/gi,
    /Content found in\s+(\d{1,2}:\d{2}:\d{2})\s*[-–]\s*(\d{1,2}:\d{2}:\d{2})/gi,
    /(\d{1,2}:\d{2})\s*[-–]\s*(\d{1,2}:\d{2})/g,
    /(\d{1,2}:\d{2}:\d{2})\s*[-–]\s*(\d{1,2}:\d{2}:\d{2})/g
  ];
  
  const timestamps = [];
  
  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(modalText)) !== null) {
      timestamps.push({
        start: match[1],
        end: match[2],
        fullMatch: match[0]
      });
    }
  });
  
  // Remove duplicates
  const uniqueTimestamps = timestamps.filter((ts, index, self) => 
    index === self.findIndex(t => t.start === ts.start && t.end === ts.end)
  );
  
  console.log(`Extracted ${uniqueTimestamps.length} unique timestamps`);
  return uniqueTimestamps;
}

/**
 * Utility function for delays
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ============================================================================
// BATCH PROCESSING SIMULATION
// ============================================================================

/**
 * Simulate batch processing multiple videos
 */
async function simulateBatchProcessing() {
  console.log('\n🔄 SIMULATING BATCH PROCESSING');
  
  const copyrightVideos = findCopyrightVideos();
  if (copyrightVideos.length === 0) {
    console.log('No videos to process');
    return;
  }
  
  console.log(`Processing ${copyrightVideos.length} videos...`);
  
  const batchResults = [];
  
  for (const video of copyrightVideos) {
    console.log(`\n📹 Processing: "${video.title}"`);
    
    // Extract timestamps
    const result = await testVideoTimestampExtraction(video);
    batchResults.push(result);
    
    if (result.success && result.timestamps.length > 0) {
      console.log(`✅ Found ${result.timestamps.length} timestamps`);
      console.log(`🎬 Editor URL: ${result.editorUrl}`);
      
      // In real implementation, we would:
      // 1. Store timestamps in session storage
      // 2. Navigate to editor URL
      // 3. Apply cuts using existing apply-cuts logic
      
    } else {
      console.log(`❌ Failed to extract timestamps: ${result.error || 'No timestamps found'}`);
    }
    
    // Delay between videos
    await delay(1000);
  }
  
  // Summary
  const successful = batchResults.filter(r => r.success && r.timestamps.length > 0);
  console.log(`\n📊 BATCH PROCESSING SUMMARY:`);
  console.log(`Total videos: ${batchResults.length}`);
  console.log(`Successful: ${successful.length}`);
  console.log(`Failed: ${batchResults.length - successful.length}`);
  
  return batchResults;
}

// ============================================================================
// USAGE INSTRUCTIONS
// ============================================================================

console.log(`
🎯 INTEGRATION TEST USAGE:

1. Navigate to: studio.youtube.com/channel/{CHANNEL_ID}/videos/upload
2. Run: testCompleteIntegration()
3. For batch simulation: simulateBatchProcessing()

Individual functions:
- testPageDetection() - Check page compatibility
- findCopyrightVideos() - Find videos with copyright issues
- testVideoTimestampExtraction(video) - Test single video
- simulateBatchProcessing() - Simulate processing all videos

Example:
const videos = findCopyrightVideos();
const result = await testVideoTimestampExtraction(videos[0]);
`);

// Auto-run basic detection
setTimeout(() => {
  console.log('\n🔍 Auto-running basic page detection...');
  testPageDetection();
}, 1000);
