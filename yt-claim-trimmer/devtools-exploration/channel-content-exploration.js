/**
 * DevTools Exploration Scripts for Channel Content Page
 * 
 * Use these scripts in Chrome DevTools Console to explore the channel content page
 * and understand how to integrate it with ClaimCutter for batch processing.
 * 
 * URL Pattern: https://studio.youtube.com/channel/{CHANNEL_ID}/videos/upload
 */

console.log('🔍 ClaimCutter Channel Content Page Exploration Scripts Loaded');

// ============================================================================
// STEP 1: PAGE ANALYSIS
// ============================================================================

/**
 * Analyze the current page structure and identify video rows with copyright issues
 */
function step1_analyzePage() {
  console.log('\n📊 STEP 1: Analyzing Channel Content Page Structure');
  
  // Check if we're on the right page
  const currentUrl = window.location.href;
  const isChannelContentPage = currentUrl.includes('/channel/') && currentUrl.includes('/videos');
  
  console.log('Current URL:', currentUrl);
  console.log('Is Channel Content Page:', isChannelContentPage);
  
  if (!isChannelContentPage) {
    console.warn('⚠️ Not on channel content page. Navigate to: studio.youtube.com/channel/{CHANNEL_ID}/videos/upload');
    return;
  }
  
  // Find all video rows
  const videoRows = document.querySelectorAll('[data-video-id], tr[data-video-id], .video-row, [role="row"]');
  console.log(`Found ${videoRows.length} potential video rows`);
  
  // Look for copyright-related elements
  const copyrightElements = document.querySelectorAll('[data-testid*="copyright"], [aria-label*="copyright"], .copyright, [title*="copyright"]');
  console.log(`Found ${copyrightElements.length} copyright-related elements`);
  
  // Look for "See details" buttons
  const seeDetailsButtons = document.querySelectorAll('button[aria-label*="See details"], button:contains("See details"), [data-testid*="details"]');
  console.log(`Found ${seeDetailsButtons.length} "See details" buttons`);
  
  // Alternative selector approaches
  const allButtons = document.querySelectorAll('button');
  const detailsButtons = Array.from(allButtons).filter(btn => 
    btn.textContent?.includes('See details') || 
    btn.getAttribute('aria-label')?.includes('See details')
  );
  console.log(`Found ${detailsButtons.length} buttons with "See details" text`);
  
  return {
    videoRows: videoRows.length,
    copyrightElements: copyrightElements.length,
    seeDetailsButtons: seeDetailsButtons.length,
    detailsButtons: detailsButtons.length
  };
}

/**
 * Identify videos with copyright issues specifically
 */
function step2_findCopyrightVideos() {
  console.log('\n🎯 STEP 2: Finding Videos with Copyright Issues');
  
  // Look for rows that contain copyright indicators
  const allRows = document.querySelectorAll('tr, [role="row"], .video-item, .content-row');
  console.log(`Scanning ${allRows.length} rows for copyright indicators...`);
  
  const copyrightVideos = [];
  
  allRows.forEach((row, index) => {
    // Check for copyright-related text or elements within this row
    const copyrightIndicators = [
      'Copyright',
      'copyright',
      'See details',
      'Take action',
      'Monetization',
      'Visibility'
    ];
    
    const rowText = row.textContent || '';
    const hasCopyrightIndicator = copyrightIndicators.some(indicator => 
      rowText.includes(indicator)
    );
    
    // Look for specific copyright-related elements
    const copyrightElements = row.querySelectorAll(
      '[data-testid*="copyright"], [aria-label*="copyright"], .copyright, [title*="copyright"], button[aria-label*="See details"]'
    );
    
    // Look for "See details" buttons specifically
    const seeDetailsButton = row.querySelector('button[aria-label*="See details"], button:contains("See details")') ||
      Array.from(row.querySelectorAll('button')).find(btn => 
        btn.textContent?.includes('See details') || 
        btn.getAttribute('aria-label')?.includes('See details')
      );
    
    if (hasCopyrightIndicator || copyrightElements.length > 0 || seeDetailsButton) {
      // Try to extract video information
      const videoTitle = row.querySelector('[data-testid="video-title"], .video-title, h3, h4')?.textContent?.trim();
      const videoId = row.getAttribute('data-video-id') || 
        row.querySelector('[data-video-id]')?.getAttribute('data-video-id');
      
      copyrightVideos.push({
        index,
        row,
        videoTitle,
        videoId,
        copyrightElements: copyrightElements.length,
        seeDetailsButton,
        rowText: rowText.substring(0, 100) + '...' // First 100 chars for debugging
      });
    }
  });
  
  console.log(`Found ${copyrightVideos.length} videos with potential copyright issues:`);
  copyrightVideos.forEach((video, i) => {
    console.log(`${i + 1}. "${video.videoTitle}" (ID: ${video.videoId})`);
    console.log(`   - Copyright elements: ${video.copyrightElements}`);
    console.log(`   - Has "See details" button: ${!!video.seeDetailsButton}`);
  });
  
  // Store for next steps
  window.claimCutterCopyrightVideos = copyrightVideos;
  
  return copyrightVideos;
}

/**
 * Test clicking "See details" button and analyze the modal
 */
function step3_testSeeDetailsModal(videoIndex = 0) {
  console.log('\n🔍 STEP 3: Testing "See details" Modal');
  
  const copyrightVideos = window.claimCutterCopyrightVideos;
  if (!copyrightVideos || copyrightVideos.length === 0) {
    console.error('❌ No copyright videos found. Run step2_findCopyrightVideos() first.');
    return;
  }
  
  if (videoIndex >= copyrightVideos.length) {
    console.error(`❌ Video index ${videoIndex} out of range. Found ${copyrightVideos.length} videos.`);
    return;
  }
  
  const video = copyrightVideos[videoIndex];
  console.log(`Testing video: "${video.videoTitle}"`);
  
  if (!video.seeDetailsButton) {
    console.error('❌ No "See details" button found for this video.');
    return;
  }
  
  // Click the "See details" button
  console.log('Clicking "See details" button...');
  video.seeDetailsButton.click();
  
  // Wait a moment for modal to appear, then analyze it
  setTimeout(() => {
    console.log('Analyzing modal that appeared...');
    
    // Look for modal elements
    const modals = document.querySelectorAll('[role="dialog"], .modal, .dialog, [data-testid*="modal"]');
    console.log(`Found ${modals.length} modal elements`);
    
    modals.forEach((modal, i) => {
      console.log(`Modal ${i + 1}:`);
      console.log('- Text content preview:', modal.textContent?.substring(0, 200) + '...');
      console.log('- Classes:', modal.className);
      console.log('- Data attributes:', Array.from(modal.attributes).filter(attr => attr.name.startsWith('data-')));
    });
    
    // Look for timestamp patterns in the modal
    const modalText = modals[0]?.textContent || '';
    const timestampPatterns = [
      /\d{1,2}:\d{2}/g, // MM:SS
      /\d{1,2}:\d{2}:\d{2}/g, // HH:MM:SS
      /\d{1,2}:\d{2}\s*[-–]\s*\d{1,2}:\d{2}/g, // MM:SS - MM:SS
      /\d{1,2}:\d{2}:\d{2}\s*[-–]\s*\d{1,2}:\d{2}:\d{2}/g // HH:MM:SS - HH:MM:SS
    ];
    
    console.log('Looking for timestamp patterns in modal...');
    timestampPatterns.forEach((pattern, i) => {
      const matches = modalText.match(pattern);
      if (matches) {
        console.log(`Pattern ${i + 1} matches:`, matches);
      }
    });
    
    // Store modal reference for further analysis
    window.claimCutterCurrentModal = modals[0];
    
  }, 1000);
}

/**
 * Extract timestamps from the currently open modal
 */
function step4_extractTimestampsFromModal() {
  console.log('\n⏰ STEP 4: Extracting Timestamps from Modal');
  
  const modal = window.claimCutterCurrentModal || document.querySelector('[role="dialog"]');
  if (!modal) {
    console.error('❌ No modal found. Run step3_testSeeDetailsModal() first.');
    return;
  }
  
  console.log('Analyzing modal for timestamp extraction...');
  
  // Use similar logic to our existing timestamp extraction
  const modalText = modal.textContent || '';
  console.log('Modal text length:', modalText.length);
  
  // Look for various timestamp patterns
  const patterns = [
    /(\d{1,2}:\d{2})\s*[-–]\s*(\d{1,2}:\d{2})/g,
    /(\d{1,2}:\d{2}:\d{2})\s*[-–]\s*(\d{1,2}:\d{2}:\d{2})/g,
    /Content found in\s+(\d{1,2}:\d{2})\s*[-–]\s*(\d{1,2}:\d{2})/gi,
    /Content found in\s+(\d{1,2}:\d{2}:\d{2})\s*[-–]\s*(\d{1,2}:\d{2}:\d{2})/gi
  ];
  
  const extractedTimestamps = [];
  
  patterns.forEach((pattern, patternIndex) => {
    let match;
    while ((match = pattern.exec(modalText)) !== null) {
      extractedTimestamps.push({
        pattern: patternIndex,
        start: match[1],
        end: match[2],
        fullMatch: match[0]
      });
    }
  });
  
  console.log(`Found ${extractedTimestamps.length} timestamp ranges:`);
  extractedTimestamps.forEach((ts, i) => {
    console.log(`${i + 1}. ${ts.start} - ${ts.end} (pattern ${ts.pattern})`);
  });
  
  return extractedTimestamps;
}

/**
 * Test video ID extraction and editor URL construction
 */
function step5_testVideoIdExtraction() {
  console.log('\n🎬 STEP 5: Testing Video ID Extraction and Editor URL Construction');
  
  const copyrightVideos = window.claimCutterCopyrightVideos;
  if (!copyrightVideos || copyrightVideos.length === 0) {
    console.error('❌ No copyright videos found. Run step2_findCopyrightVideos() first.');
    return;
  }
  
  copyrightVideos.forEach((video, i) => {
    console.log(`\nVideo ${i + 1}: "${video.videoTitle}"`);
    
    // Try multiple methods to extract video ID
    let videoId = video.videoId;
    
    if (!videoId) {
      // Look for video ID in various places within the row
      const row = video.row;
      
      // Check data attributes
      videoId = row.getAttribute('data-video-id') ||
        row.querySelector('[data-video-id]')?.getAttribute('data-video-id');
      
      // Check for YouTube video ID pattern in href attributes
      const links = row.querySelectorAll('a[href*="/video/"]');
      links.forEach(link => {
        const href = link.getAttribute('href');
        const match = href?.match(/\/video\/([a-zA-Z0-9_-]{11})/);
        if (match) {
          videoId = match[1];
        }
      });
      
      // Check for video ID in onclick handlers or other attributes
      const allElements = row.querySelectorAll('*');
      allElements.forEach(el => {
        Array.from(el.attributes).forEach(attr => {
          if (attr.value.includes('video/') || attr.value.match(/[a-zA-Z0-9_-]{11}/)) {
            const match = attr.value.match(/([a-zA-Z0-9_-]{11})/);
            if (match && !videoId) {
              videoId = match[1];
            }
          }
        });
      });
    }
    
    console.log(`   Video ID: ${videoId || 'NOT FOUND'}`);
    
    if (videoId) {
      const editorUrl = `https://studio.youtube.com/video/${videoId}/editor`;
      console.log(`   Editor URL: ${editorUrl}`);
    }
  });
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Run all exploration steps in sequence
 */
function runAllSteps() {
  console.log('🚀 Running all exploration steps...');
  
  step1_analyzePage();
  
  setTimeout(() => {
    const videos = step2_findCopyrightVideos();
    if (videos.length > 0) {
      setTimeout(() => {
        step5_testVideoIdExtraction();
        console.log('\n✅ All steps completed. To test modal interaction, run: step3_testSeeDetailsModal(0)');
      }, 1000);
    }
  }, 1000);
}

/**
 * Quick test of modal interaction
 */
function quickModalTest() {
  const videos = step2_findCopyrightVideos();
  if (videos.length > 0) {
    step3_testSeeDetailsModal(0);
    setTimeout(() => {
      step4_extractTimestampsFromModal();
    }, 2000);
  }
}

// ============================================================================
// USAGE INSTRUCTIONS
// ============================================================================

console.log(`
🎯 USAGE INSTRUCTIONS:

1. Navigate to: studio.youtube.com/channel/{CHANNEL_ID}/videos/upload
2. Run: runAllSteps()
3. To test modal interaction: quickModalTest()

Individual steps:
- step1_analyzePage() - Analyze page structure
- step2_findCopyrightVideos() - Find videos with copyright issues
- step3_testSeeDetailsModal(0) - Test clicking "See details" for first video
- step4_extractTimestampsFromModal() - Extract timestamps from open modal
- step5_testVideoIdExtraction() - Test video ID extraction

Results are stored in:
- window.claimCutterCopyrightVideos
- window.claimCutterCurrentModal
`);
