/**
 * End-to-end tests for YouTube Studio integration
 * 
 * These tests validate the extension's interaction with real YouTube Studio pages
 * and ensure selector reliability across different scenarios.
 */

import { test, expect, chromium, BrowserContext, Page } from '@playwright/test';
import path from 'path';

// Extension path
const extensionPath = path.join(__dirname, '../../dist');

let context: BrowserContext;
let page: Page;

test.beforeAll(async () => {
  // Launch browser with extension loaded
  context = await chromium.launchPersistentContext('', {
    headless: false, // Run in headed mode to see the extension
    args: [
      `--disable-extensions-except=${extensionPath}`,
      `--load-extension=${extensionPath}`,
      '--no-sandbox',
      '--disable-dev-shm-usage'
    ],
  });
  
  page = await context.newPage();
});

test.afterAll(async () => {
  await context.close();
});

test.describe('YouTube Studio Selector Validation', () => {
  test.skip('should find copyright page elements', async () => {
    // Note: This test is skipped because it requires actual YouTube Studio access
    // and copyright claims to be present. It's provided as a template for manual testing.
    
    // Navigate to YouTube Studio (requires authentication)
    await page.goto('https://studio.youtube.com');
    
    // Wait for login if needed
    await page.waitForTimeout(5000);
    
    // Navigate to a video with copyright claims
    // This would need to be a real video ID with claims
    const videoId = 'YOUR_VIDEO_ID_HERE';
    await page.goto(`https://studio.youtube.com/video/${videoId}/copyright`);
    
    // Validate copyright page selectors
    const claimsTable = page.locator('[role="table"]');
    await expect(claimsTable).toBeVisible({ timeout: 10000 });
    
    const claimRows = page.locator('[role="row"]:not([role="columnheader"])');
    const rowCount = await claimRows.count();
    expect(rowCount).toBeGreaterThan(0);
    
    // Test "See details" button selector
    const seeDetailsButtons = page.locator('[aria-label*="See details"], [aria-label*="View details"]');
    const buttonCount = await seeDetailsButtons.count();
    expect(buttonCount).toBeGreaterThan(0);
  });

  test.skip('should find editor page elements', async () => {
    // Note: This test is skipped for the same reasons as above
    
    const videoId = 'YOUR_VIDEO_ID_HERE';
    await page.goto(`https://studio.youtube.com/video/${videoId}/editor`);
    
    // Validate editor page selectors
    const timelineTimecode = page.locator('[aria-label*="Timeline timecode"], [aria-label*="Current time"]');
    await expect(timelineTimecode).toBeVisible({ timeout: 10000 });
    
    const newCutButton = page.locator('[aria-label*="New cut"], [aria-label*="Add cut"]');
    await expect(newCutButton).toBeVisible();
    
    const saveButton = page.locator('[aria-label*="Save"], [aria-label*="Save changes"]');
    await expect(saveButton).toBeVisible();
  });
});

test.describe('Extension Loading and Basic Functionality', () => {
  test('should load extension without errors', async () => {
    // Check that extension is loaded
    const extensions = await context.backgroundPages();
    expect(extensions.length).toBeGreaterThan(0);
  });

  test('should have extension popup available', async () => {
    // Navigate to any page to test extension popup
    await page.goto('https://example.com');
    
    // The extension popup should be accessible (though we can't easily test the UI)
    // This test mainly ensures the extension loaded without critical errors
    
    // Check console for extension errors
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error' && msg.text().includes('claimcutter')) {
        logs.push(msg.text());
      }
    });
    
    await page.waitForTimeout(2000);
    expect(logs.length).toBe(0);
  });
});

test.describe('Selector Fallback Testing', () => {
  test('should handle missing primary selectors gracefully', async () => {
    // Create a test page with fallback selectors
    await page.setContent(`
      <html>
        <body>
          <!-- Missing primary selector, but has fallback -->
          <div class="ytcp-copyright-table-row">Test Row</div>
          <button class="ytcp-copyright-details-button">See details</button>
          
          <!-- Test script injection -->
          <script>
            window.testSelectorFallback = true;
          </script>
        </body>
      </html>
    `);
    
    // Inject our selector utility to test fallback behavior
    await page.addScriptTag({
      content: `
        // Simplified version of our selector utility for testing
        function findElement(selectorConfig) {
          // Try primary selector first
          let element = document.querySelector(selectorConfig.primary);
          if (element) return element;
          
          // Try fallback selectors
          for (const fallback of selectorConfig.fallbacks) {
            element = document.querySelector(fallback);
            if (element) return element;
          }
          
          return null;
        }
        
        // Test copyright row selector
        const rowSelector = {
          primary: '[role="row"]:not([role="columnheader"])',
          fallbacks: ['.ytcp-copyright-table-row']
        };
        
        const row = findElement(rowSelector);
        window.testRowFound = !!row;
        
        // Test button selector
        const buttonSelector = {
          primary: '[aria-label*="See details"]',
          fallbacks: ['.ytcp-copyright-details-button']
        };
        
        const button = findElement(buttonSelector);
        window.testButtonFound = !!button;
      `
    });
    
    // Verify fallback selectors worked
    const rowFound = await page.evaluate(() => window.testRowFound);
    const buttonFound = await page.evaluate(() => window.testButtonFound);
    
    expect(rowFound).toBe(true);
    expect(buttonFound).toBe(true);
  });
});

test.describe('Timestamp Parsing Validation', () => {
  test('should parse various timestamp formats correctly', async () => {
    await page.setContent('<html><body></body></html>');
    
    // Inject timestamp parsing logic for testing
    await page.addScriptTag({
      content: `
        // Simplified timestamp parsing for testing
        function parseTimestamp(text) {
          const patterns = [
            /Content found in\\s+(\\d{1,2}:\\d{2}(?::\\d{2})?)\\s*[–—-]\\s*(\\d{1,2}:\\d{2}(?::\\d{2})?)/i,
            /(\\d{1,2}:\\d{2}(?::\\d{2})?)\\s*[–—\\-~]\\s*(\\d{1,2}:\\d{2}(?::\\d{2})?)/
          ];
          
          for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match) {
              return { start: match[1], end: match[2] };
            }
          }
          return null;
        }
        
        // Test various formats
        const testCases = [
          'Content found in 1:23 – 2:45',
          'Content found in 01:23:45 – 02:30:15',
          '5:00 - 6:30',
          '1:00 ~ 2:00'
        ];
        
        window.parseResults = testCases.map(test => ({
          input: test,
          result: parseTimestamp(test)
        }));
      `
    });
    
    const results = await page.evaluate(() => window.parseResults);
    
    // Verify all test cases parsed successfully
    for (const result of results) {
      expect(result.result).not.toBeNull();
      expect(result.result.start).toBeDefined();
      expect(result.result.end).toBeDefined();
    }
  });
});

test.describe('Modal UI Testing', () => {
  test('should create modal with shadow DOM', async () => {
    await page.setContent('<html><body></body></html>');
    
    // Inject modal creation logic
    await page.addScriptTag({
      content: `
        function createTestModal() {
          const modalContainer = document.createElement('div');
          modalContainer.id = 'claimcutter-modal';
          
          const shadow = modalContainer.attachShadow({ mode: 'open' });
          shadow.innerHTML = '<div class="modal-content">Test Modal</div>';
          
          document.body.appendChild(modalContainer);
          return modalContainer;
        }
        
        window.testModal = createTestModal();
      `
    });
    
    // Verify modal was created
    const modalExists = await page.locator('#claimcutter-modal').count();
    expect(modalExists).toBe(1);
    
    // Verify shadow DOM content
    const shadowContent = await page.evaluate(() => {
      const modal = document.getElementById('claimcutter-modal');
      return modal.shadowRoot.querySelector('.modal-content').textContent;
    });
    
    expect(shadowContent).toBe('Test Modal');
  });
});

// Helper function for manual testing
test.describe('Manual Testing Helpers', () => {
  test.skip('manual test - extension on real YouTube Studio', async () => {
    // This test is for manual execution only
    // It opens YouTube Studio for manual testing of the extension
    
    console.log('Opening YouTube Studio for manual testing...');
    console.log('1. Navigate to a video with copyright claims');
    console.log('2. Go to the Copyright tab');
    console.log('3. Click the ClaimCutter extension icon');
    console.log('4. Test the functionality manually');
    
    await page.goto('https://studio.youtube.com');
    
    // Wait for manual testing
    await page.waitForTimeout(60000); // 1 minute for manual testing
  });
});
