/**
 * Unit tests for delay utilities
 */

import {
  delay,
  randomDelay,
  shortDelay,
  mediumDelay,
  longDelay,
  actionDelay,
  typingDelay,
  progressiveDelay,
  exponentialBackoff,
  waitForCondition,
  readingDelay,
  decisionDelay,
  batchDelay
} from '../../src/utils/delays';

// Mock setTimeout for testing
jest.useFakeTimers();

describe('delay', () => {
  test('resolves after specified milliseconds', async () => {
    const promise = delay(1000);
    
    // Fast-forward time
    jest.advanceTimersByTime(1000);
    
    await expect(promise).resolves.toBeUndefined();
  });

  test('does not resolve before specified time', async () => {
    const promise = delay(1000);
    
    // Advance time by less than delay
    jest.advanceTimersByTime(500);
    
    // Promise should still be pending
    expect(promise).toEqual(expect.any(Promise));
  });
});

describe('randomDelay', () => {
  test('uses default range when no parameters provided', async () => {
    const promise = randomDelay();
    
    // Should resolve within default range (300-700ms)
    jest.advanceTimersByTime(700);
    
    await expect(promise).resolves.toBeUndefined();
  });

  test('respects custom min and max values', async () => {
    const promise = randomDelay(100, 200);
    
    // Should resolve within custom range
    jest.advanceTimersByTime(200);
    
    await expect(promise).resolves.toBeUndefined();
  });
});

describe('preset delay functions', () => {
  test('shortDelay uses correct range', async () => {
    const promise = shortDelay();
    jest.advanceTimersByTime(300);
    await expect(promise).resolves.toBeUndefined();
  });

  test('mediumDelay uses correct range', async () => {
    const promise = mediumDelay();
    jest.advanceTimersByTime(700);
    await expect(promise).resolves.toBeUndefined();
  });

  test('longDelay uses correct range', async () => {
    const promise = longDelay();
    jest.advanceTimersByTime(1200);
    await expect(promise).resolves.toBeUndefined();
  });
});

describe('actionDelay', () => {
  test('returns appropriate delay for click action', async () => {
    const promise = actionDelay('click');
    jest.advanceTimersByTime(500);
    await expect(promise).resolves.toBeUndefined();
  });

  test('returns appropriate delay for type action', async () => {
    const promise = actionDelay('type');
    jest.advanceTimersByTime(300);
    await expect(promise).resolves.toBeUndefined();
  });

  test('returns appropriate delay for navigate action', async () => {
    const promise = actionDelay('navigate');
    jest.advanceTimersByTime(2000);
    await expect(promise).resolves.toBeUndefined();
  });

  test('returns appropriate delay for wait action', async () => {
    const promise = actionDelay('wait');
    jest.advanceTimersByTime(1000);
    await expect(promise).resolves.toBeUndefined();
  });
});

describe('typingDelay', () => {
  test('calculates delay based on text length', async () => {
    const shortText = 'hi';
    const longText = 'this is a much longer text string';

    const shortPromise = typingDelay(shortText);
    const longPromise = typingDelay(longText);

    // Advance time enough for both to complete
    jest.advanceTimersByTime(1000);

    await expect(shortPromise).resolves.toBeUndefined();
    await expect(longPromise).resolves.toBeUndefined();
  });
});

describe('progressiveDelay', () => {
  test('increases delay with each attempt', async () => {
    const attempt1 = progressiveDelay(1, 100);
    const attempt2 = progressiveDelay(2, 100);
    const attempt3 = progressiveDelay(3, 100);
    
    // First attempt should be base delay
    jest.advanceTimersByTime(100);
    await expect(attempt1).resolves.toBeUndefined();
    
    // Second attempt should be longer
    jest.advanceTimersByTime(150);
    await expect(attempt2).resolves.toBeUndefined();
    
    // Third attempt should be even longer
    jest.advanceTimersByTime(225);
    await expect(attempt3).resolves.toBeUndefined();
  });

  test('caps delay at maximum value', async () => {
    const highAttempt = progressiveDelay(10, 1000);
    
    // Should cap at 5000ms regardless of calculation
    jest.advanceTimersByTime(5000);
    await expect(highAttempt).resolves.toBeUndefined();
  });
});

describe('exponentialBackoff', () => {
  test('implements exponential backoff with jitter', async () => {
    const attempt1 = exponentialBackoff(1, 100);
    const attempt2 = exponentialBackoff(2, 100);
    
    // First attempt
    jest.advanceTimersByTime(110); // Base + some jitter
    await expect(attempt1).resolves.toBeUndefined();
    
    // Second attempt should be roughly double
    jest.advanceTimersByTime(220); // 200 + some jitter
    await expect(attempt2).resolves.toBeUndefined();
  });

  test('respects maximum delay', async () => {
    const highAttempt = exponentialBackoff(10, 1000, 2000);
    
    // Should cap at maxMs
    jest.advanceTimersByTime(2000);
    await expect(highAttempt).resolves.toBeUndefined();
  });
});

describe('waitForCondition', () => {
  beforeEach(() => {
    jest.useRealTimers(); // Use real timers for these tests
  });

  afterEach(() => {
    jest.useFakeTimers(); // Restore fake timers
  });

  test('resolves when condition becomes true', async () => {
    let conditionMet = false;

    // Make condition true after a short delay
    setTimeout(() => {
      conditionMet = true;
    }, 50);

    const result = await waitForCondition(() => conditionMet, 1000, 10);
    expect(result).toBe(true);
  });

  test('times out when condition never becomes true', async () => {
    const result = await waitForCondition(() => false, 100, 10);
    expect(result).toBe(false);
  });

  test('handles async conditions', async () => {
    let conditionMet = false;

    const asyncCondition = async () => {
      return Promise.resolve(conditionMet);
    };

    // Make condition true after a short delay
    setTimeout(() => {
      conditionMet = true;
    }, 50);

    const result = await waitForCondition(asyncCondition, 1000, 10);
    expect(result).toBe(true);
  });
});

describe('readingDelay', () => {
  test('calculates delay based on text length and reading speed', async () => {
    const shortText = 'Hello world';
    const longText = 'This is a much longer text that should take more time to read and process';

    const shortPromise = readingDelay(shortText, 200);
    const longPromise = readingDelay(longText, 200);

    // Advance time enough for both to complete
    jest.advanceTimersByTime(5000);

    await expect(shortPromise).resolves.toBeUndefined();
    await expect(longPromise).resolves.toBeUndefined();
  });

  test('enforces minimum delay', async () => {
    const emptyText = '';
    const promise = readingDelay(emptyText);

    // Should still have minimum 500ms delay
    jest.advanceTimersByTime(500);
    await expect(promise).resolves.toBeUndefined();
  });
});

describe('decisionDelay', () => {
  test('provides human-like decision time', async () => {
    const promise = decisionDelay();
    
    // Should resolve within human decision range (1-3 seconds)
    jest.advanceTimersByTime(3000);
    await expect(promise).resolves.toBeUndefined();
  });
});

describe('batchDelay', () => {
  test('provides longer delays for first and last items', async () => {
    const firstItem = batchDelay(0, 5);
    const middleItem = batchDelay(2, 5);
    const lastItem = batchDelay(4, 5);
    
    // All should resolve within reasonable time
    jest.advanceTimersByTime(1200);
    
    await expect(firstItem).resolves.toBeUndefined();
    await expect(middleItem).resolves.toBeUndefined();
    await expect(lastItem).resolves.toBeUndefined();
  });
});
