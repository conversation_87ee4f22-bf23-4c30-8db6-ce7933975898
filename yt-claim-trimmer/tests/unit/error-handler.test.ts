/**
 * Unit tests for error handling utilities
 */

import {
  createError,
  formatErrorForUser,
  getRecoveryActions,
  logError,
  reportError,
  handleChromeError,
  withErrorHandling,
  retryWithBackoff
} from '../../src/utils/error-handler';

// Mock Chrome APIs
const mockChrome = {
  runtime: {
    sendMessage: jest.fn(),
    lastError: null as any,
  },
};
(global as any).chrome = mockChrome;

// Mock console methods
const mockConsole = {
  warn: jest.fn(),
  error: jest.fn(),
  log: jest.fn(),
};
global.console = { ...console, ...mockConsole };

describe('createError', () => {
  test('creates error with all properties', () => {
    const error = createError(
      'INVALID_PAGE',
      'Test error message',
      'Test details',
      'Test context'
    );

    expect(error.code).toBe('INVALID_PAGE');
    expect(error.message).toBe('Test error message');
    expect(error.details).toBe('Test details');
    expect(error.context).toBe('Test context');
    expect(error.timestamp).toBeGreaterThan(0);
    expect(error.recoverable).toBe(true);
    expect(error.userMessage).toContain('YouTube Studio');
  });

  test('creates error with minimal properties', () => {
    const error = createError('EXTENSION_ERROR', 'Test error');

    expect(error.code).toBe('EXTENSION_ERROR');
    expect(error.message).toBe('Test error');
    expect(error.details).toBeUndefined();
    expect(error.context).toBeUndefined();
    expect(error.recoverable).toBe(false);
  });

  test('provides appropriate user messages for different error codes', () => {
    const invalidPageError = createError('INVALID_PAGE', 'Test');
    expect(invalidPageError.userMessage).toContain('YouTube Studio');
    expect(invalidPageError.recoverable).toBe(true);

    const sessionExpiredError = createError('SESSION_EXPIRED', 'Test');
    expect(sessionExpiredError.userMessage).toContain('session has expired');
    expect(sessionExpiredError.recoverable).toBe(true);

    const unknownError = createError('EXTENSION_ERROR', 'Test');
    expect(unknownError.userMessage).toContain('unexpected error');
    expect(unknownError.recoverable).toBe(false);
  });
});

describe('formatErrorForUser', () => {
  test('formats error message for user display', () => {
    const error = createError(
      'INVALID_PAGE',
      'Internal error',
      'Stack trace here'
    );

    const formatted = formatErrorForUser(error);
    expect(formatted).toContain('YouTube Studio');
    expect(formatted).toContain('Technical details');
    expect(formatted).toContain('Stack trace here');
  });

  test('excludes technical details for user cancelled errors', () => {
    const error = createError('USER_CANCELLED', 'Operation cancelled');
    const formatted = formatErrorForUser(error);
    
    expect(formatted).not.toContain('Technical details');
    expect(formatted).toBe(error.userMessage);
  });
});

describe('getRecoveryActions', () => {
  test('returns recovery actions for recoverable errors', () => {
    const error = createError('INVALID_PAGE', 'Test error');
    const actions = getRecoveryActions(error);

    expect(actions).toHaveLength(1);
    expect(actions[0].label).toBe('Open YouTube Studio');
    expect(actions[0].primary).toBe(true);
    expect(typeof actions[0].action).toBe('function');
  });

  test('returns empty array for errors without actions', () => {
    const error = createError('USER_CANCELLED', 'Test error');
    const actions = getRecoveryActions(error);

    expect(actions).toHaveLength(0);
  });
});

describe('logError', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('logs recoverable errors as warnings', () => {
    const error = createError('INVALID_PAGE', 'Test error');
    logError(error);

    expect(mockConsole.warn).toHaveBeenCalledWith(
      '[ClaimCutter] Recoverable error:',
      expect.objectContaining({
        code: 'INVALID_PAGE',
        message: 'Test error',
      })
    );
  });

  test('logs non-recoverable errors as errors', () => {
    const error = createError('EXTENSION_ERROR', 'Test error');
    logError(error);

    expect(mockConsole.error).toHaveBeenCalledWith(
      '[ClaimCutter] Critical error:',
      expect.objectContaining({
        code: 'EXTENSION_ERROR',
        message: 'Test error',
      })
    );
  });
});

describe('reportError', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('logs error and sends to background script', () => {
    const error = createError('INVALID_PAGE', 'Test error');
    reportError(error);

    expect(mockConsole.warn).toHaveBeenCalled();
    expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
      action: 'ERROR_REPORT',
      error: expect.objectContaining({
        code: 'INVALID_PAGE',
        message: 'Test error',
      }),
    });
  });

  test('handles Chrome API errors gracefully', () => {
    mockChrome.runtime.sendMessage.mockImplementation(() => {
      throw new Error('Chrome API error');
    });

    const error = createError('INVALID_PAGE', 'Test error');
    
    expect(() => reportError(error)).not.toThrow();
    expect(mockConsole.error).toHaveBeenCalledWith(
      '[ClaimCutter] Failed to report error to background:',
      expect.any(Error)
    );
  });
});

describe('handleChromeError', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockChrome.runtime.lastError = null;
  });

  test('returns null when no Chrome error', () => {
    const result = handleChromeError('test context');
    expect(result).toBeNull();
  });

  test('creates and reports error when Chrome error exists', () => {
    mockChrome.runtime.lastError = { message: 'Chrome error message' };
    
    const result = handleChromeError('test context');
    
    expect(result).not.toBeNull();
    expect(result?.code).toBe('EXTENSION_ERROR');
    expect(result?.message).toBe('Chrome error message');
    expect(result?.context).toBe('test context');
    expect(mockChrome.runtime.sendMessage).toHaveBeenCalled();
  });
});

describe('withErrorHandling', () => {
  test('wraps function with error handling', async () => {
    const mockFn = jest.fn().mockResolvedValue('success');
    const wrappedFn = withErrorHandling(mockFn, 'test context');

    const result = await wrappedFn('arg1', 'arg2');

    expect(result).toBe('success');
    expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
  });

  test('catches and reports errors', async () => {
    const mockFn = jest.fn().mockRejectedValue(new Error('Test error'));
    const wrappedFn = withErrorHandling(mockFn, 'test context');

    await expect(wrappedFn()).rejects.toMatchObject({
      code: 'EXTENSION_ERROR',
      message: 'Test error',
      context: 'test context',
    });

    expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
      action: 'ERROR_REPORT',
      error: expect.objectContaining({
        code: 'EXTENSION_ERROR',
        message: 'Test error',
        context: 'test context',
      }),
    });
  });
});

describe('retryWithBackoff', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('succeeds on first attempt', async () => {
    const mockFn = jest.fn().mockResolvedValue('success');

    const result = await retryWithBackoff(mockFn, 3, 10, 'test operation');

    expect(result).toBe('success');
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  test('retries on failure and eventually succeeds', async () => {
    const mockFn = jest.fn()
      .mockRejectedValueOnce(new Error('First failure'))
      .mockResolvedValue('success');

    const result = await retryWithBackoff(mockFn, 3, 10, 'test operation');

    expect(result).toBe('success');
    expect(mockFn).toHaveBeenCalledTimes(2);
  });

  test('fails after max attempts', async () => {
    const mockFn = jest.fn().mockRejectedValue(new Error('Persistent failure'));

    await expect(retryWithBackoff(mockFn, 2, 10, 'test operation')).rejects.toMatchObject({
      code: 'EXTENSION_ERROR',
      message: 'Persistent failure',
    });

    expect(mockFn).toHaveBeenCalledTimes(2);
    expect(mockChrome.runtime.sendMessage).toHaveBeenCalled();
  });
});
