/**
 * Unit tests for timestamp parsing utilities
 */

import {
  timestampToSeconds,
  secondsToTimestamp,
  parseTimestampPair,
  parseMultipleTimestamps,
  validateTimestampPair,
  extractTimestampsFromElement
} from '../../src/utils/timestamp-parser';

describe('timestampToSeconds', () => {
  test('converts MM:SS format correctly', () => {
    expect(timestampToSeconds('1:23')).toBe(83);
    expect(timestampToSeconds('0:30')).toBe(30);
    expect(timestampToSeconds('10:00')).toBe(600);
  });

  test('converts HH:MM:SS format correctly', () => {
    expect(timestampToSeconds('1:23:45')).toBe(5025);
    expect(timestampToSeconds('0:01:30')).toBe(90);
    expect(timestampToSeconds('2:00:00')).toBe(7200);
  });

  test('throws error for invalid format', () => {
    expect(() => timestampToSeconds('invalid')).toThrow();
    expect(() => timestampToSeconds('1:2:3:4')).toThrow();
    expect(() => timestampToSeconds('')).toThrow();
  });
});

describe('secondsToTimestamp', () => {
  test('converts to MM:SS format by default', () => {
    expect(secondsToTimestamp(83)).toBe('01:23');
    expect(secondsToTimestamp(30)).toBe('00:30');
    expect(secondsToTimestamp(600)).toBe('10:00');
  });

  test('converts to HH:MM:SS format when includeHours is true', () => {
    expect(secondsToTimestamp(5025, true)).toBe('01:23:45');
    expect(secondsToTimestamp(90, true)).toBe('00:01:30');
    expect(secondsToTimestamp(7200, true)).toBe('02:00:00');
  });

  test('automatically includes hours for large values', () => {
    expect(secondsToTimestamp(3661)).toBe('01:01:01');
    expect(secondsToTimestamp(7200)).toBe('02:00:00');
  });
});

describe('parseTimestampPair', () => {
  test('parses "Content found in" format', () => {
    const result = parseTimestampPair('Content found in 1:23 – 2:45');
    expect(result).toEqual({
      start: 83,
      end: 165,
      raw: 'Content found in 1:23 – 2:45'
    });
  });

  test('parses "Claimed content" format', () => {
    const result = parseTimestampPair('Claimed content: 0:30 - 1:15');
    expect(result).toEqual({
      start: 30,
      end: 75,
      raw: 'Claimed content: 0:30 - 1:15'
    });
  });

  test('parses "From X to Y" format', () => {
    const result = parseTimestampPair('From 2:00 to 3:30');
    expect(result).toEqual({
      start: 120,
      end: 210,
      raw: 'From 2:00 to 3:30'
    });
  });

  test('parses generic timestamp range', () => {
    const result = parseTimestampPair('5:00 – 6:30');
    expect(result).toEqual({
      start: 300,
      end: 390,
      raw: '5:00 – 6:30'
    });
  });

  test('parses parenthetical format', () => {
    const result = parseTimestampPair('Some text (1:00 - 2:00) more text');
    expect(result).toEqual({
      start: 60,
      end: 120,
      raw: 'Some text (1:00 - 2:00) more text'
    });
  });

  test('handles HH:MM:SS format', () => {
    const result = parseTimestampPair('Content found in 1:23:45 – 2:30:15');
    expect(result).toEqual({
      start: 5025,
      end: 9015,
      raw: 'Content found in 1:23:45 – 2:30:15'
    });
  });

  test('returns null for invalid timestamps', () => {
    expect(parseTimestampPair('No timestamps here')).toBeNull();
    expect(parseTimestampPair('2:00 - 1:00')).toBeNull(); // end before start
    expect(parseTimestampPair('')).toBeNull();
  });

  test('handles different separators', () => {
    expect(parseTimestampPair('1:00 – 2:00')).toBeTruthy(); // em dash
    expect(parseTimestampPair('1:00 — 2:00')).toBeTruthy(); // em dash
    expect(parseTimestampPair('1:00 - 2:00')).toBeTruthy(); // hyphen
    expect(parseTimestampPair('1:00 ~ 2:00')).toBeTruthy(); // tilde
  });
});

describe('parseMultipleTimestamps', () => {
  test('parses multiple timestamp pairs separated by commas', () => {
    const result = parseMultipleTimestamps('1:00 - 2:00, 3:00 - 4:00');
    expect(result).toHaveLength(2);
    expect(result[0]).toEqual({ start: 60, end: 120, raw: '1:00 - 2:00' });
    expect(result[1]).toEqual({ start: 180, end: 240, raw: '3:00 - 4:00' });
  });

  test('parses multiple timestamp pairs separated by "and"', () => {
    const result = parseMultipleTimestamps('1:00 - 2:00 and 3:00 - 4:00');
    expect(result).toHaveLength(2);
  });

  test('returns empty array for no valid timestamps', () => {
    const result = parseMultipleTimestamps('No timestamps here');
    expect(result).toHaveLength(0);
  });
});

describe('validateTimestampPair', () => {
  test('validates correct timestamp pairs', () => {
    expect(validateTimestampPair({ start: 60, end: 120 })).toBe(true);
    expect(validateTimestampPair({ start: 0, end: 30 })).toBe(true);
  });

  test('rejects invalid timestamp pairs', () => {
    expect(validateTimestampPair({ start: -1, end: 60 })).toBe(false); // negative start
    expect(validateTimestampPair({ start: 60, end: -1 })).toBe(false); // negative end
    expect(validateTimestampPair({ start: 120, end: 60 })).toBe(false); // end before start
    expect(validateTimestampPair({ start: 60, end: 60 })).toBe(false); // same start and end
  });

  test('rejects unreasonably long timestamps', () => {
    const veryLong = 25 * 3600; // 25 hours
    expect(validateTimestampPair({ start: 0, end: veryLong })).toBe(false);
    expect(validateTimestampPair({ start: veryLong, end: veryLong + 60 })).toBe(false);
  });

  test('rejects unreasonably long durations', () => {
    const longDuration = 13 * 3600; // 13 hours
    expect(validateTimestampPair({ start: 0, end: longDuration })).toBe(false);
  });
});

describe('extractTimestampsFromElement', () => {
  test('extracts timestamps from DOM element', () => {
    // Create a mock DOM element
    const element = document.createElement('div');
    element.textContent = 'Content found in 1:23 – 2:45';

    const result = extractTimestampsFromElement(element);
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({
      start: 83,
      end: 165,
      raw: 'Content found in 1:23 – 2:45'
    });
  });

  test('returns empty array for element with no timestamps', () => {
    const element = document.createElement('div');
    element.textContent = 'No timestamps here';

    const result = extractTimestampsFromElement(element);
    expect(result).toHaveLength(0);
  });

  test('extracts multiple timestamps from element', () => {
    const element = document.createElement('div');
    element.textContent = 'Content found in 1:00 - 2:00, 3:00 - 4:00';

    const result = extractTimestampsFromElement(element);
    // The function currently returns the first match, which is expected behavior
    // Multiple timestamps would need to be in separate elements or parsed differently
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({
      start: 60,
      end: 120,
      raw: 'Content found in 1:00 - 2:00, 3:00 - 4:00'
    });
  });
});
