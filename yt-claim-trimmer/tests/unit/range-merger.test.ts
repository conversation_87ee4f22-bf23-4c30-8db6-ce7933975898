/**
 * Unit tests for range merging utilities
 */

import {
  mergeRanges,
  rangesOverlap,
  rangesAdjacent,
  calculateTotalDuration,
  findGaps,
  optimizeRanges,
  validateRanges,
  formatRangesForDisplay
} from '../../src/utils/range-merger';
import type { TimestampPair } from '../../src/utils/timestamp-parser';

describe('mergeRanges', () => {
  test('merges overlapping ranges', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 120 },   // 1:00 - 2:00
      { start: 100, end: 180 },  // 1:40 - 3:00 (overlaps with first)
    ];

    const result = mergeRanges(ranges);
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({
      start: 60,
      end: 180,
      originalCount: 2,
      sources: ['60-120', '100-180']
    });
  });

  test('merges adjacent ranges', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 120 },   // 1:00 - 2:00
      { start: 125, end: 180 },  // 2:05 - 3:00 (adjacent within tolerance)
    ];

    const result = mergeRanges(ranges);
    expect(result).toHaveLength(1);
    expect(result[0].start).toBe(60);
    expect(result[0].end).toBe(180);
    expect(result[0].originalCount).toBe(2);
  });

  test('keeps separate non-overlapping ranges', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 120 },   // 1:00 - 2:00
      { start: 180, end: 240 },  // 3:00 - 4:00 (separate)
    ];

    const result = mergeRanges(ranges);
    expect(result).toHaveLength(2);
    expect(result[0]).toEqual({
      start: 60,
      end: 120,
      originalCount: 1,
      sources: ['60-120']
    });
    expect(result[1]).toEqual({
      start: 180,
      end: 240,
      originalCount: 1,
      sources: ['180-240']
    });
  });

  test('sorts ranges before merging', () => {
    const ranges: TimestampPair[] = [
      { start: 180, end: 240 },  // 3:00 - 4:00
      { start: 60, end: 120 },   // 1:00 - 2:00
      { start: 100, end: 140 },  // 1:40 - 2:20 (overlaps with second)
    ];

    const result = mergeRanges(ranges);
    expect(result).toHaveLength(2);
    expect(result[0].start).toBe(60);
    expect(result[0].end).toBe(140);
    expect(result[1].start).toBe(180);
    expect(result[1].end).toBe(240);
  });

  test('handles empty array', () => {
    const result = mergeRanges([]);
    expect(result).toHaveLength(0);
  });

  test('handles single range', () => {
    const ranges: TimestampPair[] = [{ start: 60, end: 120 }];
    const result = mergeRanges(ranges);
    expect(result).toHaveLength(1);
    expect(result[0].originalCount).toBe(1);
  });

  test('preserves raw text sources', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 120, raw: 'Content found in 1:00 – 2:00' },
      { start: 100, end: 180, raw: 'Content found in 1:40 – 3:00' },
    ];

    const result = mergeRanges(ranges);
    expect(result[0].sources).toEqual([
      'Content found in 1:00 – 2:00',
      'Content found in 1:40 – 3:00'
    ]);
  });
});

describe('rangesOverlap', () => {
  test('detects overlapping ranges', () => {
    const range1 = { start: 60, end: 120 };
    const range2 = { start: 100, end: 180 };
    expect(rangesOverlap(range1, range2)).toBe(true);
  });

  test('detects non-overlapping ranges', () => {
    const range1 = { start: 60, end: 120 };
    const range2 = { start: 180, end: 240 };
    expect(rangesOverlap(range1, range2)).toBe(false);
  });

  test('detects touching ranges as non-overlapping', () => {
    const range1 = { start: 60, end: 120 };
    const range2 = { start: 120, end: 180 };
    expect(rangesOverlap(range1, range2)).toBe(false);
  });
});

describe('rangesAdjacent', () => {
  test('detects adjacent ranges within tolerance', () => {
    const range1 = { start: 60, end: 120 };
    const range2 = { start: 123, end: 180 }; // 3 seconds gap
    expect(rangesAdjacent(range1, range2, 5)).toBe(true);
  });

  test('detects non-adjacent ranges outside tolerance', () => {
    const range1 = { start: 60, end: 120 };
    const range2 = { start: 130, end: 180 }; // 10 seconds gap
    expect(rangesAdjacent(range1, range2, 5)).toBe(false);
  });

  test('detects touching ranges as adjacent', () => {
    const range1 = { start: 60, end: 120 };
    const range2 = { start: 120, end: 180 };
    expect(rangesAdjacent(range1, range2)).toBe(true);
  });
});

describe('calculateTotalDuration', () => {
  test('calculates total duration correctly', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 120 },   // 60 seconds
      { start: 180, end: 240 },  // 60 seconds
    ];
    expect(calculateTotalDuration(ranges)).toBe(120);
  });

  test('handles empty array', () => {
    expect(calculateTotalDuration([])).toBe(0);
  });
});

describe('findGaps', () => {
  test('finds gaps between ranges', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 120 },   // 1:00 - 2:00
      { start: 180, end: 240 },  // 3:00 - 4:00
    ];

    const gaps = findGaps(ranges, 300); // 5 minute video
    expect(gaps).toHaveLength(3);
    expect(gaps[0]).toEqual({ start: 0, end: 60 });     // Before first
    expect(gaps[1]).toEqual({ start: 120, end: 180 });  // Between ranges
    expect(gaps[2]).toEqual({ start: 240, end: 300 });  // After last
  });

  test('handles no gaps', () => {
    const ranges: TimestampPair[] = [
      { start: 0, end: 120 },
      { start: 120, end: 240 },
    ];

    const gaps = findGaps(ranges, 240);
    expect(gaps).toHaveLength(0);
  });

  test('handles empty ranges', () => {
    const gaps = findGaps([], 300);
    expect(gaps).toEqual([{ start: 0, end: 300 }]);
  });
});

describe('optimizeRanges', () => {
  test('removes short ranges', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 62 },    // 2 seconds (too short)
      { start: 120, end: 180 },  // 60 seconds (keep)
      { start: 240, end: 241 },  // 1 second (too short)
    ];

    const result = optimizeRanges(ranges, 3);
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({ start: 120, end: 180 });
  });

  test('keeps all ranges above minimum duration', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 65 },    // 5 seconds
      { start: 120, end: 180 },  // 60 seconds
    ];

    const result = optimizeRanges(ranges, 3);
    expect(result).toHaveLength(2);
  });
});

describe('validateRanges', () => {
  test('removes invalid ranges', () => {
    const ranges: TimestampPair[] = [
      { start: -10, end: 60 },   // Invalid (negative start)
      { start: 120, end: 100 },  // Invalid (end before start)
      { start: 180, end: 240 },  // Valid
    ];

    const result = validateRanges(ranges);
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({ start: 180, end: 240 });
  });

  test('trims ranges that exceed video duration', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 120 },   // Valid
      { start: 180, end: 350 },  // Exceeds 300s video duration
    ];

    const result = validateRanges(ranges, 300);
    expect(result).toHaveLength(2);
    expect(result[0]).toEqual({ start: 60, end: 120 });
    expect(result[1]).toEqual({ start: 180, end: 300 }); // Trimmed
  });
});

describe('formatRangesForDisplay', () => {
  test('formats ranges for human display', () => {
    const ranges: TimestampPair[] = [
      { start: 60, end: 120 },   // 1:00 - 2:00
      { start: 3660, end: 3720 }, // 1:01:00 - 1:02:00
    ];

    const result = formatRangesForDisplay(ranges);
    expect(result).toEqual([
      '1:00 - 2:00 (1:00)',
      '1:01:00 - 1:02:00 (1:00)'
    ]);
  });
});
