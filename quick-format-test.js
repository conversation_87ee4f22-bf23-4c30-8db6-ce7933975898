// Quick YouTube Studio Format Test
// Run this in devtools console on YouTube Studio editor page

console.log('🎯 QUICK YOUTUBE STUDIO FORMAT TEST');
console.log('===================================');

// Test your specific case: 9066 seconds (2:31:06)
function testYourCase() {
  const seconds = 9066; // Your specific timestamp
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  console.log(`\n📊 YOUR CASE: ${seconds} seconds`);
  console.log(`Time: ${hours}h ${minutes}m ${secs}s`);
  
  const formats = {
    'HH:MM:SS:FF': `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}:00`,
    'HH:MM:SS': `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`,
    'MM:SS:FF (total)': `${Math.floor(seconds/60)}:${secs.toString().padStart(2, '0')}:00`,
    'MM:SS (total)': `${Math.floor(seconds/60)}:${secs.toString().padStart(2, '0')}`,
    'MM:SS:FF (regular)': `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}:00`,
    'MM:SS (regular)': `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  };
  
  console.log('\n🔍 POSSIBLE FORMATS:');
  Object.entries(formats).forEach(([name, value]) => {
    console.log(`${name}: ${value}`);
  });
  
  return formats;
}

// Test what YouTube Studio actually accepts
function testInputAcceptance() {
  console.log('\n🧪 TESTING INPUT ACCEPTANCE:');
  
  const inputs = document.querySelectorAll('input[role="timer"]');
  if (inputs.length === 0) {
    console.log('❌ No timer inputs found. Are you on the YouTube Studio editor page?');
    return;
  }
  
  const testInput = inputs[0];
  const originalValue = testInput.value;
  
  console.log(`Found ${inputs.length} timer input(s)`);
  console.log(`Testing on first input (original value: "${originalValue}")`);
  
  const testValues = [
    '02:31:06:00',  // HH:MM:SS:FF
    '02:31:06',     // HH:MM:SS  
    '151:06:00',    // Total minutes with frames
    '151:06'        // Total minutes
  ];
  
  testValues.forEach((value, i) => {
    setTimeout(() => {
      console.log(`\nTest ${i + 1}: "${value}"`);
      
      // Focus and set value
      testInput.focus();
      testInput.select();
      testInput.value = value;
      
      // Trigger events
      testInput.dispatchEvent(new Event('input', { bubbles: true }));
      testInput.dispatchEvent(new Event('change', { bubbles: true }));
      testInput.blur();
      
      setTimeout(() => {
        const result = testInput.value;
        console.log(`  Result: "${result}"`);
        console.log(`  Status: ${result === value ? '✅ ACCEPTED' : result ? '🔄 TRANSFORMED' : '❌ REJECTED'}`);
        
        // Restore original after last test
        if (i === testValues.length - 1) {
          setTimeout(() => {
            testInput.value = originalValue;
            testInput.dispatchEvent(new Event('input', { bubbles: true }));
            console.log(`\n🔄 Restored original: "${originalValue}"`);
          }, 500);
        }
      }, 300);
    }, i * 1500);
  });
}

// Check what's currently in the inputs
function checkCurrentInputs() {
  console.log('\n📋 CURRENT INPUT VALUES:');
  
  const inputs = document.querySelectorAll('input[role="timer"]');
  inputs.forEach((input, i) => {
    console.log(`Input ${i + 1}: "${input.value}" (placeholder: "${input.placeholder}")`);
  });
  
  if (inputs.length === 0) {
    console.log('❌ No timer inputs found');
  }
}

// Main test function
function runQuickTest() {
  checkCurrentInputs();
  testYourCase();
  
  console.log('\n⏳ Starting acceptance tests in 2 seconds...');
  setTimeout(testInputAcceptance, 2000);
}

// Make functions available globally
window.quickTest = {
  runQuickTest,
  testYourCase,
  testInputAcceptance,
  checkCurrentInputs
};

console.log('\n🚀 INSTRUCTIONS:');
console.log('1. Navigate to YouTube Studio editor page');
console.log('2. Open a video for editing (especially a 2+ hour video)');
console.log('3. Click "Trim & cut" to enter edit mode');
console.log('4. Run: runQuickTest()');
console.log('\nOr run individual tests:');
console.log('- checkCurrentInputs()');
console.log('- testYourCase()');
console.log('- testInputAcceptance()');

// Auto-run if we detect we're on the right page
if (window.location.href.includes('studio.youtube.com') && 
    (window.location.href.includes('/editor') || document.querySelectorAll('input[role="timer"]').length > 0)) {
  console.log('\n🎯 YouTube Studio detected! Running quick test...');
  runQuickTest();
} else {
  console.log('\n⚠️  Navigate to YouTube Studio editor page first, then run: runQuickTest()');
}
