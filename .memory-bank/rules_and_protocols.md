<!-- Memory Bank Protocol Version: 1.1 (Optimized 2025-05-23) -->
## 0. Foundational Path Definitions &amp; AI Directives

**AI Initialization Action:** When you, the AI, initialize a Memory Bank for a new project and create this `rules_and_protocols.md` file within it, you MUST determine the actual absolute paths for `WORKSPACE_DIR` and `MEMORY_BANK_DIR` for that specific project. You will then **replace the placeholder "Actual Path" lines below** with these determined absolute paths using the exact format shown. You must also ensure the Protocol Version marker at the top of this document is present and reflects the version of the template you are using, with the current date.

**Core Principle:** All file operations and path references within this project MUST be grounded by the following definitions. This is critical to prevent errors such as creating files in incorrect locations or misinterpreting project structure.

1.  **`WORKSPACE_DIR` (Project Root):**
    *   **Definition:** This is the absolute root directory of the current user's project.
        *   **Actual Path (to be filled by AI during initialization):** `WORKSPACE_DIR=/Users/<USER>/dev/augment/claimcutter`
    *   **AI Directive:** You MUST always interpret relative file paths (e.g., `src/components/file.js`, `README.md`) as being relative to this `WORKSPACE_DIR`. When creating new project files or directories, ensure they are placed correctly within this `WORKSPACE_DIR` and not, for example, within the memory bank directory itself or a nested directory of the same project name.

2.  **`MEMORY_BANK_DIR` (Memory Bank Location):**
    *   **Definition:** This directory is always located at `WORKSPACE_DIR/.memory-bank/`.
        *   **Actual Path (to be filled by AI during initialization):** `MEMORY_BANK_DIR=/Users/<USER>/dev/augment/claimcutter/.memory-bank/`
    *   **AI Directive:** All files constituting the Memory Bank (e.g., `projectbrief.md`, `techContext.md`) reside exclusively within this `MEMORY_BANK_DIR`. Do not create project-specific code or assets inside the `MEMORY_BANK_DIR`.

**Operational Mandate:** Before any file creation, modification, or read operation, confirm your understanding of the target path in relation to `WORKSPACE_DIR` and `MEMORY_BANK_DIR`. If ambiguity exists, seek clarification.

---

# Augment Code Memory Bank System: Instructions &amp; Protocols

## 1. Introduction

This document outlines the operational instructions and protocols for the Augment Code Memory Bank System. This system is designed to ensure consistent, context-aware, and controlled code augmentation by the AI agent, even across sessions where its immediate memory might be reset. It combines a structured Memory Bank for persistent project knowledge with Augment Code's guideline mechanisms and a clear communication protocol.

**Core Principle:** The AI Agent relies *entirely* on the Memory Bank and these instructions at the start of *every* task. Due to potential memory resets between sessions, adherence to this principle is critical for effective and accurate assistance.

## 2. Memory Bank Structure

The Memory Bank is a collection of Markdown files stored in a `.memory-bank/` directory at the root of the project. These files provide the foundational and evolving context for the project.

```mermaid
flowchart TD
    MB[(.memory-bank/)]
    MB --&gt; PB[projectbrief.md]
    MB --&gt; PC[productContext.md]
    MB --&gt; SP[systemPatterns.md]
    MB --&gt; TC[techContext.md]
    MB --&gt; AC[activeContext.md]
    MB --&gt; P[progress.md]
    MB --&gt; R[rules_and_protocols.md]
    MB --&gt; PI[project_intelligence.md]

    PB --&gt; PC
    PB --&gt; SP
    PB --&gt; TC
    
    PC --&gt; AC
    SP --&gt; AC
    TC --&gt; AC
    
    AC --&gt; P
```

### 2.1. Core Memory Bank Files (Required)

1.  **`projectbrief.md`**:
    *   The foundational document. Defines core project requirements, goals, and overall scope.
    *   **Context Hub:** Includes a structured section (e.g., YAML frontmatter or a Markdown table) indexing key concepts and their locations within other foundational files (e.g., `productContext.md#UserPersonas`, `systemPatterns.md#Authentication`). This helps in quickly navigating to relevant detailed context.
    *   Shapes all other memory bank files. Created at project initiation.

2.  **`productContext.md`**:
    *   The "Why": Details the problem the project solves, its purpose, target users, and user experience goals.
    *   Describes how the product should ideally work from a user perspective.

3.  **`systemPatterns.md`**:
    *   The "How" (Architecture): Documents the system architecture, key technical decisions, design patterns employed, and relationships between major components.
    *   **May contain structured data blocks (e.g., YAML or JSON within Markdown comments or fenced code blocks) for precise definitions like API conventions or component interaction diagrams.**

4.  **`techContext.md`**:
    *   The "With What": Specifies technologies, frameworks, libraries, versions, development environment setup, technical constraints, and critical dependencies.
    *   **May contain structured data blocks (e.g., YAML or JSON within Markdown comments or fenced code blocks) for lists of libraries with versions, or specific configuration details.**

5.  **`activeContext.md`**:
    *   The "Now": This file is highly dynamic and managed as a **LIFO (Last-In, First-Out) stack of task contexts**. Each entry in the stack represents a distinct sub-task or operational phase.
    *   Includes: Current task goals, relevant snippets, in-progress decisions, immediate next steps, and any transient information for the active task.
    *   Completed task contexts are summarized and archived (see Section 6.3 and 6.4).

6.  **`progress.md`**:
    *   The "Status": Tracks what functionalities are complete and working, what remains to be built, known issues, bugs, and overall project completion status against goals. Summaries of completed task contexts from `activeContext.md` are archived here.

7.  **`rules_and_protocols.md` (This File)**:
    *   Defines the operational rules, communication protocols, mode definitions, and workflows for the AI agent. This is the master instruction set for agent behavior. Includes the Protocol Version marker at the top.

8.  **`project_intelligence.md`**:
    *   The "Learnings" & "Actionable Insights": Captures project-specific patterns, user preferences, critical implementation paths, known challenges, evolution of project decisions, and tool usage patterns.
    *   **Structure:** Entries should ideally be structured with fields like:
        *   `**Observation:** (What was noticed?)`
        *   `**Implication:** (What does it mean for the project?)`
        *   `**Recommendation:** (What should be done or considered? This could be an action item, a pattern to adopt, or a principle to follow.)`
    *   This is a living document. Recommendations should be periodically reviewed by the AI (and user) to see if they warrant updates to other foundational files (e.g., `systemPatterns.md`, `techContext.md`).

### 2.2. Additional Context Files (Optional)

Create additional Markdown files or subfolders within `.memory-bank/` as needed to organize complex information, such as:
*   Detailed feature specifications
*   API documentation (internal or external)
*   Integration guides
*   Testing strategies and plans
*   Deployment procedures
*   Data migration plans

#### 2.2.1. Modular Context Snippets (Advanced)
*   **Purpose:** For common patterns, configurations, or technical explanations that are reusable across multiple projects or within different parts of a large project (e.g., standard Docker setup, company-wide API authentication method, common UI component patterns).
*   **Storage:** These snippets can be stored as individual Markdown files in a dedicated subdirectory, e.g., `MEMORY_BANK_DIR/snippets/`. Each snippet should be well-defined and potentially versioned (e.g., `auth_v1.md`, `docker_setup_node_v2.md`).
*   **Usage:**
    *   Project-specific Memory Bank files (e.g., `techContext.md`, `systemPatterns.md`) can reference these snippets.
    *   **AI Directive:** When a reference to a snippet is encountered (e.g., `See snippet: 'docker_setup_node_v2.md'`), you should locate and read the content of that snippet from the `snippets/` directory to incorporate its information into your current understanding.
    *   The AI may also be instructed to "include" or "transclude" the content of a snippet into a main Memory Bank file if explicitly requested by the user, effectively copying its content into the target location.
*   **Maintenance:** Snippets should be maintained as standalone, authoritative pieces of information.

### 2.3. File Maintenance and Archival

To ensure the Memory Bank remains efficient over time, especially for long projects:

1.  **`progress.md` Archival:**
    *   When `progress.md` becomes excessively large (e.g., over a few thousand lines or as determined by performance impact), older entries should be archived.
    *   **Procedure:** The AI, with user approval, can create an archive file like `progress_archive_YYYY-MM.md` (e.g., `progress_archive_2025-01.md`) in the `MEMORY_BANK_DIR` and move older entries (e.g., entries older than 3-6 months, or by project milestone) into it.
    *   A note should be left in the current `progress.md` pointing to the latest archive file.

2.  **`activeContext.md` Management:**
    *   The LIFO stack management (see Section 2.1, item 5) inherently helps keep `activeContext.md` focused on current tasks.
    *   Summaries of completed task contexts are archived to `progress.md`.

3.  **`project_intelligence.md` Review:**
    *   Periodically (e.g., prompted by the user or at major project milestones), `project_intelligence.md` should be reviewed. Outdated learnings can be marked as deprecated or moved to an archive section within the file itself. Actionable recommendations should be processed or explicitly deferred.

### 2.4. Handling Structured Data
*   When files like `systemPatterns.md` or `techContext.md` contain structured data blocks (e.g., YAML or JSON), you should attempt to parse and utilize this data directly for relevant tasks.
*   If parsing fails or the format is unrecognized, treat the block as plain text and seek clarification if its content is critical.

## 3. Augment Code Guidelines Integration (`.augment-guidelines`)

Augment Code supports an `.augment-guidelines` file at the root of the repository for workspace-specific guidelines. This file should be used for:

*   **Concise, high-level pointers** that are immediately relevant to Augment Code's Agent and Chat.
*   Examples: Preferred libraries (e.g., "Use `pytest` for testing"), core patterns (e.g., "For NextJS, use the App Router"), anti-patterns to avoid (e.g., "Do not use deprecated module X"), naming conventions (e.g., "Function names should be verb-noun").
*   These guidelines are limited (e.g., 2000 characters). They serve as a quick reference for Augment Code's native guideline system.
*   **A critical rule for Memory Bank adherence (see Section 6.5) should be included here by the AI during initialization.**

**Relationship to Memory Bank:**
*   The `.augment-guidelines` file complements the Memory Bank.
*   The detailed operational rules, comprehensive context, and evolving project intelligence reside in the `.memory-bank/` files, particularly `rules_and_protocols.md` and `project_intelligence.md`.
*   If there's a conflict, the detailed instructions in the `.memory-bank/` take precedence for the agent's structured operation, while `.augment-guidelines` inform its general response style for Chat/Agent features not covered by this protocol.

## 4. Communication Protocol

### 4.1. Prefix: Files Under Review

**ALWAYS** begin each message with a code block detailing the files currently under review by the AI. This includes files being read for context or planned for modification.

**Format:**
```
Files under review:
└── .memory-bank/
    ├── projectbrief.md - Core project goals and scope. (Utilizing Context Hub)
    └── activeContext.md - Current work focus and next steps (LIFO Stack).
└── src/
    ├── components/
    │   └── UserProfile.tsx - Component for displaying user profiles.
    └── services/
        └── api.ts - API communication layer.
```
*   Include a directory tree structure.
*   Provide a brief, one-sentence description of each file's relevance or current state.

### 4.2. File Operation Plan

When proposing changes to files (creations or modifications), **ALWAYS** format the plan as a directory tree within a code block.

**Format:**
```
File operation plan:
└── .memory-bank/
    └── activeContext.md [M] - Update top of LIFO stack with details of the new feature implementation.
└── src/
    ├── components/
    │   ├── NewFeatureComponent.tsx [C] - Create the main component for the new feature.
    │   └── UserProfile.tsx [M] - Integrate a summary of the new feature.
    └── types/
        └── feature.ts [C] - Define TypeScript types for the new feature.
```
*   Indicate files to be modified with `[M]`.
*   Indicate files to be created with `[C]`.
*   Provide a concise, one-sentence description for each proposed change.

## 5. Operational Modes: PLAN and ACT

The AI operates in two distinct modes: PLAN and ACT. This ensures user oversight and control.

### 5.1. PLAN Mode

**Purpose:** To analyze requirements, read relevant Memory Bank files (utilizing `projectbrief.md` Context Hub), understand context, and propose a course of action. No code is written or files modified in PLAN mode.

**Message Format:**
Begin PLAN mode messages with a code block:
```
Planning... [Brief description of the planning focus, e.g., "Analyzing New Feature Requirements"]

Items Under Consideration:
1.  [Main Item 1] [STATUS_TAG]
    └── [Sub-item 1.1]
    └── [Sub-item 1.2]
2.  [Main Item 2] [STATUS_TAG]
    └── [Sub-item 2.1]
3.  [File Operation Plan (if applicable, see section 4.2)]

Do you approve this plan? If yes, I'll proceed to implementation in ACT mode.
(Alternatively, if criteria met: "Propose Quick ACT for [specific minor change]?")
```

**Status Tags for "Items Under Consideration":**
*   `[EVALUATING]`: Currently being analyzed or researched.
*   `[PENDING]`: Awaiting information or dependent on another item.
*   `[RESOLVED]`: Analysis complete, decision made, or information gathered.

**Critical Rule for PLAN Mode:**
*   The AI **MUST NEVER** transition from PLAN to ACT mode (including Quick ACT) without explicit user confirmation (e.g., "yes", "approved", "proceed").
*   If approval is unclear, remain in PLAN mode and seek clarification.

### 5.2. ACT Mode

**Purpose:** To execute the approved plan. This involves writing code, modifying files, and performing other actions as detailed in the plan.

**Message Format:**
Begin ACT mode messages with a code block:
```
Acting... [Brief description of the action, e.g., "Implementing New Feature Component"]

Task Queue:
1.  [Main Task 1] [STATUS_TAG]
    └── [Sub-task 1.1]
    └── [Sub-task 1.2]
2.  [Main Task 2] [STATUS_TAG]
    └── [Sub-task 2.1]
```

**Status Tags for "Task Queue":**
*   `[PENDING]`: Task has not yet started.
*   `[IN_PROGRESS]`: Task is currently being worked on.
*   `[COMPLETED]`: Task has been successfully finished.
*   `[BLOCKED]`: Task cannot proceed due to a dependency or issue.
*   `[FAILED]`: Task execution was attempted but failed.

### 5.3. Quick ACT Mode (Optional Variant)

**Purpose:** For very small, well-defined changes where the AI has high confidence and the risk is minimal (e.g., renaming a variable in a function it just wrote, a minor typo fix in documentation it authored).
**Trigger:** AI may propose "Quick ACT" if conditions are met during PLAN mode. User must still approve.

**Procedure:**
1.  **Proposal (within PLAN mode response):**
    ```
    Proposing Quick ACT: [Brief description of change, e.g., "Rename variable 'temp' to 'userData' in function 'getUser'"]
    File: [path/to/file.ext]
    Confidence: High
    Approve Quick ACT? (y/n)
    ```
2.  **User Confirmation:** Requires explicit user confirmation (e.g., "y", "yes").
3.  **Execution:** If approved, AI performs the action.
4.  **Logging:** The action and its outcome MUST be logged in `progress.md`. Relevant details (e.g., rationale for a rename if it establishes a pattern) should be captured in `project_intelligence.md` (using Observation, Implication, Recommendation structure). `activeContext.md` should be updated by marking the specific micro-task within the current context as complete.

**Critical Rule:** "Quick ACT" should be used sparingly and only for low-impact changes. If there is any uncertainty, the full PLAN mode must be used.

## 6. Core Workflows

### 6.1. Initial Task Workflow (Start of any new request)

```mermaid
flowchart TD
    A[User Request Received] --&gt; B{Read ALL .memory-bank/ files (noting rules_and_protocols.md version/cache status)};
    B --&gt; C{Understand Task &amp; Context (utilize projectbrief.md Context Hub)};
    C --&gt; D{Is Plan Required?};
    D -- Yes --&gt; E[Enter PLAN Mode];
    D -- No (e.g. simple query) --&gt; F[Provide Direct Answer/Info];
```

### 6.2. PLAN Mode Workflow

```mermaid
flowchart TD
    A[Enter PLAN Mode] --&gt; B[Review Relevant Memory Bank Files (using Context Hub, checking for structured data)];
    B --&gt; C[Analyze Request &amp; Formulate Strategy];
    C --&gt; D[Develop File Operation Plan (if needed)];
    D --&gt; E[Present Plan to User (using PLAN mode format, may offer Quick ACT)];
    E --&gt; F{User Approval?};
    F -- Yes --&gt; G[Transition to ACT Mode or Execute Quick ACT];
    F -- No --&gt; H[Revise Plan or Seek Clarification];
```

### 6.3. ACT Mode Workflow

```mermaid
flowchart TD
    A[Enter ACT Mode with Approved Plan] --&gt; B[Execute Tasks from Plan];
    B --&gt; C[Update Task Queue Status Regularly];
    C --&gt; D{Task Complete?};
    D -- Yes --&gt; E[Update `activeContext.md`: Mark current task context on LIFO stack as complete, summarize to `progress.md`, then pop/archive from stack];
    E --&gt; F[Update `project_intelligence.md` with structured learnings (Observation, Implication, Recommendation) if new patterns learned or insights gained];
    F --&gt; G[Report Completion to User];
    D -- No / Issue Encountered --&gt; H[Report Issue, Potentially Revert to PLAN Mode];
```

### 6.4. Memory Bank Update Workflow

Memory Bank files (especially `activeContext.md` (LIFO stack management), `progress.md` (archiving summaries from `activeContext.md`), and `project_intelligence.md` (structured learnings)) **MUST** be updated:
1.  After significant changes are implemented (completion of an ACT mode cycle).
2.  When new project patterns, preferences, or critical decisions are identified (for `project_intelligence.md`).
3.  When the user explicitly requests with a command like "**update memory bank**".
    *   In this case, **ALL** core Memory Bank files **MUST** be reviewed (respecting caching rules for `rules_and_protocols.md`), even if some don't require immediate textual updates, to ensure the AI's internal understanding is refreshed.
4.  When current context needs clarification or correction.

```mermaid
flowchart TD
    Trigger[Update Triggered] --&gt; Review[Review ALL Core Memory Bank Files (respecting cache for rules, using Context Hub)]
    Review --&gt; Identify[Identify Changes/New Info/Learnings]
    Identify --&gt; UpdateFiles[Update Relevant .md Files (manage activeContext stack, append to progress)]
    UpdateFiles --&gt; UpdatePI[Update project_intelligence.md with structured learnings if applicable]
    UpdatePI --&gt; Validate[Validate Consistency Across Memory Bank]
    Validate --&gt; Confirm[Confirm Update with User]
```

### 6.5. Memory Bank Initialization Workflow

Upon receiving a user command like "initialize memory bank", "start memory bank", or "build memory bank", the AI will perform the following steps:

1.  **Acknowledge Command:** Confirm understanding of the request.
2.  **Create Directory:**
    *   The AI will ensure the `.memory-bank/` directory exists at the project root, creating it if necessary using its built-in capabilities for file system operations.
3.  **Create Core Files:**
    *   The AI will then ensure the following empty Markdown files are present inside `.memory-bank/` (unless specified otherwise for `rules_and_protocols.md`), creating them if necessary using its file creation capabilities:
        *   `projectbrief.md`
        *   `productContext.md`
        *   `systemPatterns.md`
        *   `techContext.md`
        *   `activeContext.md`
        *   `progress.md`
        *   `project_intelligence.md`
        *   `rules_and_protocols.md`: The AI will take the master content of the "Augment Code Memory Bank System: Instructions &amp; Protocols" (i.e., the structure of this document itself, as known to the AI). It will then perform the following critical steps:
            1.  Determine the absolute `WORKSPACE_DIR` for the current project it is operating within.
            2.  Using this determined `WORKSPACE_DIR`, it will construct the absolute `MEMORY_BANK_DIR` (e.g., `WORKSPACE_DIR/.memory-bank/`).
            3.  It will then modify the "0. Foundational Path Definitions &amp; AI Directives" section within the master content:
                *   Replace the placeholder `WORKSPACE_DIR=/REPLACE_WITH_ACTUAL_WORKSPACE_DIR` with the actual determined path (e.g., `WORKSPACE_DIR=/Users/<USER>/Documents/dczapped`).
                *   Replace the placeholder `MEMORY_BANK_DIR=/REPLACE_WITH_ACTUAL_WORKSPACE_DIR/.memory-bank/` with the actual derived path (e.g., `MEMORY_BANK_DIR=/Users/<USER>/Documents/dczapped/.memory-bank/`).
            4.  It will ensure the `<!-- Memory Bank Protocol Version: 1.1 (Optimized YYYY-MM-DD) -->` marker is present at the top of the content, updating YYYY-MM-DD to the current date.
            5.  Finally, the AI will write this fully personalized and path-updated version of the `rules_and_protocols.md` content into the newly created `.memory-bank/rules_and_protocols.md` file in the user's project.
4.  **Confirmation and Next Steps:**
    *   Report the successful setup or verification of the directory and files (including the personalized `rules_and_protocols.md`).
    *   **Crucially, prompt the user to populate `.memory-bank/projectbrief.md` with the core project goals, scope, and initial Context Hub entries.**
    *   Offer to create or update the project's root `.augment-guidelines` file by adding/ensuring the presence of the following critical rule:
        ```
        - Critical: This project uses a Memory Bank. Always operate according to '.memory-bank/rules_and_protocols.md'. Read it first for any complex task.
        ```
    *   If the user agrees, the AI will use its file writing capabilities to add this line. If `.augment-guidelines` already exists, the AI should append this rule if not already present, being careful not to remove existing user guidelines.

```mermaid
flowchart TD
    UserInput["User: 'Initialize memory bank'"] --&gt; AICheck{Directory .memory-bank/ Exists?};
    AICheck -- No --&gt; CreateDir[AI: Create .memory-bank/ via tools];
    AICheck -- Yes --&gt; CreateFiles[AI: Check/Create Core Files (incl. personalized rules_and_protocols.md with version) via tools];
    CreateDir --&gt; CreateFiles;
    CreateFiles --&gt; PromptUser[AI: Prompt user for projectbrief.md content (incl. Context Hub)];
    PromptUser --&gt; SetupAugmentGuidelines[AI: Offer to setup .augment-guidelines];
    SetupAugmentGuidelines --&gt; Ready[Memory Bank Initialized];
```

## 7. Key Directives Summary

*   **Mandatory Reading:** At the start of EVERY task, read ALL files in the `.memory-bank/` directory (respecting caching for `rules_and_protocols.md`, using Context Hub in `projectbrief.md`) to establish full context, paying special attention to the paths defined in Section 0 of `rules_and_protocols.md`.
*   **Strict Mode Control:** NEVER transition from PLAN to ACT mode (including Quick ACT) without explicit user approval.
*   **Documentation First:** Maintain the Memory Bank with precision and clarity. It is the AI's sole source of truth.
*   **Structured Communication:** Adhere strictly to the "Files under review" and "File operation plan" formats.
*   **Continuous Learning:** Actively update `project_intelligence.md` with structured (Observation, Implication, Recommendation) insights.
*   **Utilize Context Hub:** Leverage the index in `projectbrief.md` to quickly locate relevant information in other Memory Bank files.
*   **Handle Structured Data:** Parse and utilize YAML/JSON blocks within Markdown files where present and applicable.
*   **Manage `activeContext.md` as LIFO Stack:** Archive completed task summaries to `progress.md`.
*   **Consider Archival:** Follow guidelines for archiving `progress.md` and reviewing `project_intelligence.md`.

## 8. AI Self-Optimization & Adherence Checks

### 8.1. Rules Caching (This File - `rules_and_protocols.md`)
*   **Objective:** To reduce redundant processing of this `rules_and_protocols.md` file within a single continuous session.
*   **Procedure:**
    1.  Upon first read in a session, note the "Last Modified" timestamp of this file (if available from the environment).
    2.  You MAY cache a summary or a structural representation (e.g., parsed sections, checksum) of these rules.
    3.  On subsequent needs to consult these rules *within the same session*:
        *   If the "Last Modified" timestamp can be checked and has not changed, you MAY rely on your cached understanding for *non-critical, routine operations*.
        *   **Crucially, for any major planning (entering PLAN mode), critical decisions, or if there's any doubt about rule interpretation, you MUST re-read this file thoroughly.**
        *   If the timestamp cannot be checked, or if a new session starts, a full re-read is mandatory.
*   **User Override:** If the user indicates that `rules_and_protocols.md` has been updated, a full re-read is mandatory.

### 8.2. Adherence Rule Check (Augment Code Specific)
*   **Objective:** To proactively ensure the AI is operating under the Memory Bank protocol.
*   **Procedure (At the start of a new session or major task, if feasible by the AI platform):**
    1.  Check if the project's root `.augment-guidelines` file exists.
    2.  If it exists, check if it contains the critical adherence rule:
        `"- Critical: This project uses a Memory Bank. Always operate according to '.memory-bank/rules_and_protocols.md'. Read it first for any complex task."` (or a close, functional equivalent).
    3.  If the file is missing, or the rule is missing or substantially incorrect, you should:
        *   Inform the user of this discrepancy.
        *   Remind the user of the importance of this rule for proper Memory Bank operation.
        *   Offer to add or correct the rule in `.augment-guidelines` as per the initialization workflow (Section 6.5, step 4).
*   **Constraint:** This check depends on your ability to read files outside the `MEMORY_BANK_DIR` (i.e., at `WORKSPACE_DIR`) and the specifics of the Augment Code platform. Perform this check on a best-effort basis.

This instruction set is designed to make the AI a reliable and highly effective partner in code augmentation. Adherence is paramount.