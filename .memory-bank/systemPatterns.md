# System Patterns

## The "How" - Architecture and Design Patterns

### ExtensionArchitecture

The Chrome Extension follows Manifest V3 architecture with a service worker-based background script and content scripts for DOM interaction.

**High-Level Architecture Flow:**
```
┌────────────┐   ① inject        ┌─────────────────────┐
│ popup.html │─────────────▶    │ content‑script A     │
└────────────┘                    │ collect‑timestamps  │
     ▲  ▲          message        └─────────────────────┘
     │  │  ⑤ status                  │   save & send
  open│  │                          ▼
     │  │        tabs.update   ┌─────────────────────┐
     │  └───────────────▶     │ background SW       │
     │                         │ (service worker)    │
     │                         └─────────────────────┘
     │                              │ inject
     │                              ▼
     │                         ┌─────────────────────┐
     └───────────────────────▶ │ content‑script B    │
     ④ show modal              │ apply‑cuts          │
                               └─────────────────────┘
```

**Directory Structure:**
```
yt-claim-trimmer/
├─ public/
│   ├─ icons/  (16,32,48,128 png)
│   └─ popup.html
├─ src/
│   ├─ manifest.json  (MV3)
│   ├─ background.ts
│   ├─ scripts/
│   │   ├─ collect-timestamps.ts
│   │   └─ apply-cuts.ts
│   └─ ui/
│       └─ popup.ts
├─ tests/
│   └─ playwright/
│       ├─ selectors.spec.ts
│       └─ e2e-collect-apply.spec.ts
├─ package.json
├─ tsconfig.json
└─ vite.config.ts  (bundle to /dist for Load unpacked)
```

### ContentScriptFlow

The extension operates through a detailed five-step workflow:

**Step 1: User Initiation**
1. User clicks toolbar icon → `popup.html` shows **Start / Dry-run** options
2. User selects mode and clicks Start
3. Popup sends message to background service worker

**Step 2: Timestamp Collection** (`/video/*/copyright`)
1. Background service worker injects `collect-timestamps.ts` (content-script A)
2. Content script A:
   - Loops through copyright claim rows using robust selectors
   - Clicks "See details" for each claim with random delays (300-700ms)
   - Regex parses "Content found in ##:## – ##:##" text
   - Validates and converts timestamps to seconds
   - Pushes timestamp pairs to array
   - Stores data in `chrome.storage.session`
   - Sends completion message to background

**Step 3: Navigation & Preparation**
1. Background receives completion message from content script A
2. Background navigates tab to `/video/*/editor` URL via `chrome.tabs.update()`
3. Background waits for `document.readyState === 'complete'`

**Step 4: Cut Application** (`/video/*/editor`)
1. Background injects `apply-cuts.ts` (content-script B)
2. Content script B:
   - Retrieves timestamp array from `chrome.storage.session`
   - Sorts and merges overlapping timestamp pairs using `mergeRanges()` utility
   - For each timestamp pair:
     - Focuses timeline timecode input
     - Types start time + Enter
     - Clicks "New cut" button
     - Types end time in cut dialog
     - Clicks confirm (✓) button
     - Applies random delay (300-700ms)

**Step 5: User Confirmation**
1. Content script B shows in-page modal with:
   - List of all cuts to be applied
   - Start/End timestamps with duration
   - Cancel/Proceed buttons
2. If user clicks Proceed:
   - Modal closes
   - Native Save button is highlighted with CSS pulse animation
   - User manually clicks Save to complete process

### MessagePassing

Communication between components uses Chrome's runtime messaging:

```javascript
// Background to Content Script
chrome.tabs.sendMessage(tabId, {
  action: 'START_COLLECTION',
  data: { videoId: 'abc123' }
});

// Content Script to Background
chrome.runtime.sendMessage({
  action: 'TIMESTAMPS_COLLECTED',
  data: { timestamps: [...], count: 5 }
});

// Storage for session data
chrome.storage.session.set({
  'claimcutter_timestamps': timestampArray
});
```

### ErrorHandling

Comprehensive error handling for common failure scenarios:

**Login Session Loss**
- Detect redirect to `accounts.google.com`
- Pause script execution
- Show toast: "Please re-login and press Resume"
- Provide resume functionality

**DOM Selector Changes**
- Centralized selector mapping for easy updates
- Fallback selectors using ARIA labels and data-test attributes
- Error reporting with specific error codes
- Link to GitHub Issues for user reporting

**Video Processing State**
- Check if video is already being processed
- Warn user and exit gracefully
- Prevent concurrent editing operations

**Network/Performance Issues**
- Timeout handling for slow page loads
- Retry logic for failed DOM interactions
- Progress indicators for long operations

### UIComponents

**Extension Popup** (`popup.html`)
- Start/Stop buttons
- Progress indicator
- Dry-run mode toggle
- Settings panel

**Confirmation Modal** (Injected into YouTube Studio)
- List of all cuts to be applied
- Start/End timestamps with duration
- Cancel/Proceed buttons
- Warning about irreversible edits

**Progress Overlay**
- Real-time status updates
- Current operation indicator
- Cancel button for user control

---

*This file documents the system architecture, key technical decisions, design patterns employed, and relationships between major components.*
