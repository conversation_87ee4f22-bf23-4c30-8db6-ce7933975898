# Project Brief

## Core Project Goals and Scope

**Product Name**: YouTube Copyright Claim <PERSON>ch <PERSON>mmer – Chrome Extension
**Owner**: ClaimCutter Development Team
**Last updated**: 2025-01-27

### Primary Goal
Automate the YouTube Studio copyright claim resolution workflow by creating a Chrome Extension that collects copyright claim timestamps and applies batch cuts in the video editor, reducing manual editing time from 15-20 minutes to under 2 minutes.

### Core Objectives
1. **Automate Timestamp Collection**: Automatically scrape all copyright claim timestamps from YouTube Studio's Copyright tab
2. **Batch Cut Application**: Apply all cuts in a single Editor session to minimize video re-processing
3. **User Safety**: Maintain manual control over the final Save action to ensure user approval
4. **Zero Re-uploads**: Preserve original video URL and analytics by using YouTube's native trim functionality
5. **ToS Compliance**: Operate entirely within the user's active browser session without stealth techniques

### Success Metrics
- Reduce claim resolution time to **< 2 minutes** for ≤10 claims
- Achieve **0 incorrect or missed cuts** per video
- Maintain **0 incidents** of forced re-upload
- Generate **0 YouTube/Google security warnings**

### Project Scope
- **MVP**: Chrome Extension (Manifest V3) supporting YouTube Studio copyright claim batch processing
- **Target Users**: Solo vloggers and gaming streamers with long-form content (30+ minutes)
- **Browser Support**: Google Chrome 115+ on macOS, Windows, Linux
- **Timeline**: 6-week development cycle to version 1.0

## Context Hub

This section serves as an index to key concepts and their locations within other foundational files:

### Product Context References
- User Personas: `productContext.md#UserPersonas` (Solo Vloggers, Gaming Streamers)
- User Stories: `productContext.md#UserStories` (MVP and v1.1 features)
- Success Metrics: `productContext.md#SuccessMetrics`
- Problem Statement: `productContext.md#ProblemStatement`

### System Architecture References
- Extension Architecture: `systemPatterns.md#ExtensionArchitecture` (Manifest V3, Service Worker)
- Content Script Flow: `systemPatterns.md#ContentScriptFlow`
- Message Passing: `systemPatterns.md#MessagePassing`
- Error Handling: `systemPatterns.md#ErrorHandling`
- UI Components: `systemPatterns.md#UIComponents`

### Technical Context References
- Chrome Extension Stack: `techContext.md#ChromeExtensionStack`
- Permissions Model: `techContext.md#PermissionsModel`
- YouTube Studio Selectors: `techContext.md#YouTubeStudioSelectors`
- Testing Framework: `techContext.md#TestingFramework`
- Development Tools: `techContext.md#DevelopmentTools`

### Active Work References
- Current Sprint: `activeContext.md#CurrentSprint`
- Implementation Tasks: `activeContext.md#ImplementationTasks`
- Blockers: `activeContext.md#Blockers`

### Progress References
- Development Milestones: `progress.md#DevelopmentMilestones`
- Feature Completion: `progress.md#FeatureCompletion`
- Testing Status: `progress.md#TestingStatus`
- Known Issues: `progress.md#KnownIssues`

### Intelligence References
- YouTube Studio Patterns: `project_intelligence.md#YouTubeStudioPatterns`
- Extension Development Insights: `project_intelligence.md#ExtensionDevelopmentInsights`
- Risk Mitigations: `project_intelligence.md#RiskMitigations`

---

*This file shapes all other memory bank files and should be created at project initiation.*
