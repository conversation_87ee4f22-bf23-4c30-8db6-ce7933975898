# Technical Context

## The "With What" - Technologies and Tools

### ChromeExtensionStack

**Core Platform**
- **Chrome Extension Manifest V3**: Required for modern Chrome extensions (mandatory June 2025)
- **Service Worker**: Background script for event handling and navigation (minimal footprint)
- **Content Scripts**: DOM manipulation within YouTube Studio pages
- **Chrome APIs**: Storage, tabs, scripting, and runtime messaging

**Development Technologies**
- **TypeScript 5.x**: Type safety and modern JavaScript features
- **Vite**: Modern build tool targeting Chrome 115+ with MV3 compliance
- **ESLint + Prettier**: Code quality and formatting
- **pnpm**: Fast, efficient package management

**Frontend Technologies**
- **Vanilla TypeScript**: No external frameworks to minimize bundle size and complexity
- **HTML5/CSS3**: Extension popup and injected modal interfaces
- **Tailwind CSS**: Utility-first CSS framework for rapid UI development (inline classes)
- **Chrome Storage API**: Session-based data persistence

### KeyTechnicalDecisions

| Area | Choice | Rationale |
|------|--------|-----------|
| **Manifest version** | **V3** | Required June 2025; service worker background keeps footprint minimal |
| **Message passing** | `chrome.runtime.sendMessage` + `chrome.tabs.sendMessage` | Simpler than ports; sufficient for linear workflow |
| **Storage** | `chrome.storage.session` | Clears automatically when tab closes, no privacy risk |
| **Testing** | Playwright headed, connectOverCDP | Verifies real Studio selectors without anti-bot flags |
| **Random delay** | 300–700 ms via `delay()` util | Mimics human clicks; avoids double-add bug |
| **Language** | TypeScript 5.x | Type safety, modern features, better AI code generation |
| **Bundler** | Vite | Fast builds, MV3 compliance, Chrome 115+ target |
| **UI Framework** | Vanilla TS + Tailwind | Minimal bundle size, no framework complexity |
| **Package Manager** | pnpm | Fast installs, efficient disk usage |

### BuildSystem

**Vite Configuration**
- **Target**: Chrome 115+ with Manifest V3 compliance
- **Output**: `/dist` folder ready for "Load unpacked"
- **TypeScript**: Full type checking and modern JS features
- **Bundle Optimization**: Tree shaking and code splitting

**Development Workflow**
```bash
pnpm install          # Install dependencies
pnpm run dev          # Development build with watch mode
pnpm run build        # Production build to /dist
pnpm run lint         # ESLint + Prettier check
pnpm run test         # Jest unit tests
pnpm run test:e2e     # Playwright integration tests
```

**CI/CD Pipeline**
- **GitHub Actions**: Node 18 workflow
- **Steps**: pnpm install → lint → test → playwright-run
- **Release**: Build → zip `/dist` → attach to GitHub Releases

### PermissionsModel

Required Chrome extension permissions for functionality:

```json
{
  "permissions": [
    "activeTab",     // Access to current YouTube Studio tab
    "scripting",     // Inject content scripts dynamically
    "storage"        // Store timestamp data in session
  ],
  "host_permissions": [
    "https://studio.youtube.com/*"  // YouTube Studio domain access
  ]
}
```

**Security Considerations**
- Minimal permissions following principle of least privilege
- No external network requests outside YouTube Studio domain
- Session-only storage (cleared when tab closes)
- No persistent data collection or analytics

### YouTubeStudioSelectors

Critical DOM selectors for YouTube Studio interaction:

```yaml
selectors:
  copyright_page:
    claim_rows: 'div[aria-label="Copyright notice row"]'
    see_details_button: 'button[aria-label="See details"]'
    timestamp_text: 'span:contains("Content found in")'

  editor_page:
    timeline_timecode: 'input[aria-label="Timeline timecode"]'
    new_cut_button: 'button[aria-label="New cut"]'
    confirm_cut: 'button[aria-label="Confirm cut"]'
    save_button: 'button[aria-label="Save"]'

  fallback_selectors:
    # ARIA-based selectors for internationalization
    # data-test attributes where available
    # CSS class fallbacks with version detection
```

### YouTubeStudioEditorWorkflow - CONFIRMED WORKING

**Successful Trim Application Process:**
1. **Enter Trim Mode**: Click 'Trim & cut' button (anchor tag with class 'style-scope ytve-entrypoint-options-panel')
2. **Start New Cut**: Click 'New Cut' button (ytcp-button.style-scope.ytve-trim-options-panel)
3. **Input Start Time**:
   - Find first timestamp input: `document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]')[0]`
   - Clear existing content: `input.select(); input.focus();`
   - Input start time: `input.value = startTime`
4. **Input End Time**:
   - Find second timestamp input: `document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]')[1]`
   - Clear and input end time same way
5. **Save Cut**:
   ```javascript
   // Find visible Cut button - WORKING METHOD
   const allIconButtons = document.querySelectorAll('ytcp-icon-button');
   const cutButton = Array.from(allIconButtons).find(btn =>
     btn.offsetParent !== null && btn.getAttribute('aria-label') === 'Cut'
   );
   cutButton.click();
   ```
6. **For Next Cut**: Input next start time in visible timestamp input to enable New Cut button

**Key Technical Details:**
- Timestamp format: MM:SS:FF (minutes:seconds:frames) for videos under 1 hour
- Cut button selector: Must find visible ytcp-icon-button with aria-label="Cut" using offsetParent check
- **CRITICAL**: New Cut button enablement is controlled by timeline needle position - the button only becomes enabled when the timeline needle (controlled by timestamp input value) is positioned in an uncut region that doesn't overlap with existing cuts
- Multiple timestamp inputs exist but only current cut's inputs are visible (offsetParent !== null)
- Multiple Cut buttons exist in DOM - must find the visible one for current trim dialog
- Timeline needle position must be moved to uncut regions between cuts to enable New Cut button for subsequent cuts

### TestingFramework

**Unit Testing**
- **Jest**: JavaScript unit testing framework
- **Chrome Extension Testing Utils**: Mock Chrome APIs
- **DOM Testing Library**: Virtual DOM testing for content scripts

**Integration Testing**
- **Playwright**: Headed browser testing with real Chrome extension
- **User Data Directory**: Persistent test environment with extension loaded
- **YouTube Studio Test Videos**: Dedicated test content with known copyright claims

**Selector Regression Testing**
- **Nightly Playwright Runs**: Automated detection of YouTube Studio DOM changes
- **Selector Validation**: Verify all critical selectors still exist
- **Screenshot Comparison**: Visual regression testing for UI changes

### DevelopmentTools

**Development Environment**
- **Node.js 18+**: JavaScript runtime for build tools
- **Chrome DevTools**: Extension debugging and performance analysis
- **VS Code**: Primary IDE with Chrome extension development plugins

**Build and Deployment**
- **npm scripts**: Build automation and packaging
- **Chrome Extension CLI**: Development server and hot reload
- **ZIP packaging**: Distribution format for "Load unpacked"

**Version Control**
- **Git**: Source code management
- **GitHub**: Repository hosting and issue tracking
- **Semantic Versioning**: Release numbering (1.0.0, 1.1.0, etc.)

### Dependencies

```yaml
dependencies:
  runtime:
    - name: "Chrome Browser"
      version: "115+"
      purpose: "Extension runtime environment"
    - name: "YouTube Studio"
      version: "current"
      purpose: "Target application for automation"

  development:
    - name: "Node.js"
      version: "18+"
      purpose: "Build tools and package management"
    - name: "Jest"
      version: "29+"
      purpose: "Unit testing framework"
    - name: "Playwright"
      version: "1.40+"
      purpose: "Integration testing with real browser"

  testing:
    - name: "@playwright/test"
      version: "1.40+"
      purpose: "Browser automation testing"
    - name: "jest-chrome"
      version: "0.8+"
      purpose: "Chrome API mocking for unit tests"
```

### Technical Constraints

**Browser Compatibility**
- Chrome 115+ only (Manifest V3 requirement)
- No Firefox or Safari support in MVP
- Desktop only (mobile Chrome extensions not supported)

**YouTube Studio Limitations**
- DOM structure changes frequently (requires maintenance)
- Internationalization affects text-based selectors
- Rate limiting on rapid UI interactions
- Session timeout handling required

**Performance Constraints**
- Random delays (300-700ms) between actions to avoid detection
- Maximum 15 seconds for timestamp collection
- Memory usage must stay under 50MB
- No background processing when tab is inactive

**Security Restrictions**
- No external API calls or data transmission
- Content Security Policy compliance
- No eval() or dynamic code execution
- Sandboxed content script environment

### Configuration Details

**Extension Manifest Configuration**

```json
{
  "manifest_version": 3,
  "name": "YouTube Copyright Claim Batch Trimmer",
  "version": "1.0.0",
  "description": "Automate copyright claim resolution in YouTube Studio",
  "permissions": ["activeTab", "scripting", "storage"],
  "host_permissions": ["https://studio.youtube.com/*"],
  "background": {
    "service_worker": "background.js"
  },
  "content_scripts": [],
  "action": {
    "default_popup": "popup.html",
    "default_title": "ClaimCutter"
  },
  "icons": {
    "16": "icons/icon16.png",
    "48": "icons/icon48.png",
    "128": "icons/icon128.png"
  }
}
```

**Development Configuration**

```json
{
  "scripts": {
    "build": "npm run clean && npm run copy-files",
    "test": "jest",
    "test:e2e": "playwright test",
    "dev": "npm run build && chrome --load-extension=./dist",
    "package": "zip -r claimcutter-v1.0.0.zip dist/"
  },
  "jest": {
    "testEnvironment": "jsdom",
    "setupFilesAfterEnv": ["jest-chrome/object"]
  }
}
```

---

*This file specifies technologies, frameworks, libraries, versions, development environment setup, technical constraints, and critical dependencies.*
