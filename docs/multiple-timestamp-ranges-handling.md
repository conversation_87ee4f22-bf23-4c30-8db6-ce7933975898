# Multiple Timestamp Ranges Handling in ClaimCutter

## Overview

ClaimCutter has been enhanced to detect and handle **multiple timestamp ranges within a single copyright claim**. This addresses edge cases where YouTube Studio copyright claims contain multiple time segments, such as music compilations, long-form content with scattered claims, or complex copyright scenarios.

## Problem Statement

### Common Edge Cases
- **Music compilations**: A single claim covering multiple song segments
- **Long-form content**: Scattered copyrighted portions throughout a video
- **Complex claims**: Multiple time periods within one copyright holder's claim
- **Compilation videos**: Several copyrighted segments from the same source

### Previous Limitations
- ClaimCutter originally expected one timestamp range per claim
- Multiple ranges in a single claim modal could be missed
- Format inconsistencies could cause parsing errors

## Enhanced Detection System

### 1. Multiple Regex Patterns

ClaimCutter now uses **5 comprehensive regex patterns** to detect various timestamp formats:

```typescript
const timestampPatterns = [
  // YouTube specific "Content found in" patterns
  /Content found in\s+(\d{1,2}:\d{2}(?::\d{2})?)\s*[-–—]\s*(\d{1,2}:\d{2}(?::\d{2})?)/gi,

  // Multiple ranges with separators like "1:23-2:45, 3:10-4:20"
  /(\d{1,2}:\d{2}(?::\d{2})?)\s*[-–—]\s*(\d{1,2}:\d{2}(?::\d{2})?)/g,

  // "From X to Y" patterns
  /from\s+(\d{1,2}:\d{2}(?::\d{2})?)\s+to\s+(\d{1,2}:\d{2}(?::\d{2})?)/gi,

  // "Between X and Y" patterns
  /between\s+(\d{1,2}:\d{2}(?::\d{2})?)\s+and\s+(\d{1,2}:\d{2}(?::\d{2})?)/gi,

  // Parenthetical ranges "(1:23 - 2:45)"
  /\((\d{1,2}:\d{2}(?::\d{2})?)\s*[-–—]\s*(\d{1,2}:\d{2}(?::\d{2})?)\)/g,
];
```

### 2. Supported Timestamp Formats

#### Text Patterns Detected:
- `Content found in 2:31:06 - 2:32:36`
- `1:23-2:45, 3:10-4:20` (comma-separated multiple ranges)
- `from 1:23 to 2:45`
- `between 1:23 and 2:45`
- `(1:23 - 2:45)` (parenthetical format)
- `1:23 - 2:45` (basic dash format)

#### Time Formats Supported:
- **MM:SS** format (e.g., `1:23`)
- **H:MM:SS** format (e.g., `2:31:06`)
- **HH:MM:SS** format (e.g., `12:31:06`)

## Robust Format Validation

### Aggressive Error Detection

To prevent parsing errors like the `31:06 - 02:32:36` issue, ClaimCutter implements:

```typescript
// Enhanced format validation with aggressive error detection
const startParts = match[1].split(':');
const endParts = match[2].split(':');

// Reject mismatched formats (this prevents parsing errors)
if (startParts.length !== endParts.length) {
  console.warn(`⚠️ Format mismatch rejected: ${rawText}`);
  return;
}

// Reject invalid part counts
if (startParts.length < 2 || startParts.length > 3) {
  console.warn(`⚠️ Invalid format rejected: ${rawText}`);
  return;
}
```

### Validation Rules:
1. **Format Consistency**: Start and end timestamps must have same number of parts
2. **Valid Part Count**: Only 2-3 time parts allowed (MM:SS or HH:MM:SS)
3. **Logical Order**: End time must be after start time
4. **Duration Limits**: Warns about ranges over 10 minutes (potential parsing errors)

## Processing Workflow

### 1. Modal Text Extraction
```typescript
function extractTimestampsFromModal(modal: Element): TimestampPair[] {
  const modalText = modal.textContent || '';
  console.log('📄 Modal text length:', modalText.length);
  
  // Process all patterns against modal text
  // ...
}
```

### 2. Pattern Matching & Validation
```typescript
timestampPatterns.forEach((pattern, patternIndex) => {
  const matches = [...modalText.matchAll(pattern)];
  
  if (matches.length > 0) {
    console.log(`🎯 Pattern ${patternIndex + 1} found ${matches.length} match(es)`);
  }
  
  // Validate each match...
});
```

### 3. Deduplication & Storage
```typescript
const seenTimestamps = new Set<string>();
const timestampKey = `${start}-${end}`;

if (!seenTimestamps.has(timestampKey)) {
  seenTimestamps.add(timestampKey);
  timestamps.push({ start, end, raw: rawText });
}
```

## Enhanced Logging & Debugging

### Console Output Examples

#### Single Range (Normal Case):
```
🔍 Extracting timestamps from modal content...
📄 Modal text length: 1247
🎯 Pattern 1 found 1 match(es)
✅ Found timestamp 1: 2:31:06 - 2:32:36 (9066s - 9156s, duration: 1m 30s)
📊 Extraction summary: 1 total matches, 1 valid unique timestamps
```

#### Multiple Ranges (Edge Case):
```
🔍 Extracting timestamps from modal content...
📄 Modal text length: 2156
🎯 Pattern 2 found 3 match(es)
✅ Found timestamp 1: 1:23 - 2:45 (83s - 165s, duration: 1m 22s)
✅ Found timestamp 2: 3:10 - 4:20 (190s - 260s, duration: 1m 10s)
✅ Found timestamp 3: 5:30 - 6:15 (330s - 375s, duration: 0m 45s)
📊 Extraction summary: 3 total matches, 3 valid unique timestamps
🎵 Multiple timestamp ranges detected in single claim:
  Range 1: 1:23 - 2:45 (1m 22s)
  Range 2: 3:10 - 4:20 (1m 10s)
  Range 3: 5:30 - 6:15 (0m 45s)
```

#### Format Error Prevention:
```
⚠️ Format mismatch rejected: 31:06 - 02:32:36 (start has 2 parts, end has 3 parts)
⚠️ Large timestamp range detected: 0:31:06 - 2:32:36 - verify this is correct
```

## UI Display Enhancement

### Collection Results Display

The UI automatically handles multiple ranges by:

1. **Individual Range Display**: Each timestamp range shown separately
2. **Merge Information**: When ranges overlap, shows merge details
3. **Source Tracking**: Displays original sources for merged ranges

#### Example UI Output:
```
✅ Collection Complete!

Claims Processed: 1
Final Timestamps: 3

1. 01:23 - 02:45 (01:22)
2. 03:10 - 04:20 (01:10) 
3. 05:30 - 06:15 (00:45)
```

#### With Merging:
```
✅ Collection Complete!

Claims Processed: 2
Raw Timestamps Found: 4
Final Timestamps: 2
Merged Ranges: 1

1. 01:23 - 04:20 (02:57) [MERGED]
   🔗 Merged from 3 sources: 1:23-2:45, 2:30-3:15, 3:10-4:20
2. 05:30 - 06:15 (00:45)
```

## Technical Implementation

### File Locations
- **Main Logic**: `yt-claim-trimmer/src/scripts/collect-timestamps.ts`
- **UI Display**: `yt-claim-trimmer/src/ui/popup.ts`
- **Type Definitions**: `yt-claim-trimmer/src/utils/timestamp-parser.ts`

### Key Functions
- `extractTimestampsFromModal()`: Enhanced pattern detection
- `parseTimestamp()`: Format validation and conversion
- `showCollectionResults()`: UI display with multiple range support

### Data Structure
```typescript
interface TimestampPair {
  start: number;    // seconds
  end: number;      // seconds
  raw?: string;     // original text for debugging
}
```

## Testing Scenarios

### Test Cases to Verify

1. **Single Range Claims** (backward compatibility)
   - Standard `Content found in 2:31:06 - 2:32:36`
   - Should work exactly as before

2. **Multiple Range Claims** (new functionality)
   - Music compilation with 3+ segments
   - Long-form content with scattered claims
   - Complex copyright scenarios

3. **Format Edge Cases**
   - Mixed MM:SS and HH:MM:SS formats
   - Various separators (-, –, —)
   - Parenthetical formats

4. **Error Prevention**
   - Malformed timestamp text
   - Inconsistent format combinations
   - Extremely long duration ranges

### Manual Testing Steps

1. **Find Multi-Range Claim**:
   - Look for music compilation videos
   - Check long-form content with multiple claims
   - Test complex copyright scenarios

2. **Verify Detection**:
   - Open browser console (F12)
   - Run ClaimCutter collection
   - Check for multiple range detection logs

3. **Validate UI Display**:
   - Confirm all ranges shown separately
   - Verify merge information if applicable
   - Test apply cuts functionality

## Backward Compatibility

### Guaranteed Compatibility
- ✅ **Single range claims** work exactly as before
- ✅ **Existing UI** handles multiple ranges automatically
- ✅ **Merge logic** preserves existing functionality
- ✅ **Error handling** maintains robustness

### No Breaking Changes
- All existing functionality preserved
- Enhanced detection is additive only
- UI gracefully handles both single and multiple ranges
- Console logging provides additional debugging info

## Future Enhancements

### Potential Improvements
1. **Visual Range Indicators**: Timeline visualization in UI
2. **Range Grouping**: Group related ranges by copyright holder
3. **Smart Merging Options**: User-configurable merge thresholds
4. **Export Functionality**: Export timestamp data for external tools

### Performance Considerations
- Pattern matching optimized for modal text size
- Deduplication prevents duplicate processing
- Logging can be disabled in production builds
- Memory usage scales linearly with range count

## Troubleshooting

### Common Issues

#### No Multiple Ranges Detected
- **Check Console**: Look for pattern matching logs
- **Verify Modal Text**: Ensure claim modal contains multiple ranges
- **Format Validation**: Check if ranges pass validation rules

#### Format Mismatch Errors
- **Mixed Formats**: Ensure consistent MM:SS or HH:MM:SS usage
- **Invalid Separators**: Use standard dash characters (-, –, —)
- **Part Count**: Verify 2-3 time parts per timestamp

#### UI Display Issues
- **Refresh Extension**: Reload extension after updates
- **Clear Storage**: Reset ClaimCutter state if needed
- **Browser Console**: Check for JavaScript errors

### Debug Commands

```javascript
// Test timestamp parsing in browser console
debugTimestampParsing("Content found in 1:23 - 2:45, 3:10 - 4:20");

// Check modal text content
console.log(document.querySelector('[role="dialog"]')?.textContent);

// Verify pattern matching
const text = "Your modal text here";
const pattern = /(\d{1,2}:\d{2}(?::\d{2})?)\s*[-–—]\s*(\d{1,2}:\d{2}(?::\d{2})?)/g;
console.log([...text.matchAll(pattern)]);
```

## Timeline Positioning Enhancement

### Visual Feedback Issue
Previously, after applying cuts, the timeline needle would remain at the beginning (0:00), making it visually unclear where cuts were actually applied. This created confusion about whether the cuts were successful.

### Solution: Automatic Timeline Positioning
ClaimCutter now automatically positions the timeline needle at the **first cut location** after all cuts are applied, providing clear visual feedback.

#### Implementation Details:
```typescript
// Position timeline at the first cut location for visual feedback
if (result.applied > 0 && ranges.length > 0) {
  console.log('🎯 Positioning timeline at first cut location for visual feedback...');
  try {
    const firstCutStart = ranges[0].start;
    await navigateToTimestamp(firstCutStart);
    console.log(`✅ Timeline positioned at ${firstCutStart}s (first cut location)`);

    // Show visual confirmation of timeline positioning
    showTimelinePositionFeedback(firstCutStart, ranges.length);
  } catch (error) {
    console.warn('⚠️ Failed to position timeline at cut location:', error);
    // Non-critical error - don't fail the entire operation
  }
}
```

#### Visual Notification:
After positioning, ClaimCutter shows a temporary notification:
```
🎯 Timeline Positioned
At 2:31 (first of 3 cuts)
```

### Benefits:
1. **Visual Confirmation**: Users can immediately see where cuts were applied
2. **Quality Assurance**: Easy to verify cut accuracy by observing timeline position
3. **User Experience**: Reduces confusion about cut application success
4. **Non-Intrusive**: Positioning happens automatically without user intervention

### Error Handling:
- Timeline positioning is **non-critical** - if it fails, cuts are still applied successfully
- Errors are logged as warnings, not failures
- The main cut application process continues regardless of positioning issues

## Conclusion

The multiple timestamp ranges enhancement, combined with improved timeline positioning, makes ClaimCutter more robust and capable of handling complex copyright scenarios while maintaining full backward compatibility. The enhanced detection, validation, UI display, and visual feedback provide a comprehensive solution for edge cases that were previously challenging to handle.

This implementation ensures that ClaimCutter can handle any copyright claim structure YouTube Studio presents, from simple single-range claims to complex multi-segment scenarios, all while preventing parsing errors and providing clear user feedback through both console logging and visual timeline positioning.
