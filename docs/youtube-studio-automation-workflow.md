# YouTube Studio Cut Automation Workflow

## Overview

This document comprehensively details the complete workflow for automating cuts in YouTube Studio's editor, based on extensive experimentation and breakthrough discoveries.

## Key Discovery: Auto-Population Behavior

**CRITICAL INSIGHT**: YouTube Studio automatically populates the start time in the cut dialog from the current timeline position when "New Cut" is clicked. This eliminates the need to manually set start times in the dialog.

## Complete Workflow Sequence

### For First Cut
1. **Enter Trim Mode**: Click "Trim & cut" button to enter trimming interface
2. **Set Timeline Position**: Set visible timeline input to start time
3. **Focus Event #1**: Trigger Tab key event to update timeline needle position
4. **Delay**: 500ms for focus event processing + 500ms for timeline update
5. **Click "New Cut"**: Start time automatically populated from timeline position ✨
6. **Set End Time**: Only set end time in cut dialog (start time already populated)
7. **Focus Event #2**: Trigger Tab key event to update end bracket
8. **Delay**: 500ms for focus event + 500ms for timeline bracket update
9. **Click Cut Button**: Apply the cut with checkmark button

### For Subsequent Cuts
**CRITICAL**: After first cut, "New Cut" button becomes disabled if timeline needle is over an already-cut region.

**Required Sequence**:
1. **Set Timeline Position**: Move timeline to new start time position
2. **Focus Event**: Trigger Tab key to update timeline needle
3. **Delay**: Allow timeline to fully update
4. **Check "New Cut" Status**: Verify button is enabled
5. **If Disabled**:
   - Move timeline to safe position (start time + 1 second)
   - Trigger focus event + delay for timeline update
   - **Re-check "New Cut" status** - must be enabled before proceeding
   - If still disabled, throw error
6. **Click "New Cut"**: Only after confirming button is enabled
7. **Continue**: Follow steps 6-9 from first cut workflow

## Technical Implementation Details

### Timeline Input Selection
```javascript
// Visible timeline input (always available)
const timelineInput = document.querySelector('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
```

### Cut Dialog Input Selection
```javascript
// Find all visible timestamp inputs
const allInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
const visibleInputs = Array.from(allInputs).filter(input => input.offsetParent !== null);

// First 2 inputs are dialog inputs, last is timeline toolbar
const startInput = visibleInputs[0]; // Auto-populated from timeline
const endInput = visibleInputs[1];   // Manually set
```

### Focus Event Implementation
```javascript
// Critical for timeline updates
input.dispatchEvent(new KeyboardEvent('keydown', { key: 'Tab', bubbles: true }));
await delay(500); // Essential delay for processing
```

## Experimental Results

### Breakthrough Discovery
- **Timeline Auto-Population**: When "New Cut" is clicked, YouTube Studio automatically carries the current timeline position to the start time field in the cut dialog
- **This eliminates**: Need to manually set start time in dialog
- **This requires**: Proper timeline positioning BEFORE clicking "New Cut"

### Failed Approaches
1. **Setting both start and end times in dialog**: Redundant and error-prone
2. **Multiple focus events on dialog inputs**: Unnecessary complexity
3. **Insufficient delays**: Timeline doesn't update properly without adequate delays

### Successful Approach
1. **Timeline positioning first**: Set visible timeline input to start position
2. **Focus event + delay**: Ensure timeline needle updates
3. **"New Cut" click**: Leverages auto-population behavior
4. **End time only**: Set only end time in dialog
5. **Focus event + delay**: Update end bracket
6. **Cut application**: Apply with checkmark button

## Critical Timing Requirements

### Delay Specifications
- **After timeline focus event**: 500ms minimum
- **Before "New Cut" click**: Additional 500ms for timeline update
- **After end time focus event**: 500ms for processing
- **Before cut button click**: Additional 500ms for bracket update

### Total Timing Per Cut
- **Minimum per cut**: ~3-4 seconds including all delays
- **Recommended**: 4-5 seconds for reliability

## Error Patterns and Solutions

### "New Cut" Button Disabled
**Cause**: Timeline needle positioned over already-cut region
**Solution**: Move timeline to safe position (start time + 1 second)
**Critical Fix**: After moving to safe position, MUST re-check button status before proceeding

### "Expected at least 2 timestamp inputs, found 1"
**Cause**: Cut dialog not properly opened due to disabled "New Cut" button
**Root Cause**: Timeline positioned over cut region, button disabled, dialog never opens
**Solution**:
1. Check "New Cut" button status before clicking
2. If disabled, move to safe position + focus event + delay
3. Re-check button status - must be enabled
4. Only then click "New Cut" to open dialog

### Timeline Brackets Not Updating
**Cause**: Insufficient delays after focus events
**Solution**: Implement proper delay sequence (500ms + 500ms)

### Start Time Not Auto-Populated
**Cause**: Timeline position not set before "New Cut" click
**Solution**: Always set timeline position first with focus event

### Subsequent Cuts Failing After First Success
**Cause**: "New Cut" button disabled due to timeline over cut region
**Critical Discovery**: Moving to safe position alone is insufficient - must verify button is enabled
**Solution**: Implement status verification after safe position movement

## Browser Compatibility Notes

### Chrome Debugger API Integration
- **Required**: For trusted Tab key events
- **Session Management**: Maintain persistent debugger session
- **Event Simulation**: Use chrome.debugger.sendCommand for Tab events

### Accessibility Warnings
- **Expected**: "Blocked aria-hidden" warnings during automation
- **Impact**: None on functionality
- **Cause**: Focus events on hidden elements during dialog transitions

## Performance Optimizations

### Batch Processing
- **Sequential Processing**: Process cuts one at a time
- **State Persistence**: Maintain timeline state between cuts
- **Error Recovery**: Continue processing if individual cuts fail

### Memory Management
- **Session Storage**: Use chrome.storage.session for temporary data
- **Cleanup**: Clear storage after completion
- **Debugger Sessions**: Properly attach/detach debugger

## Future Improvements

### Potential Enhancements
1. **Dynamic Delay Adjustment**: Adapt delays based on system performance
2. **Visual Confirmation**: Wait for visual timeline updates before proceeding
3. **Error Recovery**: Automatic retry for failed cuts
4. **Parallel Processing**: Investigate feasibility of concurrent cuts

### Known Limitations
1. **Manual Intervention**: Some edge cases may require manual intervention
2. **YouTube Updates**: Interface changes may break automation
3. **Performance Dependency**: Timing sensitive to system performance

## Testing Methodology

### Test Scenarios
1. **Single Cut**: Verify basic workflow
2. **Multiple Cuts**: Test subsequent cut handling
3. **Edge Cases**: Overlapping ranges, boundary conditions
4. **Performance**: Various system loads and network conditions

### Validation Criteria
1. **Timeline Accuracy**: Cuts applied at correct timestamps
2. **Visual Confirmation**: Timeline brackets appear correctly
3. **Error Handling**: Graceful failure and recovery
4. **Consistency**: Reliable results across multiple runs

## Conclusion

The breakthrough discovery of YouTube Studio's auto-population behavior has dramatically simplified the cut automation workflow. The key insight that start times are automatically populated from timeline position eliminates the complexity of manual dialog input management and provides a more reliable automation approach.

The critical success factors are:
1. **Proper timeline positioning before dialog opening**
2. **Adequate delays for timeline updates**
3. **Leveraging auto-population behavior**
4. **Sequential processing with state management**

This workflow achieves reliable automation while working with YouTube Studio's built-in behaviors rather than against them.
