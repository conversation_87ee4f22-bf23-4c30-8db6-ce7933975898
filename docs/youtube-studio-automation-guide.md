# YouTube Studio Automation Guide

## 🎉 BREAKTHROUGH: Multi-Cut Workflow SOLVED ✅

**Date**: December 4, 2024
**Status**: Successfully applying ALL cuts in sequence with correct timestamps!

**Key Fix**: Timeline input value clearing - the critical missing piece that enables subsequent cuts to work properly.

## Overview
This document provides comprehensive technical details for automating YouTube Studio copyright claim trimming, including working code examples, DOM selectors, and step-by-step workflows.

## Table of Contents
1. [Copyright Claims Page](#copyright-claims-page)
2. [Editor Page Trim Workflow](#editor-page-trim-workflow)
3. [DOM Selectors Reference](#dom-selectors-reference)
4. [Working Code Examples](#working-code-examples)
5. [Common Issues & Solutions](#common-issues--solutions)

## Copyright Claims Page

### Page Structure
YouTube Studio copyright page uses DIV-based table structure instead of HTML table elements.

### Claim Container Structure
Each copyright claim consists of 4 separate DIV elements:
- **title-text**: Song name
- **artists-text**: Artist name  
- **impact-text**: Monetization status
- **action-buttons**: Contains 'See details' button (8 buttons total)

### Working Selectors
```javascript
// Claim containers (exactly 5 for test video)
const claimContainers = document.querySelectorAll('ytcr-video-content-list-row.style-scope.ytcr-video-content-list');

// Filter for containers with exactly 3 children
const validClaims = Array.from(claimContainers).filter(container => 
  container.children.length === 3
);

// See details buttons
const detailsButtons = document.querySelectorAll('button.ytcp-button-shape-impl[aria-label="See details"]');
```

### Modal Detection for Timestamps
Copyright claims use modal popups instead of page navigation when clicking "See details" buttons.

```javascript
// Wait for modal to appear after clicking "See details"
function waitForModal() {
  return new Promise((resolve) => {
    const checkModal = () => {
      const modal = document.querySelector('[role="dialog"]') || 
                   document.querySelector('.ytcp-dialog') ||
                   document.querySelector('[aria-modal="true"]');
      
      if (modal && modal.offsetParent !== null) {
        resolve(modal);
      } else {
        setTimeout(checkModal, 100);
      }
    };
    checkModal();
  });
}

// Extract timestamps from modal
function extractTimestampsFromModal() {
  const timestampRegex = /(\d{1,2}:\d{2})\s*-\s*(\d{1,2}:\d{2})/g;
  const modalText = document.body.innerText;
  const matches = [...modalText.matchAll(timestampRegex)];
  
  return matches.map(match => ({
    start: match[1],
    end: match[2],
    raw: match[0]
  }));
}
```

## Editor Page Trim Workflow

### Complete Working Workflow

#### Step 1: Enter Trim Mode
```javascript
// Find and click 'Trim & cut' button
const trimButton = Array.from(document.querySelectorAll('a')).find(a => 
  a.textContent?.trim() === 'Trim & cut' && 
  a.classList.contains('style-scope') && 
  a.classList.contains('ytve-entrypoint-options-panel')
);

if (trimButton) {
  trimButton.click();
  console.log('✓ Entered trim mode');
}
```

#### Step 2: Start New Cut
```javascript
// Wait for trim mode to load, then click 'New Cut'
setTimeout(() => {
  const newCutButton = document.querySelector('ytcp-button.style-scope.ytve-trim-options-panel');
  if (newCutButton && !newCutButton.hasAttribute('disabled')) {
    newCutButton.click();
    console.log('✓ Started new cut');
  }
}, 1000);
```

#### Step 3: Input Start Time
```javascript
// Find first timestamp input and enter start time
const timestampInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
const startInput = timestampInputs[0];

if (startInput) {
  startInput.select();
  startInput.focus();
  startInput.value = '2:30'; // MM:SS format
  console.log('✓ Entered start time: 2:30');
}
```

#### Step 4: Input End Time
```javascript
// Find second timestamp input and enter end time
const endInput = timestampInputs[1];

if (endInput) {
  endInput.select();
  endInput.focus();
  endInput.value = '4:41'; // MM:SS format
  console.log('✓ Entered end time: 4:41');
}
```

#### Step 5: Save Cut
```javascript
// Find visible Cut button - CRITICAL: Multiple Cut buttons exist in DOM
const allIconButtons = document.querySelectorAll('ytcp-icon-button');
const cutButton = Array.from(allIconButtons).find(btn => 
  btn.offsetParent !== null && btn.getAttribute('aria-label') === 'Cut'
);

if (cutButton) {
  cutButton.click();
  console.log('✓ Cut saved successfully');
} else {
  console.error('✗ Cut button not found or not visible');
}
```

#### Step 6: Prepare for Next Cut
```javascript
// Input next start time to enable New Cut button
const visibleInput = Array.from(timestampInputs).find(input => input.offsetParent !== null);

if (visibleInput) {
  visibleInput.select();
  visibleInput.focus();
  visibleInput.value = '7:20'; // Next cut start time
  console.log('✓ Prepared for next cut');
  
  // New Cut button should now be enabled
  setTimeout(() => {
    const newCutBtn = document.querySelector('ytcp-button.style-scope.ytve-trim-options-panel');
    console.log('New Cut button enabled:', !newCutBtn.hasAttribute('disabled'));
  }, 1000);
}
```

### Complete Automation Function
```javascript
async function applyCuts(timestamps) {
  // Enter trim mode
  const trimButton = Array.from(document.querySelectorAll('a')).find(a => 
    a.textContent?.trim() === 'Trim & cut'
  );
  trimButton?.click();
  
  await delay(2000);
  
  for (let i = 0; i < timestamps.length; i++) {
    const { start, end } = timestamps[i];
    
    // Click New Cut button
    const newCutButton = document.querySelector('ytcp-button.style-scope.ytve-trim-options-panel');
    newCutButton?.click();
    
    await delay(1000);
    
    // Input start and end times
    const inputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
    
    if (inputs[0]) {
      inputs[0].select();
      inputs[0].value = start;
    }
    
    if (inputs[1]) {
      inputs[1].select();
      inputs[1].value = end;
    }
    
    await delay(500);
    
    // Save cut
    const cutButton = Array.from(document.querySelectorAll('ytcp-icon-button')).find(btn => 
      btn.offsetParent !== null && btn.getAttribute('aria-label') === 'Cut'
    );
    cutButton?.click();
    
    await delay(2000);
    
    // Prepare for next cut (if not last)
    if (i < timestamps.length - 1) {
      const nextStart = timestamps[i + 1].start;
      const visibleInput = Array.from(inputs).find(input => input.offsetParent !== null);
      if (visibleInput) {
        visibleInput.select();
        visibleInput.value = nextStart;
      }
      await delay(1000);
    }
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
```

## DOM Selectors Reference

### Copyright Claims Page Selectors

| Element | Selector | Notes |
|---------|----------|-------|
| Claim containers | `ytcr-video-content-list-row.style-scope.ytcr-video-content-list` | Filter for 3 children |
| See details buttons | `button.ytcp-button-shape-impl[aria-label="See details"]` | Opens modal popup |
| Modal dialog | `[role="dialog"]`, `[aria-modal="true"]` | Contains timestamp text |

### Editor Page Selectors

| Element | Selector | Notes |
|---------|----------|-------|
| Trim & cut button | `a` with text "Trim & cut" + classes | Entry point to trim mode |
| New Cut button | `ytcp-button.style-scope.ytve-trim-options-panel` | Starts new cut creation |
| Timestamp inputs | `input.style-scope.ytcp-media-timestamp-input[role="timer"]` | [0] = start, [1] = end |
| Cut button | `ytcp-icon-button[aria-label="Cut"]` | Must check `offsetParent !== null` |
| Save button | `button` with text "Save" | Final save after all cuts |

### Selector Validation Code
```javascript
// Validate all critical selectors exist
function validateSelectors() {
  const selectors = {
    claimContainers: 'ytcr-video-content-list-row.style-scope.ytcr-video-content-list',
    detailsButtons: 'button.ytcp-button-shape-impl[aria-label="See details"]',
    trimButton: 'a', // Filter by text content
    newCutButton: 'ytcp-button.style-scope.ytve-trim-options-panel',
    timestampInputs: 'input.style-scope.ytcp-media-timestamp-input[role="timer"]',
    cutButtons: 'ytcp-icon-button[aria-label="Cut"]'
  };

  const results = {};

  for (const [name, selector] of Object.entries(selectors)) {
    const elements = document.querySelectorAll(selector);
    results[name] = {
      found: elements.length,
      selector: selector,
      elements: Array.from(elements)
    };
  }

  return results;
}
```

## Working Code Examples

### Timestamp Collection from Copyright Page
```javascript
async function collectAllTimestamps() {
  const timestamps = [];
  const detailsButtons = document.querySelectorAll('button.ytcp-button-shape-impl[aria-label="See details"]');

  for (let i = 0; i < detailsButtons.length; i++) {
    console.log(`Processing claim ${i + 1}/${detailsButtons.length}`);

    // Click details button
    detailsButtons[i].click();

    // Wait for modal
    await waitForModal();

    // Extract timestamps
    const claimTimestamps = extractTimestampsFromModal();
    timestamps.push(...claimTimestamps);

    // Close modal (ESC key)
    document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));

    await delay(1000);
  }

  return timestamps;
}
```

### Timestamp Format Conversion
```javascript
function convertTimestamp(timeStr) {
  // Convert various formats to MM:SS for YouTube Studio
  const parts = timeStr.split(':');

  if (parts.length === 2) {
    // Already MM:SS format
    return timeStr;
  } else if (parts.length === 3) {
    // HH:MM:SS format - convert to MM:SS if under 1 hour
    const hours = parseInt(parts[0]);
    const minutes = parseInt(parts[1]);
    const seconds = parts[2];

    if (hours === 0) {
      return `${minutes}:${seconds}`;
    } else {
      // Keep HH:MM:SS for videos over 1 hour
      return timeStr;
    }
  }

  return timeStr; // Return as-is if unknown format
}
```

### Timestamp Overlap Detection and Merging
```javascript
function mergeOverlappingTimestamps(timestamps) {
  const sorted = timestamps.sort((a, b) => timeToSeconds(a.start) - timeToSeconds(b.start));
  const merged = [];

  for (const current of sorted) {
    if (merged.length === 0) {
      merged.push(current);
      continue;
    }

    const last = merged[merged.length - 1];
    const lastEnd = timeToSeconds(last.end);
    const currentStart = timeToSeconds(current.start);

    // Check for overlap (with 1-second buffer)
    if (currentStart <= lastEnd + 1) {
      // Merge timestamps
      const mergedEnd = Math.max(lastEnd, timeToSeconds(current.end));
      last.end = secondsToTime(mergedEnd);
      last.sources = [...(last.sources || [last.raw]), current.raw];
      last.merged = true;
    } else {
      merged.push(current);
    }
  }

  return merged;
}

function timeToSeconds(timeStr) {
  const parts = timeStr.split(':').map(Number);
  return parts.length === 2 ? parts[0] * 60 + parts[1] : parts[0] * 3600 + parts[1] * 60 + parts[2];
}

function secondsToTime(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}
```

## Common Issues & Solutions

### Issue 1: Cut Button Not Found
**Problem**: `Cut button not found or not visible`
**Solution**: Multiple Cut buttons exist in DOM. Must find the visible one:

```javascript
// WRONG - finds wrong button
const cutButton = document.querySelector('ytcp-icon-button[aria-label="Cut"]');

// CORRECT - finds visible button
const allIconButtons = document.querySelectorAll('ytcp-icon-button');
const cutButton = Array.from(allIconButtons).find(btn =>
  btn.offsetParent !== null && btn.getAttribute('aria-label') === 'Cut'
);
```

### Issue 2: New Cut Button Disabled
**Problem**: New Cut button becomes disabled after first cut
**Solution**: Input next start time to move timeline needle:

```javascript
// Move timeline to next cut position
const visibleInput = Array.from(timestampInputs).find(input => input.offsetParent !== null);
visibleInput.select();
visibleInput.value = nextStartTime; // This enables New Cut button
```

### Issue 3: Timestamp Input Not Accepting Values
**Problem**: Input fields don't accept direct numeric input
**Solution**: Must select and focus before setting value:

```javascript
// WRONG
input.value = '2:30';

// CORRECT
input.select();
input.focus();
input.value = '2:30';
```

### Issue 4: Modal Not Detected
**Problem**: Modal detection fails after clicking "See details"
**Solution**: Use multiple selector fallbacks and wait:

```javascript
function waitForModal() {
  return new Promise((resolve) => {
    const checkModal = () => {
      const modal = document.querySelector('[role="dialog"]') ||
                   document.querySelector('.ytcp-dialog') ||
                   document.querySelector('[aria-modal="true"]') ||
                   document.querySelector('ytcp-dialog');

      if (modal && modal.offsetParent !== null) {
        resolve(modal);
      } else {
        setTimeout(checkModal, 100);
      }
    };
    checkModal();
  });
}
```

## Implementation Notes

### Timing and Delays
- **Modal wait**: 100ms intervals until modal appears
- **Between cuts**: 2000ms for cut to be applied
- **Input delays**: 500ms after entering timestamps
- **Button clicks**: 1000ms between major actions

### Error Handling
```javascript
function safeClick(element, description) {
  if (!element) {
    console.error(`✗ ${description} not found`);
    return false;
  }

  if (element.offsetParent === null) {
    console.error(`✗ ${description} not visible`);
    return false;
  }

  try {
    element.click();
    console.log(`✓ Clicked ${description}`);
    return true;
  } catch (error) {
    console.error(`✗ Failed to click ${description}:`, error);
    return false;
  }
}
```

### Debug Utilities
```javascript
// Log all available buttons for debugging
function debugButtons() {
  const buttons = document.querySelectorAll('ytcp-icon-button');
  console.log('Available ytcp-icon-button elements:');

  Array.from(buttons).forEach((btn, i) => {
    const ariaLabel = btn.getAttribute('aria-label');
    const visible = btn.offsetParent !== null;

    if (ariaLabel || visible) {
      console.log(`${i + 1}: ${ariaLabel || 'null'} ${visible ? '(visible)' : '(hidden)'}`);
    }
  });
}

// Check timestamp input states
function debugTimestampInputs() {
  const inputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  console.log('Timestamp inputs:');

  Array.from(inputs).forEach((input, i) => {
    console.log(`Input ${i + 1}:`, {
      value: input.value,
      visible: input.offsetParent !== null,
      focused: document.activeElement === input
    });
  });
}
```

### Chrome Extension Integration
```javascript
// Message passing between content script and background
chrome.runtime.sendMessage({
  action: 'APPLY_CUTS',
  data: { timestamps: processedTimestamps }
}, (response) => {
  if (response.success) {
    console.log('✓ Cuts applied successfully');
  } else {
    console.error('✗ Failed to apply cuts:', response.error);
  }
});

// Storage for persistence
chrome.storage.session.set({
  'collected_timestamps': timestamps,
  'processing_status': 'completed'
});
```

## Testing Checklist

### Before Implementation
- [ ] Verify all selectors exist on target page
- [ ] Test timestamp extraction from modal
- [ ] Confirm trim mode entry works
- [ ] Validate Cut button detection

### During Development
- [ ] Test with single timestamp first
- [ ] Verify New Cut button state changes
- [ ] Check timestamp format conversion
- [ ] Test overlap detection and merging

### Final Validation
- [ ] Test complete workflow end-to-end
- [ ] Verify all cuts appear in timeline
- [ ] Confirm Save button functionality
- [ ] Test error recovery scenarios

---

*This guide documents the complete working implementation for YouTube Studio copyright claim automation as of the current testing session.*
