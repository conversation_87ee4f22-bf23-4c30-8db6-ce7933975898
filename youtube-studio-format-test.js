// YouTube Studio Timestamp Format Investigation
// Run this in browser devtools console on YouTube Studio editor page

console.log('🔍 YOUTUBE STUDIO TIMESTAMP FORMAT INVESTIGATION');
console.log('================================================');

// Function to analyze existing timestamp inputs
function analyzeTimestampInputs() {
  console.log('\n📋 ANALYZING EXISTING TIMESTAMP INPUTS:');
  
  const inputs = document.querySelectorAll('input[role="timer"], input.ytcp-media-timestamp-input');
  console.log(`Found ${inputs.length} timestamp input(s)`);
  
  inputs.forEach((input, i) => {
    const value = input.value;
    const placeholder = input.placeholder;
    const ariaLabel = input.getAttribute('aria-label');
    
    console.log(`\nInput ${i + 1}:`);
    console.log(`  Value: "${value}"`);
    console.log(`  Placeholder: "${placeholder}"`);
    console.log(`  Aria-label: "${ariaLabel}"`);
    console.log(`  Classes: ${input.className}`);
    
    if (value && value.includes(':')) {
      const parts = value.split(':');
      console.log(`  Parts: ${parts.length} (${parts.join(' | ')})`);
      
      if (parts.length === 4) {
        console.log(`  ✅ Format: HH:MM:SS:FF`);
      } else if (parts.length === 3) {
        console.log(`  ✅ Format: MM:SS:FF or HH:MM:SS`);
      } else if (parts.length === 2) {
        console.log(`  ✅ Format: MM:SS`);
      }
    }
  });
}

// Function to test different timestamp formats
function testTimestampFormats() {
  console.log('\n🧪 TESTING TIMESTAMP FORMAT ACCEPTANCE:');
  
  const inputs = document.querySelectorAll('input[role="timer"], input.ytcp-media-timestamp-input');
  if (inputs.length === 0) {
    console.log('❌ No timestamp inputs found. Make sure you\'re on the YouTube Studio editor page.');
    return;
  }
  
  const testInput = inputs[0];
  const originalValue = testInput.value;
  
  const testFormats = [
    '02:31:06:00',    // HH:MM:SS:FF
    '02:31:06',       // HH:MM:SS
    '151:06:00',      // MM:SS:FF (total minutes)
    '151:06',         // MM:SS (total minutes)
    '31:06:00',       // MM:SS:FF (regular minutes)
    '31:06'           // MM:SS (regular minutes)
  ];
  
  console.log('Testing formats on first input...');
  
  testFormats.forEach((format, i) => {
    setTimeout(() => {
      console.log(`\nTest ${i + 1}: "${format}"`);
      
      // Set the value
      testInput.focus();
      testInput.value = format;
      testInput.dispatchEvent(new Event('input', { bubbles: true }));
      testInput.dispatchEvent(new Event('change', { bubbles: true }));
      
      setTimeout(() => {
        const newValue = testInput.value;
        const accepted = newValue === format;
        const transformed = newValue !== format;
        
        console.log(`  Input: "${format}"`);
        console.log(`  Result: "${newValue}"`);
        console.log(`  Status: ${accepted ? '✅ ACCEPTED' : transformed ? '🔄 TRANSFORMED' : '❌ REJECTED'}`);
        
        // Restore original value after last test
        if (i === testFormats.length - 1) {
          setTimeout(() => {
            testInput.value = originalValue;
            testInput.dispatchEvent(new Event('input', { bubbles: true }));
            testInput.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`\n🔄 Restored original value: "${originalValue}"`);
          }, 500);
        }
      }, 200);
    }, i * 1000);
  });
}

// Function to detect video duration and expected format
function detectVideoInfo() {
  console.log('\n📹 VIDEO INFORMATION:');
  
  // Try to find video duration from various sources
  const durationSources = [
    () => document.querySelector('video')?.duration,
    () => {
      const timeDisplay = document.querySelector('.ytp-time-duration');
      return timeDisplay ? timeDisplay.textContent : null;
    },
    () => {
      const progressBar = document.querySelector('.ytp-progress-bar');
      return progressBar ? progressBar.getAttribute('aria-valuemax') : null;
    }
  ];
  
  let duration = null;
  for (const source of durationSources) {
    try {
      duration = source();
      if (duration) break;
    } catch (e) {
      // Continue to next source
    }
  }
  
  if (duration) {
    const durationSeconds = typeof duration === 'string' ? parseFloat(duration) : duration;
    const hours = Math.floor(durationSeconds / 3600);
    const minutes = Math.floor((durationSeconds % 3600) / 60);
    const seconds = Math.floor(durationSeconds % 60);
    
    console.log(`Duration: ${durationSeconds} seconds`);
    console.log(`Formatted: ${hours}h ${minutes}m ${seconds}s`);
    console.log(`Expected format: ${durationSeconds > 3600 ? 'HH:MM:SS:FF' : 'MM:SS:FF'}`);
  } else {
    console.log('❌ Could not detect video duration');
  }
}

// Function to check current page context
function checkPageContext() {
  console.log('\n🌐 PAGE CONTEXT:');
  console.log(`URL: ${window.location.href}`);
  console.log(`Title: ${document.title}`);
  
  const isEditor = window.location.href.includes('/editor');
  const isCopyright = window.location.href.includes('/copyright');
  const isUpload = window.location.href.includes('/upload');
  
  console.log(`Editor page: ${isEditor ? '✅' : '❌'}`);
  console.log(`Copyright page: ${isCopyright ? '✅' : '❌'}`);
  console.log(`Upload page: ${isUpload ? '✅' : '❌'}`);
  
  if (!isEditor && !isCopyright && !isUpload) {
    console.log('⚠️  You might not be on a YouTube Studio page with timestamp inputs');
  }
}

// Main execution
function runInvestigation() {
  console.log('Starting YouTube Studio timestamp format investigation...\n');
  
  checkPageContext();
  detectVideoInfo();
  analyzeTimestampInputs();
  
  console.log('\n⏳ Starting format acceptance tests in 2 seconds...');
  console.log('(This will temporarily modify input values to test acceptance)');
  
  setTimeout(() => {
    testTimestampFormats();
  }, 2000);
}

// Export functions for manual use
window.ytStudioTest = {
  runInvestigation,
  analyzeTimestampInputs,
  testTimestampFormats,
  detectVideoInfo,
  checkPageContext
};

console.log('\n🚀 READY TO RUN!');
console.log('Execute: runInvestigation()');
console.log('Or run individual functions:');
console.log('- analyzeTimestampInputs()');
console.log('- testTimestampFormats()');
console.log('- detectVideoInfo()');
console.log('- checkPageContext()');

// Auto-run the investigation
runInvestigation();
