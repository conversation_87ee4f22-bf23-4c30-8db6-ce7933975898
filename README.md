# User Guide: Augment Code Memory Bank System

## 1. Overview

Welcome to the Augment Code Memory Bank System! This system helps your AI coding assistant (Augment Code) maintain context, learn about your project, and work more effectively with you over time. It's built around a structured set of Markdown files (the "Memory Bank") that the AI reads and updates.

The detailed operational rules for the AI are in [`rules_and_protocols.md`](rules_and_protocols.md). This guide helps you, the user, initialize and interact with an AI using this system.

## 2. Initialization

To set up the Memory Bank system for your project, you can ask your Augment Code AI to do it for you.

Simply give a command like:
`"Initialize memory bank"`
`"Start memory bank for this project"`
`"Build the memory bank structure"`

The AI, following the protocols in `rules_and_protocols.md`, will then:
1.  Create the `.memory-bank/` directory at your project root if it doesn't exist.
2.  Create the empty core Memory Bank files inside `.memory-bank/`:
    *   `projectbrief.md`
    *   `productContext.md`
    *   `systemPatterns.md`
    *   `techContext.md`
    *   `activeContext.md`
    *   `progress.md`
    *   `project_intelligence.md`
    *   `rules_and_protocols.md` (as an empty file).
3.  Confirm the creation and then prompt you for the next crucial steps, including helping with `.augment-guidelines`.

### 2.1. Populate Initial Content (Crucial First Step After AI Initialization)

*   After the AI has created the structure, it will prompt you to provide content for **`.memory-bank/projectbrief.md`**. This is the most important file to start with. Define:
    *   What is the project's main goal?
    *   What are the core requirements?
    *   What is the overall scope?
*   The AI will also remind you to ensure the full content of the master `rules_and_protocols.md` (like the one in the `AugMemoryBank` folder) is copied into `.memory-bank/rules_and_protocols.md`.
*   You can add initial thoughts to other files like `productContext.md` and `techContext.md`, or let the AI help develop them over time.

### 2.2. Ensuring `.augment-guidelines` Supports Memory Bank

Augment Code uses an `.augment-guidelines` file at the project root for workspace-specific instructions. To ensure this file reinforces the Memory Bank system:

*   **AI-Assisted Setup:** During the AI-driven initialization (when you ask to "initialize memory bank"), the AI is instructed (per `rules_and_protocols.md`) to offer to create or update your project's `.augment-guidelines` file.
*   **Critical Rule:** The AI will suggest adding the following essential rule:
    ```
    - Critical: This project uses a Memory Bank. Always operate according to '.memory-bank/rules_and_protocols.md'. Read it first for any complex task.
    ```
*   **User Approval:** If you approve, the AI will attempt to add this rule. If the file already exists, the AI should append it carefully, preserving your existing guidelines.
*   **Manual Check:** It's always good practice to verify the contents of `.augment-guidelines` yourself after this step to ensure it meets your needs and stays within any character limits.

## 3. Using the Memory Bank System

### 3.1. AI's Role

*   **Reading:** The AI is instructed to read all Memory Bank files at the start of a new task to gain full context.
*   **Updating:** The AI will update files like `activeContext.md`, `progress.md`, and `project_intelligence.md` as work progresses and new information is learned.
*   **Planning (PLAN Mode):** For complex tasks, the AI will enter PLAN mode. It will present:
    *   Files it's reviewing.
    *   Items it's considering for the plan.
    *   A `File operation plan` if file changes are needed.
    *   It will **always** ask for your approval before acting.
*   **Executing (ACT Mode):** Once you approve a plan, the AI enters ACT mode to implement the changes.

### 3.2. Your Role

*   **Provide Clear Tasks:** The more context you give in your initial requests, the better the AI can utilize and update the Memory Bank.
*   **Review Plans:** Carefully review the AI's proposals in PLAN mode. Your approval is essential.
*   **Guide Updates:** If you notice the Memory Bank is missing crucial information or is outdated, you can instruct the AI:
    *   Example: `"Update the memory bank: we've decided to switch to PostgreSQL for the database."`
    *   Example: `"Ensure project_intelligence.md captures our new standard for API error handling."`
*   **Maintain `projectbrief.md`:** While the AI helps with most files, `projectbrief.md` often benefits from direct user input, especially if project goals shift significantly.

## 4. Invoking the System

"Invoking" the system primarily means interacting with an AI that is configured to follow these protocols.

*   **Standard Interaction:** Simply give tasks to your Augment Code AI as usual. If it's set up with these rules (e.g., via its master prompt or specific mode configurations that incorporate these rules), it will automatically try to use the Memory Bank.
*   **Initial Task:** When starting a new project or a major new feature, your first interaction might be:
    `"Let's set up the project structure for [project name]. I've initialized projectbrief.md in .memory-bank/. Please review it and help me outline the productContext.md and techContext.md."`
*   **Complex Tasks:** When you give a complex task, expect the AI to enter PLAN mode.
    `"Develop a user authentication feature with email/password login and OAuth2 via Google."`
    The AI should then respond with its plan, referencing the Memory Bank.
*   **Explicit Memory Bank Update:**
    `"Update memory bank"` - This command, as per `rules_and_protocols.md`, should trigger the AI to review all Memory Bank files and ensure its understanding is current, potentially asking you clarifying questions.

By following these guidelines, you and your Augment Code AI can build a rich, evolving understanding of your project, leading to more efficient and context-aware development.