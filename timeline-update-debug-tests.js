/**
 * Comprehensive Timeline Update Debug Tests
 * 
 * This script contains systematic tests to identify what triggers
 * YouTube Studio timeline updates after programmatic timestamp changes.
 * 
 * Usage: Copy and paste each test function into browser devtools console
 * Run tests one at a time and observe timeline bracket behavior
 */

// =============================================================================
// TEST 1: COMPREHENSIVE CLICK EVENT SIMULATION
// =============================================================================

function test1_ClickEventVariations() {
  console.log('🧪 TEST 1: Click Event Variations');
  
  const timestampInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  const visibleInputs = Array.from(timestampInputs).filter(input => input.offsetParent !== null);
  
  if (visibleInputs.length < 2) {
    console.log('❌ Need at least 2 visible timestamp inputs');
    return;
  }
  
  const startInput = visibleInputs[0];
  
  // Set the value first
  startInput.focus();
  startInput.value = '3:15:00';
  startInput.dispatchEvent(new Event('input', { bubbles: true }));
  console.log('✅ Value set to 3:15:00');
  
  // Test different click targets and event types
  const clickTargets = [
    // Main editor areas
    document.querySelector('.style-scope.ytve-editor'),
    document.querySelector('.style-scope.ytve-trim-options-panel'),
    document.querySelector('#movie_player'),
    
    // Timeline areas
    document.querySelector('[role="slider"]'),
    document.querySelector('.ytp-chrome-bottom'),
    
    // General areas
    document.querySelector('body'),
    document.querySelector('#content')
  ];
  
  let testIndex = 0;
  
  function runNextClickTest() {
    if (testIndex >= clickTargets.length) {
      console.log('🏁 All click tests completed');
      return;
    }
    
    const target = clickTargets[testIndex];
    const targetName = target?.className || target?.tagName || 'unknown';
    
    console.log(`\n🎯 Click Test ${testIndex + 1}: ${targetName}`);
    
    if (target) {
      // Test different mouse event combinations
      const events = [
        // Standard click
        () => target.click(),
        
        // Mouse down/up sequence
        () => {
          target.dispatchEvent(new MouseEvent('mousedown', { bubbles: true, cancelable: true }));
          setTimeout(() => {
            target.dispatchEvent(new MouseEvent('mouseup', { bubbles: true, cancelable: true }));
            target.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));
          }, 50);
        },
        
        // Focus event
        () => {
          if (target.focus) target.focus();
          else target.dispatchEvent(new FocusEvent('focus', { bubbles: true }));
        }
      ];
      
      // Try each event type
      events[testIndex % events.length]();
      
      setTimeout(() => {
        console.log(`   Result: Check timeline brackets now`);
        testIndex++;
        setTimeout(runNextClickTest, 2000); // Wait 2s between tests
      }, 500);
    } else {
      console.log(`   ❌ Target not found`);
      testIndex++;
      setTimeout(runNextClickTest, 100);
    }
  }
  
  runNextClickTest();
}

// =============================================================================
// TEST 2: FOCUS/BLUR EVENT COMBINATIONS
// =============================================================================

function test2_FocusBlurEvents() {
  console.log('\n🧪 TEST 2: Focus/Blur Event Combinations');
  
  const timestampInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  const visibleInputs = Array.from(timestampInputs).filter(input => input.offsetParent !== null);
  
  if (visibleInputs.length < 2) {
    console.log('❌ Need at least 2 visible timestamp inputs');
    return;
  }
  
  const startInput = visibleInputs[0];
  
  // Set the value first
  startInput.focus();
  startInput.value = '4:22:00';
  startInput.dispatchEvent(new Event('input', { bubbles: true }));
  console.log('✅ Value set to 4:22:00');
  
  // Test sequence 1: Blur then focus elsewhere
  setTimeout(() => {
    console.log('🔄 Test 2a: Blur input');
    startInput.blur();
    startInput.dispatchEvent(new Event('blur', { bubbles: true }));
    startInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    setTimeout(() => {
      console.log('🔄 Test 2b: Focus on different element');
      const otherElement = document.querySelector('body') || document.querySelector('#content');
      if (otherElement && otherElement.focus) {
        otherElement.focus();
      }
      
      setTimeout(() => {
        console.log('🔄 Test 2c: Focus back on input');
        startInput.focus();
        
        console.log('   Result: Check timeline brackets now');
      }, 1000);
    }, 1000);
  }, 1000);
}

// =============================================================================
// TEST 3: KEYBOARD EVENT SEQUENCES
// =============================================================================

function test3_KeyboardEvents() {
  console.log('\n🧪 TEST 3: Keyboard Event Sequences');
  
  const timestampInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  const visibleInputs = Array.from(timestampInputs).filter(input => input.offsetParent !== null);
  
  if (visibleInputs.length < 2) {
    console.log('❌ Need at least 2 visible timestamp inputs');
    return;
  }
  
  const startInput = visibleInputs[0];
  
  // Set the value first
  startInput.focus();
  startInput.value = '5:33:00';
  startInput.dispatchEvent(new Event('input', { bubbles: true }));
  console.log('✅ Value set to 5:33:00');
  
  const keyTests = [
    { key: 'Tab', keyCode: 9, description: 'Tab key' },
    { key: 'Enter', keyCode: 13, description: 'Enter key' },
    { key: 'Escape', keyCode: 27, description: 'Escape key' },
    { key: 'ArrowUp', keyCode: 38, description: 'Arrow Up' },
    { key: 'ArrowDown', keyCode: 40, description: 'Arrow Down' },
    { key: 'ArrowLeft', keyCode: 37, description: 'Arrow Left' },
    { key: 'ArrowRight', keyCode: 39, description: 'Arrow Right' }
  ];
  
  let keyTestIndex = 0;
  
  function runNextKeyTest() {
    if (keyTestIndex >= keyTests.length) {
      console.log('🏁 All keyboard tests completed');
      return;
    }
    
    const test = keyTests[keyTestIndex];
    console.log(`\n⌨️  Key Test ${keyTestIndex + 1}: ${test.description}`);
    
    // Ensure input is focused
    startInput.focus();
    
    // Dispatch key events
    startInput.dispatchEvent(new KeyboardEvent('keydown', {
      key: test.key,
      keyCode: test.keyCode,
      bubbles: true,
      cancelable: true
    }));
    
    startInput.dispatchEvent(new KeyboardEvent('keyup', {
      key: test.key,
      keyCode: test.keyCode,
      bubbles: true,
      cancelable: true
    }));
    
    // Also trigger change event
    startInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    setTimeout(() => {
      console.log(`   Result: Check timeline brackets now`);
      keyTestIndex++;
      setTimeout(runNextKeyTest, 2000);
    }, 500);
  }
  
  runNextKeyTest();
}

// =============================================================================
// TEST 4: CUSTOM EVENT DISPATCH
// =============================================================================

function test4_CustomEvents() {
  console.log('\n🧪 TEST 4: Custom Event Dispatch');
  
  const timestampInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  const visibleInputs = Array.from(timestampInputs).filter(input => input.offsetParent !== null);
  
  if (visibleInputs.length < 2) {
    console.log('❌ Need at least 2 visible timestamp inputs');
    return;
  }
  
  const startInput = visibleInputs[0];
  
  // Set the value first
  startInput.focus();
  startInput.value = '6:44:00';
  startInput.dispatchEvent(new Event('input', { bubbles: true }));
  console.log('✅ Value set to 6:44:00');
  
  const customEvents = [
    'timeupdate',
    'seeking',
    'seeked',
    'loadedmetadata',
    'canplay',
    'timelineupdate',
    'positionchange',
    'valuechange'
  ];
  
  let eventIndex = 0;
  
  function runNextCustomEvent() {
    if (eventIndex >= customEvents.length) {
      console.log('🏁 All custom event tests completed');
      return;
    }
    
    const eventName = customEvents[eventIndex];
    console.log(`\n🎪 Custom Event Test ${eventIndex + 1}: ${eventName}`);
    
    // Dispatch on input
    startInput.dispatchEvent(new CustomEvent(eventName, { 
      bubbles: true, 
      detail: { timestamp: '6:44:00' }
    }));
    
    // Also dispatch on document and window
    document.dispatchEvent(new CustomEvent(eventName, { 
      bubbles: true, 
      detail: { timestamp: '6:44:00' }
    }));
    
    setTimeout(() => {
      console.log(`   Result: Check timeline brackets now`);
      eventIndex++;
      setTimeout(runNextCustomEvent, 2000);
    }, 500);
  }
  
  runNextCustomEvent();
}

// =============================================================================
// TEST 5: DOM MUTATION AND FORM EVENTS
// =============================================================================

function test5_DOMAndFormEvents() {
  console.log('\n🧪 TEST 5: DOM Mutation and Form Events');
  
  const timestampInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  const visibleInputs = Array.from(timestampInputs).filter(input => input.offsetParent !== null);
  
  if (visibleInputs.length < 2) {
    console.log('❌ Need at least 2 visible timestamp inputs');
    return;
  }
  
  const startInput = visibleInputs[0];
  
  // Set the value first
  startInput.focus();
  startInput.value = '7:55:00';
  startInput.dispatchEvent(new Event('input', { bubbles: true }));
  console.log('✅ Value set to 7:55:00');
  
  const tests = [
    {
      name: 'Form submit event',
      action: () => {
        const form = startInput.closest('form');
        if (form) {
          form.dispatchEvent(new Event('submit', { bubbles: true }));
        } else {
          console.log('   No form found');
        }
      }
    },
    {
      name: 'Attribute mutation',
      action: () => {
        const oldValue = startInput.getAttribute('value');
        startInput.setAttribute('value', '7:55:00');
        startInput.dispatchEvent(new Event('DOMAttrModified', { bubbles: true }));
        setTimeout(() => startInput.setAttribute('value', oldValue || ''), 100);
      }
    },
    {
      name: 'Property change event',
      action: () => {
        startInput.dispatchEvent(new Event('propertychange', { bubbles: true }));
      }
    },
    {
      name: 'Selection change',
      action: () => {
        startInput.select();
        document.dispatchEvent(new Event('selectionchange'));
      }
    }
  ];
  
  let testIndex = 0;
  
  function runNextDOMTest() {
    if (testIndex >= tests.length) {
      console.log('🏁 All DOM/Form tests completed');
      return;
    }
    
    const test = tests[testIndex];
    console.log(`\n🔧 DOM Test ${testIndex + 1}: ${test.name}`);
    
    test.action();
    
    setTimeout(() => {
      console.log(`   Result: Check timeline brackets now`);
      testIndex++;
      setTimeout(runNextDOMTest, 2000);
    }, 500);
  }
  
  runNextDOMTest();
}

// =============================================================================
// MASTER TEST RUNNER
// =============================================================================

function runAllTimelineTests() {
  console.log('🚀 STARTING COMPREHENSIVE TIMELINE UPDATE TESTS');
  console.log('📋 This will run 5 test suites systematically');
  console.log('👀 Watch the timeline brackets after each test');
  console.log('⏱️  Each test waits 2 seconds before proceeding');
  console.log('\n' + '='.repeat(60));
  
  // Run tests in sequence
  test1_ClickEventVariations();
  
  setTimeout(() => {
    test2_FocusBlurEvents();
  }, 20000); // Wait for test 1 to complete
  
  setTimeout(() => {
    test3_KeyboardEvents();
  }, 30000);
  
  setTimeout(() => {
    test4_CustomEvents();
  }, 45000);
  
  setTimeout(() => {
    test5_DOMAndFormEvents();
  }, 60000);
}

// =============================================================================
// QUICK INDIVIDUAL TESTS (for rapid iteration)
// =============================================================================

function quickTest_ClickBody() {
  console.log('🚀 QUICK TEST: Click body after value change');
  const input = document.querySelector('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  if (input) {
    input.focus();
    input.value = '8:00:00';
    input.dispatchEvent(new Event('input', { bubbles: true }));
    setTimeout(() => {
      document.body.click();
      console.log('✅ Clicked body - check timeline now');
    }, 500);
  }
}

function quickTest_TabKey() {
  console.log('🚀 QUICK TEST: Tab key after value change');
  const input = document.querySelector('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  if (input) {
    input.focus();
    input.value = '9:00:00';
    input.dispatchEvent(new Event('input', { bubbles: true }));
    setTimeout(() => {
      input.dispatchEvent(new KeyboardEvent('keydown', { key: 'Tab', keyCode: 9, bubbles: true }));
      console.log('✅ Pressed Tab - check timeline now');
    }, 500);
  }
}

function quickTest_EnterKey() {
  console.log('🚀 QUICK TEST: Enter key after value change');
  const input = document.querySelector('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  if (input) {
    input.focus();
    input.value = '10:00:00';
    input.dispatchEvent(new Event('input', { bubbles: true }));
    setTimeout(() => {
      input.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', keyCode: 13, bubbles: true }));
      console.log('✅ Pressed Enter - check timeline now');
    }, 500);
  }
}

// =============================================================================
// TEST 6: WINDOW/DOCUMENT FOCUS SIMULATION (Based on DevTools Observation)
// =============================================================================

function test6_WindowFocusEvents() {
  console.log('\n🧪 TEST 6: Window/Document Focus Events (DevTools Simulation)');

  const timestampInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  const visibleInputs = Array.from(timestampInputs).filter(input => input.offsetParent !== null);

  if (visibleInputs.length < 2) {
    console.log('❌ Need at least 2 visible timestamp inputs');
    return;
  }

  const startInput = visibleInputs[0];

  // Set the value first
  startInput.focus();
  startInput.value = '11:11:00';
  startInput.dispatchEvent(new Event('input', { bubbles: true }));
  console.log('✅ Value set to 11:11:00');

  const focusTests = [
    {
      name: 'Window blur/focus (simulating DevTools click)',
      action: () => {
        window.dispatchEvent(new Event('blur'));
        setTimeout(() => {
          window.dispatchEvent(new Event('focus'));
        }, 100);
      }
    },
    {
      name: 'Document visibility change',
      action: () => {
        Object.defineProperty(document, 'hidden', { value: true, configurable: true });
        document.dispatchEvent(new Event('visibilitychange'));
        setTimeout(() => {
          Object.defineProperty(document, 'hidden', { value: false, configurable: true });
          document.dispatchEvent(new Event('visibilitychange'));
        }, 100);
      }
    },
    {
      name: 'Page focus/blur events',
      action: () => {
        document.dispatchEvent(new Event('blur', { bubbles: true }));
        setTimeout(() => {
          document.dispatchEvent(new Event('focus', { bubbles: true }));
        }, 100);
      }
    },
    {
      name: 'Input loses focus to window',
      action: () => {
        startInput.blur();
        window.focus();
        setTimeout(() => {
          startInput.focus();
        }, 200);
      }
    }
  ];

  let testIndex = 0;

  function runNextFocusTest() {
    if (testIndex >= focusTests.length) {
      console.log('🏁 All window focus tests completed');
      return;
    }

    const test = focusTests[testIndex];
    console.log(`\n🪟 Focus Test ${testIndex + 1}: ${test.name}`);

    test.action();

    setTimeout(() => {
      console.log(`   Result: Check timeline brackets now`);
      testIndex++;
      setTimeout(runNextFocusTest, 3000);
    }, 1000);
  }

  runNextFocusTest();
}

// =============================================================================
// TEST 7: IFRAME AND NESTED ELEMENT FOCUS
// =============================================================================

function test7_IframeAndNestedFocus() {
  console.log('\n🧪 TEST 7: Iframe and Nested Element Focus');

  const timestampInputs = document.querySelectorAll('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  const visibleInputs = Array.from(timestampInputs).filter(input => input.offsetParent !== null);

  if (visibleInputs.length < 2) {
    console.log('❌ Need at least 2 visible timestamp inputs');
    return;
  }

  const startInput = visibleInputs[0];

  // Set the value first
  startInput.focus();
  startInput.value = '12:34:00';
  startInput.dispatchEvent(new Event('input', { bubbles: true }));
  console.log('✅ Value set to 12:34:00');

  // Look for iframes or nested focusable elements
  const iframes = document.querySelectorAll('iframe');
  const videos = document.querySelectorAll('video');
  const buttons = document.querySelectorAll('button');

  console.log(`Found ${iframes.length} iframes, ${videos.length} videos, ${buttons.length} buttons`);

  const focusTargets = [
    ...Array.from(iframes).slice(0, 2),
    ...Array.from(videos).slice(0, 2),
    ...Array.from(buttons).slice(0, 3)
  ];

  let targetIndex = 0;

  function runNextNestedFocusTest() {
    if (targetIndex >= focusTargets.length) {
      console.log('🏁 All nested focus tests completed');
      return;
    }

    const target = focusTargets[targetIndex];
    const targetType = target.tagName.toLowerCase();

    console.log(`\n🎯 Nested Focus Test ${targetIndex + 1}: ${targetType}`);

    try {
      if (target.focus) {
        target.focus();
      }
      target.dispatchEvent(new Event('focus', { bubbles: true }));

      setTimeout(() => {
        // Focus back to input
        startInput.focus();
      }, 500);

    } catch (error) {
      console.log(`   ⚠️ Error focusing ${targetType}:`, error.message);
    }

    setTimeout(() => {
      console.log(`   Result: Check timeline brackets now`);
      targetIndex++;
      setTimeout(runNextNestedFocusTest, 2000);
    }, 1000);
  }

  runNextNestedFocusTest();
}

// =============================================================================
// ENHANCED QUICK TESTS
// =============================================================================

function quickTest_DevToolsSimulation() {
  console.log('🚀 QUICK TEST: DevTools Click Simulation');
  const input = document.querySelector('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  if (input) {
    input.focus();
    input.value = '13:45:00';
    input.dispatchEvent(new Event('input', { bubbles: true }));

    setTimeout(() => {
      // Simulate what happens when you click in DevTools
      window.dispatchEvent(new Event('blur'));
      input.blur();

      setTimeout(() => {
        window.dispatchEvent(new Event('focus'));
        console.log('✅ DevTools simulation complete - check timeline now');
      }, 200);
    }, 500);
  }
}

function quickTest_MultipleEventSequence() {
  console.log('🚀 QUICK TEST: Multiple Event Sequence');
  const input = document.querySelector('input.style-scope.ytcp-media-timestamp-input[role="timer"]');
  if (input) {
    input.focus();
    input.value = '14:56:00';
    input.dispatchEvent(new Event('input', { bubbles: true }));

    setTimeout(() => {
      // Sequence: blur -> change -> click elsewhere -> focus back
      input.blur();
      input.dispatchEvent(new Event('change', { bubbles: true }));

      setTimeout(() => {
        document.body.click();

        setTimeout(() => {
          input.focus();
          console.log('✅ Multi-event sequence complete - check timeline now');
        }, 200);
      }, 200);
    }, 500);
  }
}

// =============================================================================
// USAGE INSTRUCTIONS
// =============================================================================

console.log(`
🧪 TIMELINE UPDATE DEBUG TESTS LOADED

USAGE:
1. Make sure you're in YouTube Studio editor with "New Cut" dialog open
2. Run individual tests or the master test runner:

INDIVIDUAL TESTS:
- test1_ClickEventVariations()
- test2_FocusBlurEvents()
- test3_KeyboardEvents()
- test4_CustomEvents()
- test5_DOMAndFormEvents()
- test6_WindowFocusEvents()      // NEW: DevTools simulation
- test7_IframeAndNestedFocus()   // NEW: Nested element focus

QUICK TESTS:
- quickTest_ClickBody()
- quickTest_TabKey()
- quickTest_EnterKey()
- quickTest_DevToolsSimulation()     // NEW: Simulates DevTools click
- quickTest_MultipleEventSequence() // NEW: Complex event sequence

MASTER RUNNER:
- runAllTimelineTests()  // Runs all tests in sequence

WHAT TO WATCH:
- Timeline brackets (red selection area) should move to match input values
- Note which test successfully updates the timeline position
- Look for any console errors or warnings

START WITH: quickTest_DevToolsSimulation()
`);
